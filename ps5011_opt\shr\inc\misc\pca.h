/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  pca.h                                                                 */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _PCA_H_
#define _PCA_H_

#include "conf.h"
#include "misc/types.h"



/*
 *  PCA format:
 *
 *  MSB                                     LSB
 *
 *   BLK - DIE - EXT_DIE  | PAGE - DIE_PAGE - BANK - CH - LMU - PLANE - LC
 * ---------------------------------------------------------------------------------------------
 *
 *
 *   BLK -       EXT_DIE  | PAGE -          - BANK - CH - LMU - PLANE - LC      (EXT_DIE)
 *   BLK - DIE -          | PAGE -          - BANK - CH - LMU - PLANE - LC      (DIE)
 *   BLK - DIE -          | PAGE - DIE_PAGE - BANK - CH - LMU - PLANE - LC      (DIE Interleaving)
 *
 *
 *      total phy plane = DIE * EXT_DIE * DIE_PAGE * BANK * CH * PLANE
 *
 *
 *          U8      physical_plane_cnt;         // plane_per_die (1-plane, 2-plane or 4-plane)
 *          U8      physical_die_cnt;           // die_per_ce
 *          U8      physical_ch_cnt;            // total_ch
 *          U8      total_bank;                 // ce_per_ch
 *
 *          U8      physical_planes_per_vb      // die_per_ce * (plane * ch * bank)
 *          U8      logical_planes_per_vb       // plane_per_vb
 *
 *          U8      total_logic_die;            // total_ext_die * die_per_ce
 *
 *
 *         [2016/07/11]
 *         physical_planes_per_vb = logical_planes_per_vb = plane_per_vb
 *         if ( ((BANK * CH * PLANE) < 128) && (DIE or EXT_DIE valid) ) {
 *            DIE_PAGE must valid && ((DIE_PAGE * BANK * CH * PLANE) <= 128)
 *         }
 *
 */



#define  PACK_PCA_RULE_TRIM_BIT_SHIFT   (31)



typedef struct pca_pack_rule_struct    PCA_PACK_RULE_STRUCT, *PCA_PACK_RULE_STRUCT_PTR;

struct pca_pack_rule_struct {
	U32        shift;
	U32        bit_no;
	U32        mask;
};



typedef struct pca_rule_struct         PCA_RULE_STRUCT, *PCA_RULE_STRUCT_PTR;

struct pca_rule_struct {
	PCA_PACK_RULE_STRUCT        rule_plane;
	PCA_PACK_RULE_STRUCT        rule_lmu;           // may not be 2^n
	PCA_PACK_RULE_STRUCT        rule_ch;
	PCA_PACK_RULE_STRUCT        rule_bank;
	PCA_PACK_RULE_STRUCT        rule_die_page;
	PCA_PACK_RULE_STRUCT        rule_page;          // may not be 2^n

	PCA_PACK_RULE_STRUCT        rule_ext_die;
	PCA_PACK_RULE_STRUCT        rule_die;
	PCA_PACK_RULE_STRUCT        rule_block;


	PCA_PACK_RULE_STRUCT        rule_die_index;
	PCA_PACK_RULE_STRUCT        rule_node;
	PCA_PACK_RULE_STRUCT        rule_vb;
	U32                         node_in_vb_mask;
};




/*
 * --------------------------------------------
 *  public prototypes
 * --------------------------------------------
 */

U32  pca_pack_vb_plane(PCA_RULE_STRUCT_PTR pca_rule, U32 vb, U32 vb_plane);



#define PCA_2_PLANE(pca_rule, pca)              (((pca) >> (pca_rule)->rule_plane.shift)    & (pca_rule)->rule_plane.mask)
#define PCA_2_LMU(pca_rule, pca)                (((pca) >> (pca_rule)->rule_lmu.shift)      & (pca_rule)->rule_lmu.mask)
#define PCA_2_CHANNEL(pca_rule, pca)            (((pca) >> (pca_rule)->rule_ch.shift)       & (pca_rule)->rule_ch.mask)
#define PCA_2_BANK(pca_rule, pca)               (((pca) >> (pca_rule)->rule_bank.shift)     & (pca_rule)->rule_bank.mask)
#define PCA_2_DIE_PAGE(pca_rule, pca)           (((pca) >> (pca_rule)->rule_die_page.shift) & (pca_rule)->rule_die_page.mask)
#define PCA_2_PAGE(pca_rule, pca)               (((pca) >> (pca_rule)->rule_page.shift)     & (pca_rule)->rule_page.mask)
#define PCA_2_EXT_DIE(pca_rule, pca)            (((pca) >> (pca_rule)->rule_ext_die.shift)  & (pca_rule)->rule_ext_die.mask)
#define PCA_2_DIE(pca_rule, pca)                (((pca) >> (pca_rule)->rule_die.shift)      & (pca_rule)->rule_die.mask)
#define PCA_2_BLOCK(pca_rule, pca)              (((pca) >> (pca_rule)->rule_block.shift)    & (pca_rule)->rule_block.mask)



#define PCA_2_VB(pca_rule, pca)                 (((pca) >> (pca_rule)->rule_vb.shift) & (pca_rule)->rule_vb.mask)

#define PCA_2_VB_PLANE(pca_rule, pca)                                                                                                           \
    (                                                                                                                                           \
        (PCA_2_DIE_PAGE((pca_rule), (pca))  << ((pca_rule)->rule_bank.bit_no + (pca_rule)->rule_ch.bit_no + (pca_rule)->rule_plane.bit_no)) |   \
        (PCA_2_BANK(    (pca_rule), (pca))  << ((pca_rule)->rule_ch.bit_no) + (pca_rule)->rule_plane.bit_no)) |                                 \
        (PCA_2_CHANNEL( (pca_rule), (pca))  << (pca_rule)->rule_plane.bit_no) |                                                                 \
        (PCA_2_PLANE(   (pca_rule), (pca)))                                                                                                     \
    )








#if 1

/* die + ext_die + die_page */
#define GET_PHYSICAL_DIE(pca_rule, pca)       (U8)((((pca) >> (pca_rule)->rule_die.shift) & (pca_rule)->rule_die.mask) /* die */ +  (((pca) >> (pca_rule)->rule_die_page.shift) & (pca_rule)->rule_die_page.mask) /* die page */  )
#define GET_LOGIC_DIE(pca_rule, vb)           (U8)(vb & (BIT((pca_rule)->rule_ext_die.bit_no + (pca_rule)->rule_die.bit_no) - 1))
#define GET_PLANE_AMONG_VB(pca_rule, pca)     (U16)(( ( (pca >> (pca_rule)->rule_ch.shift) & BIT_MASK((pca_rule)->rule_ch.bit_no + (pca_rule)->rule_bank.bit_no) ) << (pca_rule)->rule_plane.bit_no ) | ( (pca >> (pca_rule)->rule_plane.shift) & (pca_rule)->rule_plane.mask ))
#define GET_VB_START_PCA(addr_rule_ptr, vb)   ((vb) << (addr_rule_ptr)->rule_ext_die.shift)

#endif







#endif /* _PCA_H_ */


