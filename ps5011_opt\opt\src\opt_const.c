/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_const.c                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_CONST_C_

#include "opt_main.h"
#include "opt_const.h"

__attribute__ ((section(".signature"))) const U16 deadbeef[2] = {
	0xBEEF, 0xDEAD
};

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '3', '-',
	'T', 'S', 'B', '-',
	'1', '5', 'n', 'm', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};

#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '3', '-',
	'T', 'S', 'B', '-',
	'B', 'i', 'C', 's', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};

#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'M', 'I', 'C', 'R', 'O', 'N', '-',
	' ', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};

#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'M', 'I', 'C', 'R', 'O', 'N', '-',
	' ', 'Q', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '3', '-',
	'T', 'S', 'B', '-',
	'2', '4', 'n', 'm', 'S', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'H', 'N', 'X', '-',
	' ', 'V', '6', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'D', '-',
	' ', 'B', '5', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'D', '-',
	' ', 'B', '6', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)//zerio bics6 qlc add
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'D', '-',
	' ', 'B', '6', '3', 'D', 'Q', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'D', '-',
	' ', 'B', '8', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'Y', 'M', 'T', '-',
	' ', 'A', 'S', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'Y', 'M', 'T', '-',
	'E', 'M', 'S', '3', 'D', 'Q', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'H', 'N', 'X', '-',
	' ', 'V', '7', '3', 'D', 'Q', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#if (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'M', '-',
	' ', 'V', '6', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC)//Samsung v7 mst add--Reip
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'M', '-',
	' ', 'V', '7', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v8 mst add--Reip
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'M', '-',
	' ', 'V', '8', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#elif (OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'S', 'A', 'M', '-',
	' ', 'V', '5', '3', 'D', 'T', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#endif
#elif (OPT_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
__attribute__ ((section(".signature"))) const U8 version[0x20 - 4] = {
	'P', 'S', '5', '0', '1', '7', '-',
	'I', 'N', 'T', 'N',
	'3', '8', 'A', '3', 'D', 'Q', 'L', 'C',
	CONTROLLER_VERSION_MAJOR, CONTROLLER_VERSION_MINOR, FW_MODE, CUSTOMER_CODE, FW_ATTRIBUTE,
	VERSION_MAJOR, VERSION_MINOR, VERSION_TAG, SUB_CODE_VERSION_1, SUB_CODE_VERSION_2
};
#else
#error "CONFIG_FLASH_TYPE"
#endif

__attribute__ ((section(".signature"))) const char g_time[] = {
	__TIME__
};

__attribute__ ((section(".signature"))) const char g_date[] = {
	__DATE__
};

#if (MicronFlashID4 == IM_N28A_ID4)
const U16 guwValleyCheckCmd[2] = {
	FPU_OFFSET(gFpuEntryList.fpu_N28_set_feature_valley_check_En),
	FPU_OFFSET(gFpuEntryList.fpu_N28_valley_check_4P_program_11_10)
};

const U16 guwWordLineBypass[3][3] = {
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_setfeature_ddr_wlbypass_disable_00),
		FPU_OFFSET(gFpuEntryList.fpu_entry_setfeature_ddr_wlbypass_enable_01),
		FPU_OFFSET(gFpuEntryList.fpu_entry_setfeature_ddr_wlbypass_enable_09),
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_N28_setfeature_ddr_disable_preread_02_with_wlbypass_disable_00),
		FPU_OFFSET(gFpuEntryList.fpu_N28_setfeature_ddr_disable_preread_02_with_wlbypass_enable_01),
		FPU_OFFSET(gFpuEntryList.fpu_N28_setfeature_ddr_disable_preread_02_with_wlbypass_enable_09),
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_N28_setfeature_ddr_disable_preread_06_with_wlbypass_disable_00),
		FPU_OFFSET(gFpuEntryList.fpu_N28_setfeature_ddr_disable_preread_06_with_wlbypass_enable_01),
		FPU_OFFSET(gFpuEntryList.fpu_N28_setfeature_ddr_disable_preread_06_with_wlbypass_enable_09),
	},
};
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
//------------------------------------------------------ Config Read Operation --------------------------------------
#if (CONFIG_FLASH_TYPE==FLASH_TYPE_HYNIX_3D_TLC || CONFIG_FLASH_TYPE==FLASH_TYPE_BICS5_3D_TLC || CONFIG_FLASH_TYPE==FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE==FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};
//(HYNIX_V6_TLC)
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_lower_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_lower_pg_read)  
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_middle_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_middle_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_upper_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_upper_pg_read)
		}
	}
};
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)//zerio bics6 qlc add
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
		}
};
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper/top, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_lower_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_lower_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_middle_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_middle_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_upper_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_upper_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_4p_32_30_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_1p_30_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_2p_32_30_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_3p_32_30_top_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_4p_32_31_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_1p_31_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_2p_32_31_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_3p_32_31_top_pg_read)
		}
	}
};
#elif (CONFIG_FLASH_TYPE==FLASH_TYPE_YMTC_3D_TLC)
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};
//(HYNIX_V6_TLC)
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)  
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)  
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)  
		}
	}
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};

const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { // normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	}
};
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V7_QLC)	//Reip Porting 3D-V7 QLC Add
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};

const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper/top, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_lower_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_lower_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_lower_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_middle_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_middle_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_middle_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_upper_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_upper_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_upper_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_4p_32_30_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_1p_30_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_2p_32_30_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_3p_32_30_top_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_4p_32_31_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_1p_31_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_2p_32_31_top_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_qlc_3p_32_31_top_pg_read)
		}
	}
};
#elif (CONFIG_FLASH_TYPE==FLASH_TYPE_MICRON_3D_TLC)
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};
//(HYNIX_V6_TLC)
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	}
};
#elif (CONFIG_FLASH_TYPE==FLASH_TYPE_MICRON_3D_QLC)
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_read)
		}
	}
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};
//(HYNIX_V6_TLC)
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	}
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
const U16 gwFPU_slc_read[2][4] = {    //  normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_30_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_30_read)
	},
	{
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_4p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_2p_32_31_read),
		FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3p_32_31_read)
	}
};
const U16 gwFPU_xlc_read[FSP_PAGE_CNT][2][4] = { //  low/mid/upper/top, normal/cache, all-plane/single-plane/2-plane/3-plane
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	},
	{
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_30_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_30_pg_read)
		},
		{
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_4p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_2p_32_31_pg_read),
			FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_3p_32_31_pg_read)
		}
	}
};
#else
#error "CONFIG_FLASH_TYPE(Config Read Operation)"
#endif

//------------------------------------------------------ Config Program Operation --------------------------------------
#if (OPT_CATEGORY_FLASH == FLASH_HYNIX_V7_TLC || OPT_CATEGORY_FLASH == FLASH_HYNIX_V8_TLC || \
	 OPT_CATEGORY_FLASH == FLASH_HYNIX_V9_TLC)
/*
*	single plane: 01 80 22 / 02 80 22 / 03 80 23
*	multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 22  
*				  02 80 11,  02 81 11, 02 81 11, 02 81 22  
*				  03 80 11,  03 81 11, 03 81 11, 03 81 23  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_22),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_22),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_23),
};
			
const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_22_gc),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_22_gc),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_23_gc),
};


const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_11),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_22)		
	},
	{ //M
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_22)		
	},
	{ //U
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_23)		
	}	
};
	
const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_11_gc), 		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_22_gc)	
	},
	{ //M
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_22_gc)	
	},
	{ //U
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_23_gc)	
	}	
};
#elif (OPT_CATEGORY_FLASH==FLASH_SANDISK_BICS5_TLC)
/*
*	single plane: 01 80 1A / 02 80 1A / 03 80 10
*	multi  plane: 01 80 11,  01 80 1A  
*				  02 80 11,  02 80 1A
*				  03 80 11,  03 80 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_10),
};
			
const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A_gc),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A_gc),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_10_gc),
};


const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11),			
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A)		
	},
	{ //M
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A)		
	},
	{ //U
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_10)		
	}	
};
	
const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11_gc), 		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A_gc)	
	},
	{ //M
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A_gc)	
	},
	{ //U
		
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_10_gc)	
	}	
};
#elif (OPT_CATEGORY_FLASH==FLASH_SANDISK_BICS6_TLC)
/*
*	single plane: 01 80 1A / 02 80 1A / 03 80 10
*	multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 1A  
*				  02 80 11,  02 81 11, 02 81 11, 02 81 1A  
*				  03 80 11,  03 81 11, 03 81 11, 03 81 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A_gc),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A_gc),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11),			
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A)		
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A)		
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_10)		
	}	
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11_gc), 		
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A_gc)	
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A_gc)	
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_10_gc)	
	}	
};

#elif (OPT_CATEGORY_FLASH==FLASH_SANDISK_BICS6_QLC)//zerio bics6 qlc add
/*
*	single plane: 01 80 1A / 02 80 1A / 03 80 1A / 04 80 10
*	multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 1A  
*				  02 80 11,  02 81 11, 02 81 11, 02 81 1A  
*				  03 80 11,  03 81 11, 03 81 11, 03 81 1A
*				  04 80 11,  04 81 11, 04 81 11, 04 81 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_1A),
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_80_program_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A_gc),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A_gc),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_1A_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_80_program_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U/T, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11),			
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A)		
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A)		
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_1A)
	},	
	{ //T

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_81_program_10)		
	}	
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11_gc),			
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A_gc)		
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A_gc)		
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_1A_gc)
	},	
	{ //T

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_USB_81_program_10_gc)		
	}	
};

#elif (OPT_CATEGORY_FLASH==FLASH_SANDISK_BICS8_TLC)
/*
*	single plane: 01 80 1A / 02 80 1A / 03 80 10
*	multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 1A
*				  02 80 11,  02 81 11, 02 81 11, 02 81 1A
*				  03 80 11,  03 81 11, 03 81 11, 03 81 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A),
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A),
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_1A_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_1A_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_10)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_LSB_81_program_1A_gc)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_CSB_81_program_1A_gc)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_FSP_MSB_81_program_10_gc)
	}
};

#elif (OPT_CATEGORY_FLASH==FLASH_YMTC_TAS_TLC)
/*
*	single plane: 01 80 1A / 02 80 1A / 03 80 10
*	multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 1A  
*				  02 80 11,  02 81 11, 02 81 11, 02 81 1A  
*				  03 80 11,  03 81 11, 03 81 11, 03 81 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc),			
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),			
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A)		
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A)		
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10)		
	}	
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc), 		
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc)	
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc), 		
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc), 		
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10_gc)
	}	
};
#elif (OPT_CATEGORY_FLASH==FLASH_YMTC_WTS_TLC)//zerio wts add
/*
*	single plane: 01 80 1A / 02 80 1A / 03 80 10
*	multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 1A
*				  02 80 11,  02 81 11, 02 81 11, 02 81 1A
*				  03 80 11,  03 81 11, 03 81 11, 03 81 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A),
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A),
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_1A_gc)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_prog_80_10_gc)
	}
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
/*
*	single plane: 80 1A / 80 1A / 80 1A /  80 10
*	multi  plane: 80 11,  80 11, 80 11, 80 1A   (LP)
*				  80 11,  80 11, 80 11, 80 1A   (MP)
*				  80 11,  80 11, 80 11, 80 1A   (UP)
*				  80 11,  80 11, 80 11, 80 10   (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// Other/last page, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A)
	},
	{ //T

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_10)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// Other/last page, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A_gc)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A_gc)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_1A_gc)
	},
	{ //T

		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_FSP_tlc_80_program_10_gc)
	},
};
#elif (OPT_CATEGORY_FLASH == FLASH_HYNIX_V7_QLC)	//Reip Porting 3D-V7 QLC Add
/*
*	single plane: (0D)01 80 22 / (0D)02 80 22 / (0D)03 80 22 / (0D)04 80 23
*	multi  plane: (0D)01 80 11,  (0D)01 81 11,  (0D)01 81 11,  (0D)01 81 22
*				  (0D)02 80 11,  (0D)02 81 11,  (0D)02 81 11,  (0D)02 81 22
*				  (0D)03 80 11,  (0D)03 81 11,  (0D)03 81 11,  (0D)03 81 22
*				  (0D)04 80 11,  (0D)04 81 11,  (0D)04 81 11,  (0D)04 81 23  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {//FIRST_PROGRAM/SECOND_PROGRAM
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_22),
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_22),
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_22),
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_80_program_23),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {//FIRST_PROGRAM/SECOND_PROGRAM
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_22_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_22_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_22_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_80_program_23_gc),
};


const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {//FIRST_PROGRAM/SECOND_PROGRAM L/M/U/T, FIRST/OTHER/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_22)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_22)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_22)
	},
	{ //T

		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_80_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_81_program_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_81_program_23)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {//FIRST_PROGRAM/SECOND_PROGRAM	// L/M/U, FIRST/OTHER/LAST
	{ //L

			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_LSB_81_program_22_gc)
	},
	{ //M

			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_CSB_81_program_22_gc)
	},
	{ //U

			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_MSB_81_program_22_gc)
	},
	{ //T

			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_80_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_81_program_11_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_FSP_USB_81_program_23_gc)
	}
};
#elif (OPT_CATEGORY_FLASH==FLASH_B47R_TLC || OPT_CATEGORY_FLASH==FLASH_B37R_TLC)
/*
*	single plane: 80 10 / 80 10 / 80 10
*	multi  plane: 80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U, FIRST/MID/LAST
	{ //L

		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	},
	{ //M

		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	},
	{ //U

		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	}
};
#elif (OPT_CATEGORY_FLASH==FLASH_N48R_QLC)
/*
*	single plane: 80 10 / 80 10 / 80 10 / 80 10
*	multi  plane: 80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc),
	FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc),
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {
	{//L
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	},
	{//M
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	},
	{//U
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	},
	{//T
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U/T, FIRST/MID/LAST/TOP
	{//L
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	},
	{//M
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	},
	{//U
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	},
	{//T
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)
	}
};
#elif (OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V6_TLC || OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V6P_TLC || OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V5_TLC)
/*
*	single plane: 80 C0 11/21,	 80 C0 12/22	 80 C0 13/23	8B 10
*	multi  plane: 80 11,  81 C0 31
*				  80 11,  81 C0 32
*				  80 11,  81 C0	33
*				  8B 11,  8B 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT+2][NAND_MAX_PLANE] = {
	{//LSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p1)
	},
	{//CSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p1)
	},
	{//MSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p1)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal)
	},
	{//MLC page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT+2][NAND_MAX_PLANE] = {
	{//LSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p1_gc)
	},
	{//CSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p1_gc)
	},
	{//MSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p1_gc)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc)
	},
	{//MLC page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT+2][NAND_MAX_PLANE+1] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_LSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_LSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_LSB_p1)
	},
	{ //M
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_CSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_CSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_CSB_p1)
	},
	{ //U
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_MSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_MSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_MSB_p1)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal)
	},
	{//MLC page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT+2][NAND_MAX_PLANE+1] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_LSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_LSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_LSB_p1_gc)
	},
	{ //M
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_CSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_CSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_CSB_p1_gc)
	},
	{ //U
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_MSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_MSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_MSB_p1_gc)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc)
	},
	{//MLC page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};
#elif (OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V7_TLC || OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V8_TLC)//Samsung v7/v8 mst add--Reip
/*
*	single plane: 80 C0 11/21,	 80 C0 12/22	 80 C0 13/23	8B 10
*	multi  plane: 80 11,  81 C0 31
*				  80 11,  81 C0 32
*				  80 11,  81 C0	33
*				  8B 11,  8B 10  (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT+2][NAND_MAX_PLANE] = {
	{//LSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p1),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p2),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p3)
	},
	{//CSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p1),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p2),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p3)
	},
	{//MSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p1),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p2),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p3)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal)
	},
	{//LSB/CSB dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT+2][NAND_MAX_PLANE] = {
	{//LSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p1_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p2_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_LSB_p3_gc)
	},
	{//CSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p1_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p2_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_CSB_p3_gc)
	},
	{//MSB
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p0_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p1_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p2_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_single_plane_MSB_p3_gc)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc)
	},
	{//LSB/CSB page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT+2][NAND_MAX_PLANE+1] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_81_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2_LSB_81_C0_31),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p2p3_LSB_81_C0_C1),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2p3p4_LSB_81_C0_F1)
	},
	{ //M
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_81_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2_CSB_81_C0_32),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p2p3_CSB_81_C0_C2),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2p3p4_CSB_81_C0_F2)
	},
	{ //U
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_80_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_81_11),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2_MSB_81_C0_33),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p2p3_MSB_81_C0_C3),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2p3p4_MSB_81_C0_F3)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal)
	},
	{//MLC page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT+2][NAND_MAX_PLANE+1] = {	// L/M/U, FIRST/MID/LAST
	{ //L
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_81_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2_LSB_81_C0_31_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p2p3_LSB_81_C0_C1_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2p3p4_LSB_81_C0_F1_gc)
	},
	{ //M
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_81_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2_CSB_81_C0_32_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p2p3_CSB_81_C0_C2_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2p3p4_CSB_81_C0_F2_gc)
	},
	{ //U
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_80_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_multi_plane_81_11_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2_MSB_81_C0_33_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p2p3_MSB_81_C0_C3_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_Tlc_p1p2p3p4_MSB_81_C0_F3_gc)
	},
	{//confirm
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_normal_plane_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc),
		FPU_OFFSET(gFpuEntryList.fpu_entry_prog_confirm_Tlc_last_plane_normal_gc)
	},
	{//MLC page dummy
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop),
		FPU_OFFSET(gFpuEntryList.fpu_entry_nop)
	},
};

#elif (OPT_CATEGORY_FLASH==FLASH_INTEL_N38A_QLC)
/*
*	single plane: 80 10 / 80 10 / 80 10 / 80 10
*	multi  plane: 80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 11
*				  80 11,  80 11, 80 11, 80 10
*				  80 11,  80 11, 80 11, 80 11    (last plane in Multi plane case)
*/
const U16 gwFPU_xlc_1P_prog[FSP_PAGE_CNT] = {
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10),
};

const U16 gwFPU_xlc_1P_prog_gc[FSP_PAGE_CNT] = {
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10_gc),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_single_plane_80_10_gc),
};


const U16 gwFPU_xlc_mP_prog[FSP_PAGE_CNT][3] = {	// L/M/U/T, FIRST/MID/LAST
	{ //L

			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10)
	},
	{ //M

			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10)
	},
	{ //U

			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10)
	},
	{ //T

			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11),
			FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10)
	}
};

const U16 gwFPU_xlc_mP_prog_gc[FSP_PAGE_CNT][3] = {	// L/M/U/T, FIRST/MID/LAST
		{ //L

				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10_gc)
		},
		{ //M

				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10_gc)
		},
		{ //U

				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10_gc)
		},
		{ //T

				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_first_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_other_80_11_gc),
				FPU_OFFSET(gFpuEntryList.fpu_entry_prog_qlc_multi_plane_last_80_10_gc)
		}
};
#else
#error "CONFIG_FLASH_TYPE(Config Program Operation)"
#endif

const U16 guwFPUEndCacheRead[2] = {
	FPU_OFFSET(gFpuEntryList.fpu_entry_3F_read),
	FPU_OFFSET(gFpuEntryList.fpu_entry_slc_3F_read)
};

const U16 guwFPUMicronSnapRead[2] = {
#if (PS5021_EN || S17_EN)
	FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_1p_20_read),
	FPU_OFFSET(gFpuEntryList.fpu_entry_slc_1p_20_read),
#endif /* (PS5021_EN || S17_EN) */
};


/*
 * for testing if page read split to 2 slices by using page_vld as index
 */
const U8 bit_vld_2_bit_cnt[16] = {
	0,  // 0000b
	1,  // 0001b
	1,  // 0010b
	2,  // 0011b
	1,  // 0100b
	2,  // 0101b
	2,  // 0110b
	3,  // 0111b
	1,  // 1000b
	2,  // 1001b
	2,  // 1010b
	3,  // 1011b
	2,  // 1100b
	3,  // 1101b
	3,  // 1110b
	4   // 1111b
};

const U8 select_group_id[16] = {	//Reip
	4,  // 0000b
	4,  // 0001b
	4,  // 0010b
	2,  // 0011b
	4,  // 0100b
	4,  // 0101b
	4,  // 0110b
	4,  // 0111b
	4,  // 1000b
	4,  // 1001b
	4,  // 1010b
	4,  // 1011b
	3,  // 1100b
	4,  // 1101b
	4,  // 1110b
	4   // 1111b
};

/*
 * for testing if page read split to 2 slices by using page_vld as index
 */
const U8 page_read_split[16] = {
	0,  // 0000b           // 0-0
	0,  // 0001b           // 1-0
	0,  // 0010b           // 2-0
	0,  // 0011b           // 3-0
	0,  // 0100b           // 4-0
	1,  // 0101b //01"0"1b // 1-4
	0,  // 0110b           // 6-0
	0,  // 0111b           // 7-0
	0,  // 1000b           // 8-0
	1,  // 1001b //1"00"1b // 1-8
	1,  // 1010b //1"0"10b // 2-8
	1,  // 1011b //1"0"11b // 3-8
	0,  // 1100b           // C-0
	1,  // 1101b //11"0"1b // 1-C
	0,  // 1110b           // E-0
	0   // 1111b           // F-0
};

const U8 gubMicronSnapReadBMP[16] = {
	0,  // 0000b           // 0-0
	1,  // 0001b           // 1-0
	1,  // 0010b           // 2-0
	1,  // 0011b           // 3-0
	2,  // 0100b           // 4-0
	0,  // 0101b //01"0"1b // 1-4
	2,  // 0110b           // 6-0
	0,  // 0111b           // 7-0
	3,  // 1000b           // 8-0
	0,  // 1001b //1"00"1b // 1-8
	0,  // 1010b //1"0"10b // 2-8
	0,  // 1011b //1"0"11b // 3-8
	3,  // 1100b           // C-0
	0,  // 1101b //11"0"1b // 1-C
	0,  // 1110b           // E-0
	0   // 1111b           // F-0
};

#if(NEW_IWL_EN)
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
//N48R
const U8 gubMicronIWLGroupBMP[16] = {
	0,  // 0000b           // 0-0
	1,  // 0001b           // 1-0
	2,  // 0010b           // 2-0
	3,  // 0011b           // 3-0
	4,  // 0100b           // 4-0
	5,  // 0101b //01"0"1b // 1-4
	6,  // 0110b           // 6-0
	7,  // 0111b           // 7-0
	8,  // 1000b           // 8-0
	9,  // 1001b //1"00"1b // 1-8
	10,  // 1010b //1"0"10b // 2-8
	11,  // 1011b //1"0"11b // 3-8
	12,  // 1100b           // C-0
	13,  // 1101b //11"0"1b // 1-C
	14,  // 1110b           // E-0
	15   // 1111b           // F-0
};
#else //(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
//B47R
const U8 gubMicronIWLGroupBMP[16] = {
	0,  // 0000b           // 0-0
	1,  // 0001b           // 1-0
	1,  // 0010b           // 2-0
	0,  // 0011b           // 3-0
	2,  // 0100b           // 4-0
	3,  // 0101b //01"0"1b // 1-4
	3,  // 0110b           // 6-0
	0,  // 0111b           // 7-0
	2,  // 1000b           // 8-0
	3,  // 1001b //1"00"1b // 1-8
	3,  // 1010b //1"0"10b // 2-8
	0,  // 1011b //1"0"11b // 3-8
	0,  // 1100b           // C-0
	0,  // 1101b //11"0"1b // 1-C
	0,  // 1110b           // E-0
	0   // 1111b           // F-0
};
#endif //(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
#else /* (NEW_IWL_EN) */
//B47R
const U8 gubMicronIWLGroupBMP[16] = {
	0,  // 0000b           // 0-0
	1,  // 0001b           // 1-0
	1,  // 0010b           // 2-0
	0,  // 0011b           // 3-0
	2,  // 0100b           // 4-0
	0,  // 0101b //01"0"1b // 1-4
	0,  // 0110b           // 6-0
	0,  // 0111b           // 7-0
	2,  // 1000b           // 8-0
	0,  // 1001b //1"00"1b // 1-8
	0,  // 1010b //1"0"10b // 2-8
	0,  // 1011b //1"0"11b // 3-8
	0,  // 1100b           // C-0
	0,  // 1101b //11"0"1b // 1-C
	0,  // 1110b           // E-0
	0   // 1111b           // F-0
};
#endif /* (NEW_IWL_EN) */

/*
 * for testing if a one-pass prog (or two pass prog in TLC case, or full sequence prog in TSB case) job
 * in a 4-plane flash need to be split to 2 2-plane/1-plane prog by using plane_vld as index
 */
const U8 multi_plane_prog_split[16] = {
	0,  // 0000b            // 0-0
	0,  // 0001b            // 1-0
	0,  // 0010b            // 1-0
	0,  // 0011b            // 2-0
	0,  // 0100b            // 1-0
	1,  // 0101b // 01"0"1b // 1-1
	0,  // 0110b            // 2-0
	1,  // 0111b // 0"111"b // 2-1
	0,  // 1000b            // 1-0
	1,  // 1001b // 1"00"1b // 1-1
	1,  // 1010b // 1"0"10b // 1-1
	1,  // 1011b // 1"0"11b // 2-1
	0,  // 1100b            // 2-0
	1,  // 1101b // 11"0"1b // 1-2
	1,  // 1110b // 111"0"b  //1-2
	0   // 1111b             //4-0
};


const U8 gather_status_to_parser_state[][2][2][2] = { // gather_status, other/COP0_JOB_CMD_READ, 1st/next macro, 1st/next plane
	{
		// OPT_NEW_GATHER_STATUS_INIT
		{
			// prog or erase or others
			{
				// 1st macro
				OPT_PARSER_STATE_IDLE,          // 1st plane
				OPT_PARSER_STATE_IDLE           // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_IDLE,          // 1st plane
				OPT_PARSER_STATE_IDLE           // next plane
			}
		},
		{
			// COP0_JOB_CMD_READ
			{
				// 1st macro
				OPT_PARSER_STATE_IDLE,          // 1st plane
				OPT_PARSER_STATE_IDLE           // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_IDLE,          // 1st plane
				OPT_PARSER_STATE_IDLE           // next plane
			}
		},
	},
	{
		// OPT_NEW_GATHER_STATUS_SUCCESS
		{
			// prog or erase or others
			{
				// 1st macro
				OPT_PARSER_STATE_RUN_MACRO,     // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_RUN_MACRO,     // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			}
		},
		{
			// COP0_JOB_CMD_READ
			{
				// 1st macro
				OPT_PARSER_STATE_RUN_MACRO,     // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_RUN_MACRO,     // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			}
		},
	},
	{
		// OPT_NEW_GATHER_STATUS_FAIL
		{
			// prog or erase or others
			{
				// 1st macro
				OPT_PARSER_STATE_UNDEFINE,      // 1st plane
				OPT_PARSER_STATE_UNDEFINE       // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_CLOSE_MACRO,   // 1st plane
				OPT_PARSER_STATE_CLOSE_MACRO       // next plane
			}
		},
		{
			// COP0_JOB_CMD_READ
			{
				// 1st macro
				OPT_PARSER_STATE_UNDEFINE,      // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_CLOSE_MACRO,   // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			}
		},
	},
	{
		// OPT_NEW_GATHER_STATUS_TIME_OUT
		{
			// prog or erase or others
			{
				// 1st macro
				OPT_PARSER_STATE_IDLE,          // 1st plane
				OPT_PARSER_STATE_IDLE           // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_CLOSE_MACRO,   // 1st plane
				OPT_PARSER_STATE_CLOSE_MACRO    // next plane
			}
		},
		{
			// COP0_JOB_CMD_READ
			{
				// 1st macro
				OPT_PARSER_STATE_UNDEFINE,      // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			},
			{
				// next macro
				OPT_PARSER_STATE_CLOSE_MACRO,   // 1st plane
				OPT_PARSER_STATE_RUN_MACRO      // next plane
			}
		},
	}
};
