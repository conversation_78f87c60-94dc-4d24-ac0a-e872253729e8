USER_SECTIONS .signature

OPTS_RAM 0x00000000 0x00006FC0 ; addr base 0x00000, size 28KB - 0x40 Bytes
{
	EXEC_INSTRN 0x00000000
	{
		*(+RO)	;
	}
	EXEC_DATA 0x00082A00 0x00002000
	{
	    	LOADADDR __data_lmastart
        	ADDR __data_start 
		*(+RW, +ZI)
	}
	EXEC_RAM_1 0x00086B50 0x400
	{
		STACK = 0x00086F50
	} 
}

;OPTD_D_FIXED_DCCM_ARBITER_BUG 0x000094DC
;{
;	FIXED_DCCM_ARBITER_BUG_REGION 0x000094DC 0x4
;	{
;		* KEEP(.fixed_dccm_arbiter_bug);
;	}
;}

OPTS_DEBUG_INFO 0x00006FC0
{
	DEBUG_REGION 0x00006FC0 0x40
	{
		* KEEP(.signature);
;		* KEEP(.signature LMALIGN (0x40) );
	}
}


; 0x08000~0x09FFF direct map to opt_d_ram, reserve upper part for MT, trigdata and FSAremap;  MT_NUM (max : 16) * (64B + 16B + 32B) =  1792 = 0x700.
;OPTD_RAM 0x00003000 0x00001500	
;{
	;EXEC_DATA 0x00008000
	;{
		;*(+RW, +ZI)	;
		;STACK = 0x000094F0	;
	;}
;}

FLH_IRAM 0x48000000 0x00002000	; fpu sequence
{
	FPU_SEQUENCE 0x48000000 0x1C00
	{
		*(.flh_iram_fpu.data)	;
	}
}
