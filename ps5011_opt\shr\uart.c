#include "uart.h"

#if OPT_PS8311_ONLY

#include "shr_types.h"

void uart_init(U32 baudrate)
{
	U32 temp;
	// ???
	//UART_REG[UART0_ID0] = 0x01;
	//UART_REG[UART0_ID1] = 0xFE;
	bvci_write ((UART_REG + UART0_ID0), 0x1, 0x1);
	bvci_write ((UART_REG + UART0_ID1), 0xFE, 0x1);

	// disable tx/rx int
	//UART_REG[UART0_STATUS] &= (U8)(~(SET_EN_TX_INT | SET_EN_RX_INT));
	temp = bvci_read ((UART_REG + UART0_STATUS));
	temp &= (U8)(~(SET_EN_TX_INT | SET_EN_RX_INT));
	bvci_write ((UART_REG + UART0_STATUS), (U8)(baudrate), 0x1);

	// set baud rate
	//UART_REG[UART0_BAUD_L] = (U8)(baudrate);
	//UART_REG[UART0_BAUD_H] = (U8)(baudrate >> 8);
	bvci_write ((UART_REG + UART0_BAUD_L), (U8)(baudrate), 0x1);
	bvci_write ((UART_REG + UART0_BAUD_H), (U8)(baudrate >> 8), 0x1);
}

#endif