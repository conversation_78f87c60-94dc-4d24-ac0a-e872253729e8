/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_copyback.h                                                        */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_COPYBACK_H_
#define _OPT_COPYBACK_H_

#include "misc/types.h"
#include "opt_main.h"
#include "opt_arch.h"



#ifdef _OPT_COPYBACK_C_
#define EXTERN
#else
#define EXTERN extern
#endif


typedef struct cop0_copy_back_struct   COP0_COPY_BACK_STRUCT, *COP0_COPY_BACK_STRUCT_PTR;

struct cop0_copy_back_struct {
	U32 s_pca[3];
	U32 t_pca;
};

#if OPT_SUPPORT_INTERNAL_COPYBACK
EXTERN BOOL opt_macro_cmd_tlc_copyback(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);



EXTERN U32 gdwFPU_tlc_first_lower_85_11_1a_prog, gdwFPU_tlc_first_middle_85_11_1a_prog, gdwFPU_tlc_first_upper_85_11_10_prog;
EXTERN U32 gdwFPU_tlc_foggy_lower_85_11_1a_prog, gdwFPU_tlc_foggy_middle_85_11_1a_prog, gdwFPU_tlc_foggy_upper_85_11_10_prog;
EXTERN U32 gdwFPU_tlc_fine_lower_85_11_1a_prog,  gdwFPU_tlc_fine_middle_85_11_1a_prog,  gdwFPU_tlc_fine_upper_85_11_10_prog;
EXTERN U16 gProg_trig_cmd_lmu_copyback_fpu[3][3]; // first,foggy,fine and lmu
#endif


#undef EXTERN
#endif /* _OPT_COPYBACK_H_ */
