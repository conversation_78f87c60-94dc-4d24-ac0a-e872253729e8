/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  S17_fpu.h                                                             */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _S17_FPU_H_
#define _S17_FPU_H_

#include "misc/types.h"
#include "conf.h"

#if (S17_EN && USE_S17_FPU)

#ifdef WIN32
#define FLH_FPU_SECTION
#else
#ifdef __XTENSA__
#define FLH_FPU_SECTION __attribute__ ((section("FIP_FPU_Content_SECTION")))
#else
#define FLH_FPU_SECTION __attribute__ ((section(".flh_iram_fpu.data")))
#endif
#endif

#define OPT_SUPPORT_ENTRY_MASK            (1)

#define FPU_IRAM_OFF                        (0)
#define FPU_OFFSET(fpu)                     ((U16)((U32)fpu - (U32)&gFpuEntryList) + FPU_IRAM_OFF)


// -----------------------------------------------------------------
// FPU comamnds
// Polling for Ready:
//      Ready : (DAT & DAT_H) != DAT_L
//      Busy  : Others
// Polling for Error:
//      Fail  : (DAT & DAT_H) != DAT_L
//      Pass  : Others
// -----------------------------------------------------------------
#define FPU_NOP                 (0x0000)

#define FPU_DLY(x)              (0x1000 | (x))

#define FPU_CMD(x)              (0x2000 | (x))
#define FPU_CMD_COND(x)         (0x2000 | BIT10 | (x))
#define FPU_CMD_DQS(x)          (0x2300 | (x))
#define FPU_CMD_DIE_MODE        (0x2800)

#define FPU_ADR(x)              (0x3000 | ((x) << 8))
#define FPU_ADR_1B(x)           (0x3100 | (x))
#define FPU_ADR_DIE_MODE        (0x3900)      // die bit is valid when send one address

#define FPU_DAT_R_MASK(x)       (0x4000 | (x))
#define FPU_DAT_W(x)            (0x4100 | (x))
#define FPU_DAT_R_MASK_EXT(x)   (0x4200 | (x))  // ext
#define FPU_DAT_W_FIR(x)        (0x4500 | (x))
#define FPU_DAT_W_FIR_D(x)        (0x4700 | x)  //DDR mode
#define FPU_DAT_W_MID_D(x)        (0x4300 | x)  //DDR mode
#define FPU_DAT_W_LAST(x)       (0x4900 | (x))
#define FPU_DAT_W_LAST_D(x)       (0x4B00 | x)  //DDR mode
#define FPU_DAT_W_1B(x)         (0x4D00 | (x))
#define FPU_DAT_R_CMP(x)        (0x4E00 | (x))  // ext

#define FPU_POL_RBY             (0x5000)
#define FPU_POL_MASK(x)         (0x5100 | (x))

#define FPU_DMA_R               (0x6000)

#define FPU_DMA_READ_SHIFT_OFFSET	(0xA)

#define FPU_DMA_W               (0x6100)
#define FPU_DMA_W_DMY           (0x6300)
#define FPU_DMA_W_RAW           (0x6500)

#define FPU_ADR_GEN             (0x7000)

#define FPU_BCC_TRIG            (0x8000)

#define FPU_RTY_DAT(x)          (0x9000 | (x))

#define FPU_DMA_R_RAW(x)        (0xA000 | (x))
#define     FPU_RAW_1ST_FRM         (0)
#define     FPU_RAW_2ND_FRM         (BIT8)
#define     FPU_RAW_LOGIC_XOR       (0)
#define     FPU_RAW_LOGIC_XNOR      (BIT6)
#define     FPU_RAW_LOGIC_NOP       (BIT7)
#define     FPU_RAW_SRC_IBUF(X)     ((((U16)(X)) & 0x07) << 3)
#define     FPU_RAW_DST_IBUF(X)     ((((U16)(X)) & 0x07) << 0)

#define FPU_DMA_R_COR(x)        (0xB000 | ((x) << 1))
#define 	COR_IBF_PTR(x)          (((x) & 0x7) << 1)

#define FPU_SBC(x)              (0xC000 | (x))
#define     FPU_SBC_SB_IBUF(x)      ((x) & 0x07)
#define     FPU_SBC_HB_IBUF(x)      (((x) & 0x07) << 3)
#define     FPU_SBC_DSP_EN          (BIT6)
#define     FPU_SBC_SB_FLOW         (BIT7)
#define     FPU_SBC_2K_SEL          (BIT8)
#define     FPU_SBC_DSP_4K_EN       (BIT9)

#define FPU_DUMP_IBUF(x)        (0xD000 | (x))
#define FPU_BR(x)               (0xD000 | (x))

#define FPU_END                 (0xF000)

/*
 * auto poll seq idx
 */
// non-die-interleave
#define POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40                   (0) // cache ready
#define POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20                   (1) // true ready
// die-interleave
#define POL_SEQ_FPU_ENTRY_READ_STATUS_F1_BUSY_40                (2)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_F2_BUSY_40                (3)
// PFA
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PREV_TLC_MODE_02   (4) // cache ready
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CURR_TLC_MODE_01   (5) // true ready 
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PREV_SLC_MODE_08    (6) // cache ready, pSLC 
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CURR_SLC_MODE_04   (7) // true ready, pSLC
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_80_00					(8)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_00					(9)

#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_INTERMEDIATE_MULTIPLANE_PROGRAM			(13) // For multi-plane program poll true ready
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_TLC_MODE_05	(14)


/*
 *  Version Control  (32 bits)
 *
 *  Andes checks "FPU_VER_H" and "FPU_VER_L"
 *
 */

//  flash type
// control from conf.h // 8 bits

//  FPU version  8 bits
#define FPU_VERSION    (0x00)

//  function definition // 16 bits
#define FPU_SUPPORT_BANKING         (BIT0)

#define FPU_SUPPORT_DIE_INTERLEAVE  (BIT1)
#define FPU_SUPPORT_CACHE_CMD       (BIT2)


#define FPU_SUPPORT_FUNCTION (FPU_SUPPORT_BANKING | \
                              FPU_SUPPORT_DIE_INTERLEAVE| \
                              FPU_SUPPORT_CACHE_CMD)

#define FPU_VER_H (((CONFIG_FLASH_TYPE) << 8 ) | FPU_VERSION)
#define FPU_VER_L (FPU_SUPPORT_FUNCTION)

#define FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM     ((COP0_BACKUP_P4K_WORKAROUND) ? (4) : (0))
#if (COP0_BACKUP_P4K_WORKAROUND)
#define FPU_GC_BACKUP_P4K_PENDING_DELAY			(FPU_NOP)
#else /*(COP0_BACKUP_P4K_WORKAROUND)*/
#define FPU_GC_BACKUP_P4K_PENDING_DELAY			(NULL)
#endif  /*(COP0_BACKUP_P4K_WORKAROUND)*/

/*
 * pre-fix setting
 */

#define TOSHIBA_FAST_READ_PREFIX 	FPU_CMD(0x36)

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
#define RD_SLC_PREFIX   (FPU_CMD(0xA2))
#define RD_TLC_PREFIX   (FPU_NOP)  //resets to TLC mode automatically at the end of SLC operation 
#define RD_MLC_PREFIX   (FPU_NOP)

#define WR_SLC_PREFIX   (FPU_CMD_DQS(0xA2))
#define WR_TLC_PREFIX   (FPU_NOP)
#define WR_MLC_PREFIX   (FPU_NOP)

#define ER_SLC_PREFIX   (FPU_CMD(0xA2))
#define ER_TLC_PREFIX   (FPU_NOP)
#define ER_MLC_PREFIX   (FPU_NOP)

#define ADDRESS_READ_NUM 	(5)
#define ADDRESS_DMA_NUM 	(2)
#define ADDRESS_WRITE_NUM 	(5)
#define ADDRESS_ERASE_NUM 	(3)
#define ADDRESS_ROW_NUM 	(3)

#else
#define FPU_SLC_MODE	(0x3B)
#define FPU_QLC_MODE	(0x0D)
//TODO B47R ,auto exit can remove MLC/TLC prefix
#define RD_TLC_PREFIX   (FPU_NOP)
#define WR_TLC_PREFIX   (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)? FPU_QLC_MODE :(FPU_NOP)
#define ER_TLC_PREFIX   (FPU_NOP)
#define RD_SLC_PREFIX   (FPU_CMD(FPU_SLC_MODE))
#define RD_MLC_PREFIX   (FPU_NOP)
#define WR_SLC_PREFIX   (FPU_CMD_DQS(FPU_SLC_MODE))
#define WR_MLC_PREFIX   (FPU_NOP)
#define ER_SLC_PREFIX   (FPU_CMD(FPU_SLC_MODE))
#define ER_MLC_PREFIX   (FPU_NOP)



#define ADDRESS_READ_NUM 	(((TRUE) ? 7 : 6))
#define ADDRESS_DMA_NUM 	(6)
#define ADDRESS_WRITE_NUM 	(6)
#define ADDRESS_ERASE_NUM 	(4)
#define ADDRESS_ROW_NUM 	(4)
#endif 

#if NTODT_IMPLEMENT_EN
#define	FPU_NTODT_ITEM_SHIFT		(4) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_BYTE	(FPU_NTODT_ITEM_SHIFT << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_5		(5) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_5_BYTE	(FPU_NTODT_ITEM_SHIFT_5 << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_8		(8) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_8_BYTE	(FPU_NTODT_ITEM_SHIFT_8 << 1) //each FPU size is 2 bytes.
#else /* NTODT_IMPLEMENT_EN */
#define	FPU_NTODT_ITEM_SHIFT		(0) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_BYTE	(FPU_NTODT_ITEM_SHIFT << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_5		(0) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_5_BYTE	(FPU_NTODT_ITEM_SHIFT_5 << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_8		(0) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_8_BYTE	(FPU_NTODT_ITEM_SHIFT_8 << 1) //each FPU size is 2 bytes.
#endif /* NTODT_IMPLEMENT_EN */


typedef struct fpu_entry_list               FPU_ENTRY_LIST, *FPU_ENTRY_LIST_PTR;

struct fpu_entry_list {
	U16 fpu_version[2];
	U16 fpu_entry_addr_gen_die[2];
	U16 fpu_entry_nop[2];
	U16 fpu_entry_addr_gen_only[2];


	U16 fpu_entry_read_status_busy_40[8];//polling index 0 
	U16 fpu_entry_read_status_busy_20[8];//polling index 1

	U16 fpu_entry_read_status_70_01_CURR_TLC_MODE[8];
	U16 fpu_entry_read_status_70_02_PREV_TLC_MODE[8];
	U16 fpu_entry_read_status_70_04_CURR_SLC_MODE[8];
	U16 fpu_entry_read_status_70_08_PREV_SLC_MODE[8];


	U16 fpu_entry_read_status_70_busy_20_CURR_TLC_MODE_01[8];//polling index 5
	U16 fpu_entry_read_status_70_busy_40_PREV_TLC_MODE_02[8];//polling index 4

	U16 fpu_entry_read_status_70_busy_20_CURR_SLC_MODE_04[8];//polling index 7
	U16 fpu_entry_read_status_70_busy_40_PREV_SLC_MODE_08[8];//polling index 6
	U16 fpu_entry_read_status_70_busy_C0_INTERMEDIATE_MULTIPLANE_PROGRAM[8];
	U16 fpu_entry_read_status_70_busy_20_CURR_SLC_TLC_MODE_05[8];

	U16 fpu_entry_read_status_lun0_busy_20[8];
	U16 fpu_entry_read_status_lun1_busy_20[8];
	U16 fpu_entry_read_status_lun2_busy_20[8];
	U16 fpu_entry_read_status_lun3_busy_20[8];
	U16 fpu_entry_read_status_78_busy_40[8];
	U16 fpu_entry_read_status_78_busy_20[8];

	U16 fpu_read_and_compare_feature_data[16];
	U16 fpu_entry_read_status_err_81[8];
	U16 fpu_entry_read_status_70_busy_80_00[8];
	U16 fpu_entry_read_status_70_busy_20_00[8];

	U16 fpu_entry_getfeature_compare_data00_00_00_00[12];
	U16 fpu_entry_getfeature_compare_data01_00_00_00[12];

	U16 fpu_set_feature[28];
	U16 fpu_get_feature[16];

	U16 fpu_entry_read[7]; //(12)       // Error Handling
	U16 fpu_entry_dma_r_05_e0[9];       // Error Handling
	U16 fpu_entry_dma_r_06_e0_poll_ready[11];
	U16 fpu_reserved0[9];    // Error Handling

	U16 fpu_entry_dump_ibuf[2];         // Error Handling
	U16 fpu_entry_dma_r_raw[9];         // Error Handling

	/*
	 *
	 * normal fpu section
	 *
	 */

	U16 fpu_entry_reset_ff[3];
	U16 fpu_entry_reset_fc[3];
	U16 fpu_entry_reset_fa_lun0[12];
	U16 fpu_entry_reset_fa_lun1[12];
	U16 fpu_entry_reset_fa_lun2[12];
	U16 fpu_entry_reset_fa_lun3[12];

	U16 fpu_entry_slc_read[7];

	U16 fpu_entry_erase[6]; //(12)
	U16 fpu_entry_slc_erase[6]; //(14+2)
	U16 fpu_entry_slc_60h_row_erase[5];
	U16 fpu_entry_tlc_60h_row_erase[5];
	U16 fpu_entry_2p_erase[9];

	/*
	 * misc
	 */

	U16 fpu_entry_read_dma[5];
	U16 fpu_entry_dly_only[2];

	U16 fpu_entry_erase_all_done[5];
	U16 fpu_entry_70[3];
	U16 fpu_entry_prog_to_flash_cache[7];
	U16 fpu_entry_read_from_flash_cache[7];



	/*
	 * read/cache read
	 */
	U16 fpu_entry_slc_3F_read[4];
	U16 fpu_entry_3F_read[6];

	/*
	 * Read command: slc / mlc mode + plane cache mode.
	 */

	U16 fpu_entry_slc_1p_20_read[1];
	U16 fpu_entry_tlc_1p_20_read[6];
	U16 fpu_entry_slc_1p_30_read[6];
	U16 fpu_entry_slc_1p_31_read[6];

	U16 fpu_entry_slc_2p_32_30_read[15];
	U16 fpu_entry_slc_2p_32_31_read[15];
	U16 fpu_entry_slc_2p_32_F1_30_read[1];
	U16 fpu_entry_slc_2p_32_F2_30_read[1];

	U16 fpu_entry_slc_3p_32_30_read[24];
	U16 fpu_entry_slc_3p_32_31_read[24];
	U16 fpu_entry_slc_4p_32_30_read[33];
	U16 fpu_entry_slc_4p_32_31_read[33];


	/*
	 * Read command: tlc mode + plane cache mode. (HYNIX_V6_TLC)
	 */

	U16 fpu_entry_tlc_1p_30_lower_pg_read[6];
	U16 fpu_entry_tlc_1p_30_middle_pg_read[6];
	U16 fpu_entry_tlc_1p_30_upper_pg_read[6];

	U16 fpu_entry_tlc_1p_31_lower_pg_read[6];
	U16 fpu_entry_tlc_1p_31_middle_pg_read[6];
	U16 fpu_entry_tlc_1p_31_upper_pg_read[6];
	
	U16 fpu_entry_tlc_2p_32_30_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_30_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_30_upper_pg_read[16];
	
	U16 fpu_entry_tlc_2p_32_31_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_31_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_31_upper_pg_read[16];
	
	U16 fpu_entry_tlc_2p_32_F1_30_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F1_30_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F1_30_upper_pg_read[16];
	
	U16 fpu_entry_tlc_2p_32_F2_30_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F2_30_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F2_30_upper_pg_read[16];
	
	U16 fpu_entry_tlc_3p_32_30_lower_pg_read[26];
	U16 fpu_entry_tlc_3p_32_30_middle_pg_read[26];
	U16 fpu_entry_tlc_3p_32_30_upper_pg_read[26];
	
	U16 fpu_entry_tlc_3p_32_31_lower_pg_read[26];
	U16 fpu_entry_tlc_3p_32_31_middle_pg_read[26];
	U16 fpu_entry_tlc_3p_32_31_upper_pg_read[26];
	
	U16 fpu_entry_tlc_4p_32_30_lower_pg_read[36];
	U16 fpu_entry_tlc_4p_32_30_middle_pg_read[36];
	U16 fpu_entry_tlc_4p_32_30_upper_pg_read[36];
	
	U16 fpu_entry_tlc_4p_32_31_lower_pg_read[36];
	U16 fpu_entry_tlc_4p_32_31_middle_pg_read[36];
	U16 fpu_entry_tlc_4p_32_31_upper_pg_read[36];


	U16 fpu_common_region_delimiter[2];

	/*
	 * Program command: slc / mlc mode.
	 */

	U16 fpu_entry_prog_mlc_80_11[12];
	U16 fpu_entry_prog_mlc_80_15[8];
	U16 fpu_entry_prog_bypass_WDMA[4];
	U16 fpu_entry_prog_slc_80_11[12];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_prog_slc_80_11_gc[12 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_prog_slc_80_15[8];
#if	(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	U16 fpu_entry_prog_slc_85_15[8];	//0107 2021 Micron N48R SLC Cache workaround
#endif /*(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)*/
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_prog_slc_80_15_gc[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_prog_slc_80_11_F1[12];
	U16 fpu_entry_prog_slc_80_11_F2[12];

	U16 fpu_entry_mlc_prog[8];
	U16 fpu_entry_slc_prog[8];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_slc_prog_gc[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_test_CA2_C85_A5_DW[8];

	/*
	 * program command: tlc mode.
	 */

	U16 fpu_entry_m3d_tlc_prog_10[9];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_m3d_tlc_prog_10_gc[9 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/

	U16 fpu_entry_m3d_tlc_prog_11[13];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_m3d_tlc_prog_11_gc[13 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/

	U16 fpu_entry_m3d_tlc_prog_15[9];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_m3d_tlc_prog_15_gc[9 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/

	U16 fpu_entry_m3d_tlc_prog_11_die0[13];

	U16 fpu_entry_m3d_tlc_prog_11_die1[13];

	/*
	 * get feature temperature
	 */

	U16 fpu_entry_getfeature_temperature_die0[5];
	U16 fpu_entry_getfeature_temperature_die1[5];
	U16 fpu_entry_getfeature_dma[4];
	U16 fpu_entry_check_plane_status_mk20[8];

	/*
	 * Error Handle - Softbit
	 */

	//For E11 verification
	U16 fpu_entry_adg_test[3];
	U16 fpu_entry_check_status_to_ready[5];

	U16 fpu_entry_test_CA2_C00_A5_C30[8];
	U16 fpu_entry_test_C05_A5_CE0_DR[8];
	U16 fpu_entry_test_C05_A2_CE0_DR[8];
	U16 fpu_reserved1[8];
	U16 fpu_entry_test_C70_POL_MK40_C00[8];
	U16 fpu_entry_test_CA2_C80_A5_DW_C10[8];
	U16 fpu_entry_test_C85_A5_DW[8];
	U16 fpu_entry_test_CA2_C60_A3_CD0[8];

	U16 fpu_entry_70_e0[4];

	U16 fpu_raw_dma_read[9];

	U16 fpu_tlc_read_lower[7];
	U16 fpu_tlc_read_middle[7];
	U16 fpu_tlc_read_upper[7];

	U16 fpu_tlc_rr_lower[7];
	U16 fpu_tlc_rr_middle[7];
	U16 fpu_tlc_rr_upper[7];
	U16 fpu_slc_micron_sb_read[1];
	U16 fpu_tlc_micron_sb_read[8];

	U16 fpu_sbc[2];
	U16 fpu_backup_restore_ibuf[5];

	U16 fpu_00_30_read[8];

	U16 fpu_ram_cmd[7];

	U16 fpu_correct[3];

	U16 fpu_reserved2[9];

	U16	fpu_slc_prefix_read_retry[1];
	U16 fpu_tlc_prefix_read_retry[9];

	U16 fpu_micron_set_mlbi_cmd[12];
	U16 fpu_micron_get_mlbi_cmd[12];
	U16 fpu_micron_set_mlbi_cmd_by_MT[16][7];
	U16 fpu_micron_get_mlbi_cmd_by_MT[16][5];
#if NES_EN
	U16 fpu_C6_erase[16];
#else /* NES_EN */
	U16 fpu_C6_erase[8];
#endif /* NES_EN */
	U16 fpu_micron_nand_mode_use[16];
	U16 fpu_entry_slc_ndep_30_read_mod_DAC[1];
	U16 fpu_entry_tlc_ndep_30_read_mod_DAC[11];
	U16 fpu_entry_slc_ndep_30_read_clear[1];
	U16 fpu_entry_tlc_ndep_30_read_clear[11];
};

#define M_OPT_GET_FPU_READ_DMA_WITH_POLL() (FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_06_e0_poll_ready))

extern FPU_ENTRY_LIST _SI(FLH_FPU_SECTION) gFpuEntryList;

#endif /* (S17_EN) */
#endif /* _S17_FPU_H_ */


