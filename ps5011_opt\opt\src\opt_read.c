/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_read.c                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_READ_C_

#include "opt_main.h"
#include "opt_arch.h"
#include "opt_const.h"
#include "opt_hal.h"
#include "opt_mt_ext.h"
#include "opt_read.h"
#include "fpu.h"
#include "opt_debug.h"
#include "nds32_intrinsic.h"
#include "opt_api.h"
#include"seedinit_tbl.h"
#include "opt_micron_read_disturb.h"
#if (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
#define M_READ_DISTURB_SLC_CONDITION(pJob)			(pJob->ubD1)
#else /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
#define M_READ_DISTURB_SLC_CONDITION(pJob)			(pJob->slc_mode)
#endif /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
#define	M_GET_READ_CNT_HIGH_BYTE_UNIT_IDX(x)		        (x>>1)
#define M_READ_DISTURB_SLC_ERASE_CNT_RANGE_ADJUSTMENT(x)	(x*10)
#define ARM_MAX_READ_CNT        (BIT_MASK(11))
//consider SPOR error log loss, every 128k re-send forceswap request when read cnt over forceswap threshold.
#define RESEND_FORCESWAP_CNT    (32) // 4K*32 = 128K, Expected not to send force swap requests too frequently
/***********************************************
** private prototypes
************************************************/
static inline BOOL opt_mt_group_read_trig_page(OPT_JOB_STRUCT_PTR job);
static BOOL opt_mt_group_read_trig_page_and_data_out(OPT_JOB_STRUCT_PTR job_trig_page, OPT_JOB_STRUCT_PTR job_data_out, BOOL cache_cmd);
static inline BOOL opt_mt_group_read_cache_end_and_data_out(OPT_JOB_STRUCT_PTR job);
static BOOL opt_mt_group_read_data_out(OPT_JOB_STRUCT_PTR job);
static void OPTReadCntHandle(OPT_JOB_STRUCT_PTR pJob, U8 ubCmdCnt);
static BOOL opt_mt_group_IWL_trig_page(OPT_JOB_STRUCT_PTR job, BOOL ubNextResult);
static BOOL opt_mt_group_IWL_read_data(OPT_JOB_STRUCT_PTR job);

#if (NEW_IWL_EN)
U8 gubGlobalPlaneIdx = 0;
#endif /* (NEW_IWL_EN) */
BOOL opt_macro_cmd_normal_read(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	U8 ubNeedMTCnt;
	// cmd + split DMA(*2) + wordline status bypass
#if (NEW_IWL_EN)
	ubNeedMTCnt = MT_1_MT_TRIG * (job->ubIWLEn ? job->cmd_cnt : 1) + (job->cmd_cnt * 2) + job->ubMicronDummyRead; //Add SetFeature MT
#else /* (NEW_IWL_EN) */
	ubNeedMTCnt = MT_1_MT_TRIG + (job->cmd_cnt * 2) + job->ubMicronDummyRead; //Add SetFeature MT
#endif /* (NEW_IWL_EN) */
	/* normal read only support multi-plane read, this case not support cache read. */
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		if (_chk_read_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
			if (MT_TRIGGER_CNT_EMPTY == OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) {
				if (((IOR_EN) ? ((M_FIP_GET_CURRENT_GROUP_ID() == job->ubMinGroupID) || (OPT_INVALID_IOR_GROUP_ID == job->ubMinGroupID)) : TRUE)) {
					if (FALSE == _chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
						job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
					}
				}
			}
		}
		else {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (job->ubIWLEn) {
#if (NEW_IWL_EN)
			for (gubGlobalPlaneIdx = 0; gubGlobalPlaneIdx < NAND_MAX_PLANE; gubGlobalPlaneIdx++) {
				if (job->plane_vld & (BIT0 << gubGlobalPlaneIdx)) {
					opt_mt_group_IWL_trig_page(job, FALSE);
					opt_mt_group_IWL_read_data(job);
				}
			}
#else /* (NEW_IWL_EN) */
			opt_mt_group_IWL_trig_page(job, FALSE);
			opt_mt_group_IWL_read_data(&que_mgr->job_handle[que_mgr->head_job]);
#endif /* (NEW_IWL_EN) */
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2;
		}
		else {
			if (opt_mt_group_read_trig_page_and_data_out(job, job, FALSE)) {
				job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2;
			}
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2 == job->macro_state) {
		if ((0 == job->cmd_cnt) || ((job->cmd_cnt) && opt_mt_group_read_data_out(job))) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			hal_optq_multi_pop();
			OPTReadCntHandle(job, bit_vld_2_bit_cnt[job->plane_vld]);
			__ASM_VOLATILE_MEMORY__();
			while (OPTQB[R8_OPT_PS_MULTI_POP] != IDLE);        // wait HW idle

			return TRUE;
		}
	}

	job->ubReturn = TRUE;
	__ASM_VOLATILE_MEMORY__();
	return FALSE;
}

BOOL opt_macro_cmd_cache_read_start(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job) //3
{
	U8 ubNeedMTCnt;
#if (NEW_IWL_EN)
	ubNeedMTCnt = MT_1_MT_TRIG * (job->ubIWLEn ? job->cmd_cnt : 1);
#else /* (NEW_IWL_EN) */
	ubNeedMTCnt = MT_1_MT_TRIG;
#endif /* (NEW_IWL_EN) */

	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		if (_chk_read_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
			if (MT_TRIGGER_CNT_EMPTY == OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) {
				if (((IOR_EN) ? ((M_FIP_GET_CURRENT_GROUP_ID() == job->ubMinGroupID) || (OPT_INVALID_IOR_GROUP_ID == job->ubMinGroupID)) : TRUE)) {
					if (FALSE == _chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
						job->macro_state = OPT_MACRO_CMD_STATE_RUN_PRE_CACHE;
					}
				}
			}
		}
		else {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_PRE_CACHE;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_PRE_CACHE == job->macro_state) {
		if (job->ubIWLEn) {
#if (NEW_IWL_EN)
			for (gubGlobalPlaneIdx = 0; gubGlobalPlaneIdx < NAND_MAX_PLANE; gubGlobalPlaneIdx++) {
				if (job->plane_vld & (BIT0 << gubGlobalPlaneIdx)) {
					opt_mt_group_IWL_trig_page(job, FALSE);
				}
			}
			job->macro_state = OPT_MACRO_CMD_STATE_WAIT_CLOSE;
			return TRUE;
#else /* (NEW_IWL_EN) */
			if (opt_mt_group_IWL_trig_page(job, FALSE)) {
				job->macro_state = OPT_MACRO_CMD_STATE_WAIT_CLOSE;
				return TRUE;
			}
#endif /* (NEW_IWL_EN) */
		}
		else {
			if (opt_mt_group_read_trig_page(job)) {
				job->macro_state = OPT_MACRO_CMD_STATE_WAIT_CLOSE;
				return TRUE;
			}
		}
	}

	job->ubReturn = TRUE;
	__ASM_VOLATILE_MEMORY__();
	return FALSE;
}

BOOL opt_macro_cmd_cache_read(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	OPT_JOB_STRUCT_PTR job_data_out = &que_mgr->job_handle[que_mgr->head_job];
	U8 ubNeedMTCnt;

	// cmd + split DMA(*2) + wordline status bypass
#if (NEW_IWL_EN)
	if (job->ubIWLEn) {
		ubNeedMTCnt = (job->cmd_cnt * MT_1_MT_TRIG) + (job_data_out->cmd_cnt * 2);  //don't need to calculate the Set feature cnt for WLBypass because we shouldn't switch states in the cache
	}
	else {
		ubNeedMTCnt = MT_1_MT_TRIG + (job_data_out->cmd_cnt * 2);    //don't need to calculate the Set feature cnt for WLBypass because we shouldn't switch states in the cache
	}
#else /* (NEW_IWL_EN) */
	ubNeedMTCnt = MT_1_MT_TRIG + (job_data_out->cmd_cnt * 2);  //don't need to calculate the Set feature cnt for WLBypass because we shouldn't switch states in the cache
#endif /* (NEW_IWL_EN) */
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		if (_chk_read_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
			if (MT_TRIGGER_CNT_EMPTY == OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) {
				if (((IOR_EN) ? ((M_FIP_GET_CURRENT_GROUP_ID() == job_data_out->ubMinGroupID) || (OPT_INVALID_IOR_GROUP_ID == job_data_out->ubMinGroupID)) : TRUE)) {
					if (FALSE == _chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
						job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_NEXT;
					}
				}
			}
		}
		else {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_NEXT;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_CACHE_NEXT == job->macro_state) {
		if (job->ubIWLEn) {
#if (NEW_IWL_EN)
			for (gubGlobalPlaneIdx = 0; gubGlobalPlaneIdx < NAND_MAX_PLANE; gubGlobalPlaneIdx++) {
				if (job_data_out->plane_vld & (BIT0 << gubGlobalPlaneIdx)) {
					opt_mt_group_IWL_read_data(job_data_out);
				}
				if (job->plane_vld & (BIT0 << gubGlobalPlaneIdx)) {
					opt_mt_group_IWL_trig_page(job, TRUE);
				}
			}
#else /* (NEW_IWL_EN) */
			if (job_data_out->ubIWLEn == job->ubIWLEn) {
#if DEBUG_DCCCM_BY_QUEUE
				if (gOptStruct.cur_que <= DEBUG_QUEUE_MAX) {
					_WRITE_OPT_DCCM_MT_DEBUG(0xA << 24 | (0xFFFFFF & job_data_out->rmp_pca));
					_WRITE_OPT_DCCM_MT_DEBUG(0xB << 24 | (0xFFFFFF & job->rmp_pca));
				}
#endif
				opt_mt_group_IWL_read_data(job_data_out);
				opt_mt_group_IWL_trig_page(job, TRUE);
			}
			else {
#if DEBUG_DCCCM_BY_QUEUE
				if (gOptStruct.cur_que <= DEBUG_QUEUE_MAX) {
					_WRITE_OPT_DCCM_MT_DEBUG(0xC << 24 | (0xFFFFFF & job_data_out->rmp_pca));
					_WRITE_OPT_DCCM_MT_DEBUG(0xD << 24 | (0xFFFFFF & job->rmp_pca));
				}
#endif
				gubIWLCrossGroup = TRUE;
				opt_mt_group_IWL_trig_page(job, TRUE);
				opt_mt_group_IWL_read_data(job_data_out);
				gubIWLCrossGroup = FALSE;
			}
#endif /* (NEW_IWL_EN) */
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_POST_CACHE;
		}
		else {
			if (opt_mt_group_read_trig_page_and_data_out(job, job_data_out, TRUE)) {
				job->macro_state = OPT_MACRO_CMD_STATE_RUN_POST_CACHE;
			}
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_POST_CACHE == job->macro_state) {
		if ((0 == job_data_out->cmd_cnt) || ((job_data_out->cmd_cnt) && opt_mt_group_read_data_out(job_data_out))) {
			job_data_out->macro_state = OPT_MACRO_CMD_STATE_DONE;
			job->macro_state = OPT_MACRO_CMD_STATE_WAIT_CLOSE;

			hal_optq_multi_pop();
			OPTReadCntHandle(job_data_out, bit_vld_2_bit_cnt[job_data_out->plane_vld]);
			__ASM_VOLATILE_MEMORY__();
			while (OPTQB[R8_OPT_PS_MULTI_POP] != IDLE);        // wait HW idle

			return TRUE;
		}
	}

	job_data_out->ubReturn = TRUE;
	job->ubReturn = TRUE;
	__ASM_VOLATILE_MEMORY__();
	return FALSE;
}

BOOL opt_macro_cmd_cache_read_end(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job) //4
{
	U8 ubNeedMTCnt = MT_1_MT_TRIG + (job->cmd_cnt * 2);

	OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x6, OPT_MACRO_CMD_STATE_INIT == job->macro_state);
	if (OPT_MACRO_CMD_STATE_WAIT_CLOSE == job->macro_state) {
		if (_chk_read_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
			if (MT_TRIGGER_CNT_EMPTY == OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) {
				if (((IOR_EN) ? ((M_FIP_GET_CURRENT_GROUP_ID() == job->ubMinGroupID) || (OPT_INVALID_IOR_GROUP_ID == job->ubMinGroupID)) : TRUE)) {
					if (FALSE == _chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubNeedMTCnt)) {
						job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_END;
					}
				}
			}
		}
		else {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_END;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_CACHE_END == job->macro_state) {

		if (job->ubIWLEn) {
#if (NEW_IWL_EN)
			for (gubGlobalPlaneIdx = 0; gubGlobalPlaneIdx < NAND_MAX_PLANE; gubGlobalPlaneIdx++) {
				if (job->plane_vld & (BIT0 << gubGlobalPlaneIdx)) {
					opt_mt_group_IWL_read_data(job);
				}
			}
#else /* (NEW_IWL_EN) */
			opt_mt_group_IWL_read_data(job);
#endif /* (NEW_IWL_EN) */
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA;
		}
		else if (job->cache_prev) {
			if (opt_mt_group_read_cache_end_and_data_out(job)) {
				job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA;
			}
		}
		else {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA == job->macro_state) {
		if ((0 == job->cmd_cnt) || ((job->cmd_cnt) && opt_mt_group_read_data_out(job))) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			hal_optq_multi_pop();
			OPTReadCntHandle(job, bit_vld_2_bit_cnt[job->plane_vld]);
			__ASM_VOLATILE_MEMORY__();
			while (OPTQB[R8_OPT_PS_MULTI_POP] != IDLE);        // wait HW idle

			return TRUE;
		}
	}

	job->ubReturn = TRUE;
	__ASM_VOLATILE_MEMORY__();
	return FALSE;
}

static inline BOOL opt_mt_group_read_trig_page(OPT_JOB_STRUCT_PTR job)
{
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;
	U16 fpu;
	U8  ubGmpLookPtrIdx;
	U8  ubPlaneIdx;
	U8  ubOPTIdx;
	U8  ubQueue = gOptStruct.cur_que;
	U16 uwFpuReadBinOffset = 0;
	U8 ubBINValue = 0;

	if (IOR_FLOW_DEBUG) {
		OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x3, job->ior_ps_cross_group_exist);
		OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x4, 0 != job->ior_ps_exe_result_idx);
	}
	if ((NEW_IWL_EN) || job->ubReturn) {	// After gathering cmd, There was a switch queue before formMT, which may have caused job ->cmd_cnt... different from the PS result,need to fill in GMP_LOOK-PTR according to the job info
		OPTQL[R32_OPT_GMP_LOOKUP_PTR_3_0] = (U32)OPT_PS_INVALID_RESULT;
		uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0];
		ubGmpLookPtrIdx = 0;

		for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
			if (job->plane_vld & (BIT(ubPlaneIdx))) {
				ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
				OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xE, OPT_INVALID_OPT_INDEX == ubOPTIdx);
				if (READ_CMD_DMA_SYNC_CNT_EN) { // To avoid reading the wrong plane causing LCA search errors
					OPTQB[R8_OPT_GMP_LOOKUP_PTR_3_0 + ubGmpLookPtrIdx] = ubOPTIdx;
					ubGmpLookPtrIdx++;
				}
				else {
					OPTQB[R8_OPT_GMP_LOOKUP_PTR_3_0 + ubPlaneIdx] = ubOPTIdx;
				}
			}
		}
	}
	else {
		hal_optq_ps_gmp_result_set(0);// start cache read must from result 0
	}

	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_MULTIPLE_PCA_FROM_1ST_VALID_GMP  |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_MULTIPLE_PCA_USE_GMP_LOOKUP_PTR |
		OPTMTW_FORMMT_MARK_OPT_CMD  |
		OPTMTW_FORMMT_CMD_MT_TRIG;

	ubBINValue = job->ubBinValue;
	if (READ_CMD_DMA_SYNC_CNT_EN) {
		//  gwFPU_slc_read[cache_cmd][N], N = 0: max plane, N = 1: one plane, N = 2: two plane, N = 3: three plane
		//  gwFPU_tlc_read[OPT_LOOKUP_LMU_ADDR][cache_cmd][N], N = 0: max plane, N = 1: one plane, N = 2: two plane, N = 3: three plane
		fpu = ((job->slc_mode) ? 	(gwFPU_slc_read[0][((job->cmd_cnt > 3 ) ? 0 : job->cmd_cnt)] ) :
				(gwFPU_xlc_read[job->lmu][0][((job->cmd_cnt > 3 ) ? 0 : job->cmd_cnt)] ) );
	}
	else {
		fpu = ((job->slc_mode) ? (gwFPU_slc_read[0][job->single_plane_read]) : (gwFPU_xlc_read[job->lmu][0/*1*/][job->single_plane_read]));

	}

	hal_optmt_wait_form_mt();
	// S17 Set Bin
	if (FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) {
		M_OPT_SET_READ_CMD_BIN_VALUE(mtd, mtq, ubBINValue);
	}
	if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
		M_OPT_SET_MT_TEMPLATE_FIRST_OP();
	}
	//---------- MT TABLE ----------//
	mtq->dw5.bits.fpu_ptr = fpu;
	//---------- TRIGGER DATA ----------//
#if (( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC)|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)) //Performance issue for cache start	//Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add
	M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
	M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
#elif( CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
	M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
#else
	M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM()));
#endif
	mtd->dw1_dat.bits.nor_cq_rsp = 0;
	mtd->dw0_dat.bits.par_rls = 0;
	mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = 1;  // After cmd is downloaded, CE busy's MT will be given high priority
	mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_CQ_RD_FORMAT | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
	hal_optq_store_readinfo(job, FALSE);

	__ASM_VOLATILE_MEMORY__();
	_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
	_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw12_iFSA1);
	hal_optmt_trigger();
	if (PS_READ_LOOKUP_PTR_CHECK_DEBUG) {
		hal_optq_wait_lookup_q_valid();
		OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x2, OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
	}
	return TRUE;
}

static BOOL opt_mt_group_read_trig_page_and_data_out(OPT_JOB_STRUCT_PTR job_trig_page, OPT_JOB_STRUCT_PTR job_data_out, BOOL cache_cmd)
{
	U16 fpu;
	U8 ubPlaneIdx;
	U8 ubOPTIdx;
	U32 split, read_slice_idx, is_dma_formed, is_cmd_formed;
	U8 ubNDEPCmdCnt = 0; //0=>init value,  1=>NDEP first cmd MT,	2=>NDEP second cmd MT

#if (NDEP_READ_EN) //Gary modify on 2022/4/14
	U8 ubIsNDEPRead = (M_OPT_CHECK_LOOKUP_NDEP_READ(job_trig_page->user_define));
#else
	U8 ubIsNDEPRead = FALSE;
#endif
	
	U32 bk_lookup_ptr;
	U32 ulOPTInfo;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;
	U8 ubSnapReadEn = 0;
	U8 ubGmpLookPtrIdx;
	U8 ubQueue = gOptStruct.cur_que;
	U16 uwFpuReadBinOffset = 0;
	U8 ubBINValue = 0;
	bk_lookup_ptr = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];
	__ASM_VOLATILE_MEMORY__();

	split = 0;

	is_cmd_formed = 0;
	is_dma_formed = 0;
	read_slice_idx = 0;

	while (1) {
		if (FALSE == is_cmd_formed) {
			if ((NEW_IWL_EN) || (IOR_EN && job_trig_page->ior_ps_cross_group_exist) || job_trig_page->ubReturn || job_trig_page->ubMicronDummyRead || ubIsNDEPRead) {
				OPTQL[R32_OPT_GMP_LOOKUP_PTR_3_0] = (U32)OPT_PS_INVALID_RESULT;
				uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[((job_trig_page->slc_mode || IOR_EN) ? R8_OPT_PS_RESULT_SLC_QUEUE_0 : R8_OPT_PS_RESULT_MLC_QUEUE_0) + cache_cmd]];
				ubGmpLookPtrIdx = 0;
				for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
					if (job_trig_page->plane_vld & (BIT(ubPlaneIdx))) {
						ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
						OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x8, OPT_INVALID_OPT_INDEX == ubOPTIdx);
						if (READ_CMD_DMA_SYNC_CNT_EN) { // To avoid reading the wrong plane causing LCA search errors
							OPTQB[R8_OPT_GMP_LOOKUP_PTR_3_0 + ubGmpLookPtrIdx] = ubOPTIdx;
							ubGmpLookPtrIdx++;
						}
						else {
							OPTQB[R8_OPT_GMP_LOOKUP_PTR_3_0 + ubPlaneIdx] = ubOPTIdx;
						}
					}
				}
			}
			else {
				hal_optq_ps_gmp_result_set(OPTQB[((job_trig_page->slc_mode || IOR_EN) ? R8_OPT_PS_RESULT_SLC_QUEUE_0 : R8_OPT_PS_RESULT_MLC_QUEUE_0) + cache_cmd]);
			}

			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_MULTIPLE_PCA_FROM_1ST_VALID_GMP |
				OPTMTW_FORMMT_ATTR_SEL_TMP |
				OPTMTW_FORMMT_MULTIPLE_PCA_USE_GMP_LOOKUP_PTR |
				OPTMTW_FORMMT_MARK_OPT_CMD |
				OPTMTW_FORMMT_CMD_MT_TRIG;

			if (ubIsNDEPRead) {
				fpu = NDEP_FPU_OFFSET(ubNDEPCmdCnt);
				ubNDEPCmdCnt++;
			}
			else {
				ubBINValue = job_trig_page->ubBinValue;
				if (READ_CMD_DMA_SYNC_CNT_EN) {
					//  gwFPU_slc_read[cache_cmd][N], N = 0: max plane, N = 1: one plane, N = 2: two plane, N = 3: three plane
					//  gwFPU_tlc_read[OPT_LOOKUP_LMU_ADDR][cache_cmd][N], N = 0: max plane, N = 1: one plane, N = 2: two plane, N = 3: three plane
					if ( (FALSE == OPT_GET_LOOKUP_DISABLE_IWL) && (TRUE == SNAP_READ_EN) && (FALSE == cache_cmd) && (1 == job_trig_page->cmd_cnt)) {
						ubSnapReadEn = gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD];
					}
					if (ubSnapReadEn) {
						fpu = guwFPUMicronSnapRead[job_trig_page->slc_mode];
					}
					else {
						
							fpu = ((job_trig_page->slc_mode) ?
									(gwFPU_slc_read[cache_cmd][(job_trig_page->cmd_cnt > 3) ? 0 : job_trig_page->cmd_cnt] ) :
									(gwFPU_xlc_read[job_trig_page->lmu][cache_cmd][(job_trig_page->cmd_cnt > 3) ? 0 : job_trig_page->cmd_cnt]));
					}
				}
				else {
					fpu = ((job_trig_page->slc_mode) ?
							(gwFPU_slc_read[cache_cmd][(job_trig_page->cmd_cnt > 3) ? 0 : job_trig_page->cmd_cnt] ) :
							(gwFPU_xlc_read[job_trig_page->lmu][cache_cmd][(job_trig_page->cmd_cnt > 3) ? 0 : job_trig_page->cmd_cnt]));
				}
			}

			hal_optmt_wait_form_mt();
			// S17 Set Bin
			if ((FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) && (FALSE == ubIsNDEPRead)) {
				M_OPT_SET_READ_CMD_BIN_VALUE(mtd, mtq, ubBINValue);
			}
			//---------- MT TABLE ----------//

			mtq->dw5.bits.fpu_ptr = fpu;
			if (TRUE == job_trig_page->ubMicronDummyRead) {
				//B47R Dummy Read Need Use True Ready , to avoid 0030 Cache Ready + 0020 CRC Fail
				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
				mtd->dw0_dat.bits.par_rls = 1;
			}
			else if (ubSnapReadEn) {
				mtq->dw10_iFSA0 += ubSnapReadEn - 1;
				//Snap Read Need Use True Ready
				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
				mtd->dw1_dat.bits.nor_cq_rsp = 0;
			}
			else {
#if (( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)\
		|| ( CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)) //Performance issue for cache start	//Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add
    			if (cache_cmd) {
    				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM()));
    			}
    			else {
    				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
    			}
#else
    			M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
#endif
				mtd->dw1_dat.bits.nor_cq_rsp = 0;
			}

			if ((NDEP_READ_EN) && (2 == ubNDEPCmdCnt)) {
				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
				mtd->dw0_dat.bits.par_rls = 1;
			}

			if ((COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) && (FALSE == cache_cmd)) {
				M_OPT_SET_MT_TEMPLATE_FIRST_OP();
			}
			mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = 1;  // After cmd is downloaded, CE busy's MT will be given high priority

			mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_CQ_RD_FORMAT | OPTCM_TRIG_DATA_PCA_SEL_FSA0);

			is_cmd_formed = TRUE;

			hal_optq_store_readinfo(job_trig_page, cache_cmd);

			_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
			_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw12_iFSA1);
		}
		else {
			__ASM_VOLATILE_MEMORY__();
			if (0 == read_slice_idx) {
				if (IOR_EN) {
					uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0];
				}
				else {
					if (job_data_out->slc_mode) {
						uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_SLC_QUEUE_0]];
					}
					else {
						uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_0]];
					}
				}


				for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
					ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
					if (OPT_INVALID_OPT_INDEX != ubOPTIdx) {
						if ((BIT0 << ubPlaneIdx) & job_data_out->dma_done_map) { //This round skips the planes that have already been DMA by other funcs
							continue;
						}
						else if (FALSE == (job_data_out->plane_vld & (BIT0 << ubPlaneIdx))) {	// After gathering cmd, There was a switch queue(job->ubReturn == TRUE) before formMT, because the plane search results may change, the plane which is different from plane_vld are not read
							continue;
						}
						else {
							job_data_out->dma_done_map |= (BIT0 << ubPlaneIdx);
							break;
						}
					}
				}
				OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x9, NAND_MAX_PLANE == ubPlaneIdx); // PS_RESULT cannot all be 0x0F (invalid)
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
				hal_optq_wait_lookup_q_valid();
				if (IOR_FLOW_DEBUG) {
					OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x5, OPTQ_PROC_Q_LOOKUP_IOR_EN() && (OPTQ_PROC_Q_LOOKUP_IOR_GRP() != job_data_out->ubMinGroupID));
				}
				split = (OPTQB[R8_OPT_PROC_Q_LOOKUP_PAGE_VLD_1] ? 1 : 0);   // page valid 1 has a value indicating have jumped to frame read

			}

			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP  |
				OPTMTW_FORMMT_ATTR_SEL_TMP  |
				OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
				OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
				OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3 |
				OPTMTW_FORMMT_DMA_MT_TRIG |
				((0 == read_slice_idx) ? OPTMTW_FORMMT_PAGE_VLD_SEL_0 : OPTMTW_FORMMT_PAGE_VLD_SEL_1) |
				OPTMTW_FORMMT_HW_AUTOGEN_FRAME;


			hal_optmt_wait_form_mt();
			if (SNAP_READ_USE_FPU_POLL && ubSnapReadEn) {
				mtq->dw5.bits.fpu_ptr = M_OPT_GET_FPU_READ_DMA_WITH_POLL();
			}
			else {
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC))	//Reip Porting 3D-V7 QLC Add
				mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_00_05_e0);
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
				mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_06_e0);
#else
				mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_05_e0);
#endif				
			}
			mtq->dw0.bits.busy = 0;
			
#if (MicronFlashID4 == IM_140S_ID4)
            mtq->dw1.bits.ldpc_cor_en = (OPT_CHECK_LOOKUP_DISABLE_LDPC_COR) ? FALSE : TRUE;
#else 
            mtq->dw1.bits.ldpc_cor_en = (OPT_CHK_DIS_LDPC_CORRECTION) ? FALSE : TRUE;
#endif

			
			ulOPTInfo = 0;
			if (OPT_CHK_LOOKUP_GEN_FAIL) {
				mtq->dw0.bits.force_r_fail = TRUE;
				ulOPTInfo |= OPT_INFO_GEN_FAIL;
			}
			ulOPTInfo |= ((cache_cmd) ? OPT_INFO_CACHE_CMD : OPT_INFO_NORMAL_CMD);
			mtq->dw8_userdefine = (ulOPTInfo | (job_data_out->plane_vld << OPT_INFO_PLANE_SHIFT ) | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);

			mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_CQ_RD_FORMAT | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
			
			#if (MicronFlashID4 == IM_140S_ID4)
			cpu_comm->uoTotalNandReadinLDPCFrame += (mtq->dw9.bits.frame_num << 1);	//0617 , for B47R
			#endif
			
			if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {
				mtd->dw0_dat.bits.opt_status |= OPT_STATUS_READ_GC_BACKUP_P4K;
			}

			if ((FALSE == cache_cmd) && 		// normal_read
				(1 == job_data_out->cmd_cnt) &&
				((FALSE == split) || (1 == read_slice_idx)) &&
				((FALSE == SNAP_READ_USE_FPU_POLL) || (FALSE == ubSnapReadEn))) {	// The last DMA of the last plane will be pulled and there will be not issue read CMD again
				mtq->dw3.bits.allow_switch = 1;
				
				#if 0 // Hynix_V6_ToDo
				mtq->dw0.bits.busy = 1;
				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
				#endif
				if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
					hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
					gubClearARMStallReq = TRUE;
				}
			}

			if (ubIsNDEPRead) {
				mtq->dw1.bits.ldpc_cor_en = FALSE;
			}
			else {
				if (FALSE == split) {
					/* no need to handle split */
					mtd->dw0_dat.bits.par_rls = 1;
				}
				else {
					/* 5(0101) 9(1001) 10(1010) 11(1011) 13(1101) */
					if (0 == read_slice_idx) {
					}
					else {
						mtd->dw0_dat.bits.par_rls = 1;
					}
					read_slice_idx++;
				}
			}

#if (SUPPORT_OPT_3D_RANDOMIZER)
			if (OPT_CHECK_TABLE_READ_NO_WAIT) {
				U16 uwPage = (mtq->dw10_iFSA0 >> COP0_PAGE_START_POINT(3)) & BITMSK(COP0_PAGE_LENS(3), 0);
				if (!job_trig_page->slc_mode) {
                    OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0X6, TRUE);
				}
				mtd->dw0_dat.bits.seed_init_src = 1;
				mtq->dw7.all = M_OPT_GET_SEEDINIT(uwPage);
			}
#endif /* (SUPPORT_OPT_3D_RANDOMIZER) */

			is_dma_formed = 1;
			_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
		}

		hal_optmt_trigger();

		if (ubIsNDEPRead && (1 == ubNDEPCmdCnt) && (1 == is_dma_formed)) {
			is_cmd_formed = FALSE;
		}

		if (gubClearARMStallReq) {
			hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
			gubClearARMStallReq = FALSE;
		}

		if (((NDEP_READ_EN) && (2 == ubNDEPCmdCnt)) || ((FALSE == ubIsNDEPRead) && (((TRUE == is_dma_formed) && (FALSE == split)) || (2 == read_slice_idx) || (TRUE == job_trig_page->ubMicronDummyRead)))) {
			break;
		}
	}

	if (TRUE == job_trig_page->ubMicronDummyRead) {
		job_data_out->dma_done_map = job_trig_page->plane_vld;
		job_data_out->cmd_cnt = 0;
	}

	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;

	if (PS_READ_LOOKUP_PTR_CHECK_DEBUG) {
		hal_optq_wait_lookup_q_valid();
		OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x3, OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
	}
	if (job_data_out->cmd_cnt) {
		job_data_out->cmd_cnt -= 1;
	}

	if (0 == job_data_out->cmd_cnt) {
		for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
			if ((BIT0 << ubPlaneIdx) &  job_data_out->dma_done_map) {
				ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
				hal_optq_internal_pop(ubOPTIdx);
			}
		}
	}

	return TRUE;
}

static inline BOOL opt_mt_group_read_cache_end_and_data_out(OPT_JOB_STRUCT_PTR job)
{
	U8 ubGmpLookPtrIdx;
	U8 ubPlaneIdx;
	U8 ubOPTIdx;
	U16 fpu;
	U32 split, read_slice_idx, is_dma_formed, is_cmd_formed;
	U32 bk_lookup_ptr;
	U32 ulOPTInfo;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;

	bk_lookup_ptr = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];
	__ASM_VOLATILE_MEMORY__();

	split = 0;

	is_cmd_formed = 0;
	is_dma_formed = 0;
	read_slice_idx = 0;

	while (1) {
		if (FALSE == is_cmd_formed) {
			// first dma slice, share the same mt of read trig page
			hal_optq_ps_gmp_result_set(OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_0 + (IOR_EN ? TRUE : job->slc_mode) * PS_RESULT_SLC_QUEUE_OFFSET]);

			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_MULTIPLE_PCA_FROM_1ST_VALID_GMP |
				OPTMTW_FORMMT_ATTR_SEL_TMP  |
				OPTMTW_FORMMT_MULTIPLE_PCA_USE_GMP_LOOKUP_PTR |
				OPTMTW_FORMMT_CMD_MT_TRIG;

			fpu = guwFPUEndCacheRead[job->slc_mode];

			hal_optmt_wait_form_mt();

			//---------- MT TABLE ----------//
			mtq->dw5.bits.fpu_ptr = fpu;
			//---------- TRIGGER DATA ----------//
			M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM()));
			mtd->dw1_dat.bits.nor_cq_rsp = 0;
			mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = 1;  // After cmd is downloaded, CE busy's MT will be given high priority

			mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_CQ_RD_FORMAT | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
			is_cmd_formed = 1;
			hal_optq_store_readinfo(job, FALSE);
		}
		else {

			if (0 == read_slice_idx) {
				if (IOR_EN || job->slc_mode) {
					uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_SLC_QUEUE_0]];
				}
				else {
					uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_0]];
				}

				for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
					ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
					if (OPT_INVALID_OPT_INDEX != ubOPTIdx) {
						if ((BIT0 << ubPlaneIdx) &  job->dma_done_map ) { //This round skips the planes that have already been DMA by other funcs
							continue;
						}
						else if (FALSE == (job->plane_vld & (BIT0 << ubPlaneIdx))) {	// After gathering cmd, There was a switch queue(job->ubReturn == TRUE) before formMT, because the plane search results may change, the plane which is different from plane_vld are not read
							continue;
						}
						else {
							job->dma_done_map |= (BIT0 << ubPlaneIdx);
							break;
						}
					}
				}

				OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xA, NAND_MAX_PLANE == ubPlaneIdx); // PS_RESULT cannot all be 0x0F (invalid)
				__ASM_VOLATILE_MEMORY__();
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
				hal_optq_wait_lookup_q_valid();
				if (IOR_FLOW_DEBUG) {
					OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x6, OPTQ_PROC_Q_LOOKUP_IOR_EN() && (OPTQ_PROC_Q_LOOKUP_IOR_GRP() != job->ubMinGroupID));
				}
				split = (OPTQB[R8_OPT_PROC_Q_LOOKUP_PAGE_VLD_1] ? 1 : 0);   // page valid 1 has a value indicating have jumped to frame read
			}

			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP  |
				OPTMTW_FORMMT_ATTR_SEL_TMP |
				OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
				OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
				OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3 |
				OPTMTW_FORMMT_DMA_MT_TRIG |
				((0 == read_slice_idx) ? OPTMTW_FORMMT_PAGE_VLD_SEL_0 : OPTMTW_FORMMT_PAGE_VLD_SEL_1) |
				OPTMTW_FORMMT_HW_AUTOGEN_FRAME;

			hal_optmt_wait_form_mt();
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))	//Reip Porting 3D-V7 QLC Add
			mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_00_05_e0);
#else
			mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_05_e0);
#endif			
			mtq->dw0.bits.busy = 0;

#if (MicronFlashID4 == IM_140S_ID4)
            mtq->dw1.bits.ldpc_cor_en = (OPT_CHECK_LOOKUP_DISABLE_LDPC_COR) ? FALSE : TRUE;
#else 
            mtq->dw1.bits.ldpc_cor_en = (OPT_CHK_DIS_LDPC_CORRECTION) ? FALSE : TRUE;
#endif

			
			ulOPTInfo = 0;
			if (OPT_CHK_LOOKUP_GEN_FAIL) {
				mtq->dw0.bits.force_r_fail = TRUE;
				ulOPTInfo |= OPT_INFO_GEN_FAIL;
			}
			ulOPTInfo |= OPT_INFO_NORMAL_CMD;	// HC require this cmd with normal
			mtq->dw8_userdefine = (ulOPTInfo | (job->plane_vld << OPT_INFO_PLANE_SHIFT ) | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);
			mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_CQ_RD_FORMAT | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
			
			#if (MicronFlashID4 == IM_140S_ID4)
			cpu_comm->uoTotalNandReadinLDPCFrame += (mtq->dw9.bits.frame_num << 1);	//0617 , for B47R
			#endif
			
			if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {
				mtd->dw0_dat.bits.opt_status |= OPT_STATUS_READ_GC_BACKUP_P4K;
			}

			if ((1 == job->cmd_cnt) && ((FALSE == split) || (1 == read_slice_idx))) {	// The last DMA of the last plane pull
				mtq->dw3.bits.allow_switch = 1;
				//Cache Read ends,after the last DMA completing, needs to confirm 0xE0
				mtq->dw0.bits.busy = TRUE;
				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
				if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
					hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
					gubClearARMStallReq = TRUE;
				}
			}

			if (FALSE == split) {
				/* no need to handle split */
				mtd->dw0_dat.bits.par_rls = 1;
			}
			else {
				/* 5(0101) 9(1001) 10(1010) 11(1011) 13(1101) */
				if (0 == read_slice_idx) {
				}
				else {
					mtd->dw0_dat.bits.par_rls = 1;
				}
				read_slice_idx++;
			}

#if (SUPPORT_OPT_3D_RANDOMIZER)
			if (OPT_CHECK_TABLE_READ_NO_WAIT) {
				U16 uwPage = (mtq->dw10_iFSA0 >> COP0_PAGE_START_POINT(3)) & BITMSK(COP0_PAGE_LENS(3), 0);
				if (!job->slc_mode) {
                    OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0X6, TRUE);
				}
				mtd->dw0_dat.bits.seed_init_src = 1;
				mtq->dw7.all = M_OPT_GET_SEEDINIT(uwPage);
			}
#endif /* (SUPPORT_OPT_3D_RANDOMIZER) */

			is_dma_formed = 1;
		}

		_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
		hal_optmt_trigger();

		if (gubClearARMStallReq) {
			hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
			gubClearARMStallReq = FALSE;
		}

		if (((TRUE == is_dma_formed) && (FALSE == split)) || (2 == read_slice_idx)) {
			break;
		}
	}

	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;

	if (PS_READ_LOOKUP_PTR_CHECK_DEBUG) {
		hal_optq_wait_lookup_q_valid();
		OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x4, OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
	}
	job->cmd_cnt -= 1;

	if (0 == job->cmd_cnt) {
		for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
			if ((BIT0 << ubPlaneIdx) &  job->dma_done_map) {
				ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
				hal_optq_internal_pop(ubOPTIdx);
			}
		}
	}

	return TRUE;
}

static BOOL opt_mt_group_read_data_out(OPT_JOB_STRUCT_PTR job)
{
	U8 ubPlaneIdx;
	U8 ubOPTIdx;
	U8 bk_lookup_ptr;
	U32 plane;
	U32 plane_cnt;
	U32 split;
	U32 read_slice_idx;
	U32 ulOPTInfo;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;

	bk_lookup_ptr = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];
	__ASM_VOLATILE_MEMORY__();

	plane_cnt = job->cmd_cnt;

	for (plane = 0; plane < plane_cnt; plane++) {
		split = 0;
		read_slice_idx = 0;
		while (1) {
			__ASM_VOLATILE_MEMORY__();

			if (0 == read_slice_idx) {

				if (IOR_EN) {
					uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0];
				}
				else {
					if (job->slc_mode) {
						uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_SLC_QUEUE_0]];
					}
					else {
						uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_0]];
					}
				}

				for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
					ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
					if (OPT_INVALID_OPT_INDEX != ubOPTIdx) {
						if (BIT(ubPlaneIdx)  &  job->dma_done_map) {  //This round skips the planes that have already been DMA by other funcs
							continue;
						}
						else if (FALSE == (job->plane_vld & (BIT0 << ubPlaneIdx))) {	// After gathering cmd, There was a switch queue(job->ubReturn == TRUE) before formMT, because the plane search results may change, the plane which is different from plane_vld are not read
							continue;
						}
						else {
							job->dma_done_map |= (BIT0 << ubPlaneIdx);
							break;
						}
					}
				}

				OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xB, NAND_MAX_PLANE == ubPlaneIdx); // PS_RESULT cannot all be 0x0F (invalid)
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
				hal_optq_wait_lookup_q_valid();
				OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x7, OPTQ_PROC_Q_LOOKUP_IOR_EN() && (OPTQ_PROC_Q_LOOKUP_IOR_GRP() != job->ubMinGroupID));
				split = (OPTQB[R8_OPT_PROC_Q_LOOKUP_PAGE_VLD_1] ? 1 : 0);   // page valid 1 has a value indicating have jumped to frame read
			}

			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP  |
				OPTMTW_FORMMT_ATTR_SEL_TMP |
				OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
				OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
				OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3 |
				OPTMTW_FORMMT_DMA_MT_TRIG  |
				((0 == read_slice_idx) ? OPTMTW_FORMMT_PAGE_VLD_SEL_0 : OPTMTW_FORMMT_PAGE_VLD_SEL_1) |
				OPTMTW_FORMMT_HW_AUTOGEN_FRAME;


			ulOPTInfo = 0;

			if ((OPT_MACRO_CMD_STATE_WAIT_CLOSE == job->macro_state) || (OPT_MACRO_CMD_STATE_RUN_POST_CACHE == job->macro_state)) {
				ulOPTInfo |= OPT_INFO_CACHE_CMD;
			}
			else {
				ulOPTInfo |= OPT_INFO_NORMAL_CMD;
			}

			hal_optmt_wait_form_mt();
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)	//Reip Porting 3D-V7 QLC Add
			mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_00_05_e0);
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
			mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_06_e0);
#else
			mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_05_e0);
#endif			
			mtq->dw0.bits.busy = 0;
			
#if (MicronFlashID4 == IM_140S_ID4)
            mtq->dw1.bits.ldpc_cor_en = (OPT_CHECK_LOOKUP_DISABLE_LDPC_COR) ? FALSE : TRUE;
#else 
            mtq->dw1.bits.ldpc_cor_en = (OPT_CHK_DIS_LDPC_CORRECTION) ? FALSE : TRUE;
#endif

			if (OPT_CHK_LOOKUP_GEN_FAIL) {
				mtq->dw0.bits.force_r_fail = TRUE;
				ulOPTInfo |= OPT_INFO_GEN_FAIL;
			}
			mtq->dw8_userdefine = ( ulOPTInfo | (job->plane_vld << OPT_INFO_PLANE_SHIFT) | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);

			mtd->dw0_dat.all |= (OPTCM_TRIG_DATA_CQ_RD_FORMAT | OPTCM_TRIG_DATA_PCA_SEL_FSA0);
			#if (MicronFlashID4 == IM_140S_ID4)
			cpu_comm->uoTotalNandReadinLDPCFrame += (mtq->dw9.bits.frame_num << 1);	//0617 , for B47R
			#endif
			if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {
				mtd->dw0_dat.bits.opt_status |= OPT_STATUS_READ_GC_BACKUP_P4K;
			}

			//if ((job->macro_state != OPT_MACRO_CMD_STATE_WAIT_CLOSE) &&	// When entering the cached state, the read_data_out should not end with cmd
			if (((OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2 == job->macro_state) || (OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA == job->macro_state)) &&	// When entering the cached state, the read_data_out should not end with cmd
				(1 == job->cmd_cnt) &&
				((FALSE == split) || (1 == read_slice_idx))) {	// The last DMA of the last plane pull

				mtq->dw3.bits.allow_switch = TRUE;
				//Last dma of normal read or cache fail
				mtq->dw0.bits.busy = TRUE;
				M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
				if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
					hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
					gubClearARMStallReq = TRUE;
				}
			}

			if (FALSE == split) {
				/* no need to handle split */
				mtd->dw0_dat.bits.par_rls = 1;
			}
			else {
				/* 5(0101) 9(1001) 10(1010) 11(1011) 13(1101) */
				if (0 == read_slice_idx) {
				}
				else {
					mtd->dw0_dat.bits.par_rls = 1;
				}
				read_slice_idx++;
			}

#if (SUPPORT_OPT_3D_RANDOMIZER)
			if (OPT_CHECK_TABLE_READ_NO_WAIT) {
				U16 uwPage = (mtq->dw10_iFSA0 >> COP0_PAGE_START_POINT(3)) & BITMSK(COP0_PAGE_LENS(3), 0);
				if (!job->slc_mode) {
                    OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0X6, TRUE);
				}
				mtd->dw0_dat.bits.seed_init_src = 1;
				mtq->dw7.all = M_OPT_GET_SEEDINIT(uwPage);
			}
#endif /* (SUPPORT_OPT_3D_RANDOMIZER) */

			__ASM_VOLATILE_MEMORY__();
			_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
			hal_optmt_trigger();

			if (gubClearARMStallReq) {
				hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
				gubClearARMStallReq = FALSE;
			}

			if ((FALSE == split) || (2 == read_slice_idx)) {
				break;
			}
		}

		job->cmd_cnt -= 1;
	}


	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;

	if (IOR_EN) {
		uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0];
	}
	else {
		if (job->slc_mode) {
			uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_SLC_QUEUE_0]];
		}
		else {
			uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_0]];
		}
	}

	for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
		if (BIT(ubPlaneIdx) &  job->dma_done_map) {
			ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
			hal_optq_internal_pop(ubOPTIdx);
		}
	}
	if (PS_READ_LOOKUP_PTR_CHECK_DEBUG) {
		OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xC, job->dma_done_map != job->plane_vld);
		hal_optq_wait_lookup_q_valid();
		OPT_CRITICAL_ASSERT(ASSERT_LOOKUP_PTR_ERROR | 0x5, OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
	}

	return TRUE;
}

void OPTReadCntHandle(OPT_JOB_STRUCT_PTR pJob, U8 ubCmdCnt)
{
#if (READ_DISTURB_PRDH_EN)
	OPTMicronReadCntHandle(pJob, ubCmdCnt);
#else /* (READ_DISTURB_PRDH_EN) */
	U32 ulAddr = 0;
	ArmReadCnt_t uoReadCnt = {0};
	U16 uwUnit = 0;
	U16 uwReadCnt = 0;
	U8 ubReadCntTemp = 0;
	U8 ubLoop = 0;
	U16 uwEraseCnt = 0;
	U16 uwReadVerifyReadCntThreshold = 0;
	U16 uwForceCopyReadCntThreshold = 0;

	cpu_comm->uoTotalNandRead += ubCmdCnt;
	if (cpu_comm->uwReadCntSequentialReadWeightingFactor) {
		__ASM_VOLATILE_MEMORY__();
		if (FALSE == OPTQ_PROC_Q_LOOKUP_RMP_BPS) {
			uwUnit = (gOptStruct.ulPCAMaskFWUnit & pJob->pca) >> gOptStruct.ubPCAMaskFWUnitShift;
			if ((uwUnit == gOptStruct.que_mgr[gOptStruct.cur_que].uwContinuousUnit) && (pJob->page != gOptStruct.que_mgr[gOptStruct.cur_que].uwPage) && ((pJob->cache_prev) || (pJob->cache_next) || (cpu_comm->SeqRead))) {
				gOptStruct.que_mgr[gOptStruct.cur_que].uwPage = pJob->page;
				gOptStruct.que_mgr[gOptStruct.cur_que].uwSequentialReadWeightingFactorCnt += ubCmdCnt;
				if (gOptStruct.que_mgr[gOptStruct.cur_que].uwSequentialReadWeightingFactorCnt > cpu_comm->uwReadCntSequentialReadWeightingFactor) {
					gOptStruct.que_mgr[gOptStruct.cur_que].uwSequentialReadWeightingFactorCnt = 0;
				}
				else {
					return;
				}
			}
			else {
				gOptStruct.que_mgr[gOptStruct.cur_que].uwSequentialReadWeightingFactorCnt = 0;
			}
			gOptStruct.que_mgr[gOptStruct.cur_que].uwContinuousUnit = uwUnit;
			ubReadCntTemp = gpubReadCntLowByte[uwUnit];
			gpubReadCntLowByte[uwUnit] += ubCmdCnt;
			if (gpubReadCntLowByte[uwUnit] < ubReadCntTemp) {	// Represents a carry after overfill 256 read cnt
				if (uwUnit & BIT0) {
					++gpubReadCntHighByte[M_GET_READ_CNT_HIGH_BYTE_UNIT_IDX(uwUnit)].ReadCntLow;
					ubReadCntTemp = gpubReadCntHighByte[M_GET_READ_CNT_HIGH_BYTE_UNIT_IDX(uwUnit)].ReadCntLow;
				}
				else {
					++gpubReadCntHighByte[M_GET_READ_CNT_HIGH_BYTE_UNIT_IDX(uwUnit)].ReadCntHigh;
					ubReadCntTemp = gpubReadCntHighByte[M_GET_READ_CNT_HIGH_BYTE_UNIT_IDX(uwUnit)].ReadCntHigh;
				}

				if (0 == ubReadCntTemp) {
					__ASM_VOLATILE_MEMORY__();
					cpu_comm->ubAndesModifyDBUFRequest = TRUE;
					while (TRUE != cpu_comm->ubAndesModifyDBUFRequest);
					__ASM_VOLATILE_MEMORY__();
					if (TRUE == cpu_comm->ubARMModifyDBUFRequest) {
						cpu_comm->ubAndesModifyDBUFRequest = FALSE;
						__ASM_VOLATILE_MEMORY__();
					}
					else {
						ulAddr = (DBUF_ERASE_CNT_BASE + uwUnit * ERASE_CNT_STRUCT_SIZE) & ERASE_CNT_UNIT_MASK;
						uoReadCnt.uoAll = axim_read(ulAddr);
						if (uwUnit & BIT0) {
							if (ARM_MAX_READ_CNT > uoReadCnt.uo.ReadCntHigh) {
								++uoReadCnt.uo.ReadCntHigh;
							}
							uwReadCnt = uoReadCnt.uo.ReadCntHigh;
							uwEraseCnt = uoReadCnt.uo.uwEraseCntHigh;
						}
						else {
							if (ARM_MAX_READ_CNT > uoReadCnt.uo.ReadCntLow) {
								++uoReadCnt.uo.ReadCntLow;
							}
							uwReadCnt = uoReadCnt.uo.ReadCntLow;
							uwEraseCnt = uoReadCnt.uo.uwEraseCntLow;
						}

						if (M_READ_DISTURB_SLC_CONDITION(pJob)) {
							for (ubLoop = 0; ubLoop < ERASE_CNT_LEVEL_NUM; ubLoop++) {
								if (uwEraseCnt <= (M_READ_DISTURB_SLC_ERASE_CNT_RANGE_ADJUSTMENT((U32)gpReadDisturbThreshold->uwSLCEraseCntRange[ubLoop]))) {
									uwReadVerifyReadCntThreshold = gpReadDisturbThreshold->uwSLCReadVerifyReadCntThreshold[ubLoop];
									uwForceCopyReadCntThreshold = gpReadDisturbThreshold->uwSLCForceCopyReadCntThreshold[ubLoop];
									break;
								}
							}
						}
						else {
							for (ubLoop = 0; ubLoop < ERASE_CNT_LEVEL_NUM; ubLoop++) {
								if (uwEraseCnt <= gpReadDisturbThreshold->uwTLCEraseCntRange[ubLoop]) {
									uwReadVerifyReadCntThreshold = gpReadDisturbThreshold->uwTLCReadVerifyReadCntThreshold[ubLoop];
									uwForceCopyReadCntThreshold = gpReadDisturbThreshold->uwTLCForceCopyReadCntThreshold[ubLoop];
									break;
								}
							}
						}

						if ((0 == (uwReadCnt % uwReadVerifyReadCntThreshold))  ||
							(uwReadCnt == uwForceCopyReadCntThreshold) ||
							((uwForceCopyReadCntThreshold) && (uwReadCnt > uwForceCopyReadCntThreshold) && (0 == (uwReadCnt % RESEND_FORCESWAP_CNT))) ||
							(ARM_MAX_READ_CNT == uwReadCnt)) {

							if (uwUnit & BIT0) {
								uoReadCnt.uo.btOverReadCntThresholdHigh = TRUE;
							}
							else {
								uoReadCnt.uo.btOverReadCntThresholdLow = TRUE;
							}

							if (MAX_U16_VALUE > cpu_comm->uwOverReadCntThresholdUnitNum) {
								++cpu_comm->uwOverReadCntThresholdUnitNum;
							}
						}

						axim_write(ulAddr, uoReadCnt.uoAll, 0xFF);
						cpu_comm->ubAndesModifyDBUFRequest = FALSE;
						__ASM_VOLATILE_MEMORY__();
					}
				}
			}
		}
	}
#endif /* (READ_DISTURB_PRDH_EN) */
}

//#if (MICRON_140S)
static BOOL opt_mt_group_IWL_trig_page(OPT_JOB_STRUCT_PTR job, BOOL ubNextResult)
{
	U8 ubMTQDelayEn, ubSnapReadSection, ubPlaneIdx, ubOPTIdx, ubLookupPtrBackup;
	U16 uwFPU;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;

	__ASM_VOLATILE_MEMORY__();
	ubLookupPtrBackup = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];

	OPTQL[R32_OPT_GMP_LOOKUP_PTR_3_0] = (U32)OPT_PS_INVALID_RESULT;
	uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[((IOR_EN ? TRUE : job->slc_mode) ? R8_OPT_PS_RESULT_SLC_QUEUE_0 : R8_OPT_PS_RESULT_MLC_QUEUE_0) + ubNextResult]];
#if (NEW_IWL_EN)
	ubPlaneIdx = gubGlobalPlaneIdx;
	ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
	OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xD, OPT_INVALID_OPT_INDEX == ubOPTIdx);
	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
	OPTQB[R8_OPT_GMP_LOOKUP_PTR_3_0] = ubOPTIdx;
#else /* (NEW_IWL_EN) */
	for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
		if (job->plane_vld & (BIT(ubPlaneIdx))) {	//As it is an IWL, it is expected that there will only have one Plane valid
			ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
			OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xD, OPT_INVALID_OPT_INDEX == ubOPTIdx);
			if ((IOR_EN && job->ior_ps_cross_group_exist) || job->ubReturn) {
				OPTQB[R8_OPT_GMP_LOOKUP_PTR_3_0] = ubOPTIdx;
			}
			else {
				hal_optq_ps_gmp_result_set(OPTQB[((IOR_EN ? TRUE : job->slc_mode) ? R8_OPT_PS_RESULT_SLC_QUEUE_0 : R8_OPT_PS_RESULT_MLC_QUEUE_0) + ubNextResult]);
			}
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
			break;
		}
	}
#endif /* (NEW_IWL_EN) */
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_MULTIPLE_PCA_FROM_1ST_VALID_GMP |
		OPTMTW_FORMMT_ATTR_SEL_TMP |
		OPTMTW_FORMMT_MULTIPLE_PCA_USE_GMP_LOOKUP_PTR |
		OPTMTW_FORMMT_MARK_OPT_CMD |
		OPTMTW_FORMMT_CMD_MT_TRIG;
#if FIP_MTQ_DLY
	ubMTQDelayEn = FALSE;
#endif

#if (NEW_IWL_EN)
	gOptStruct.que_mgr[gOptStruct.cur_que].ubIWLPatchBMP |= gubMicronIWLGroupBMP[BIT(ubPlaneIdx)];
#endif /* (NEW_IWL_EN) */

	uwFPU = guwFPUMicronSnapRead[job->slc_mode];
	_WRITE_OPT_DCCM_MT_DEBUG(0xBF << 24 | uwFPU);

#if FIP_MTQ_DLY
	if (0 == job->slc_mode) {
		ubMTQDelayEn = TRUE;
	}
#endif

	hal_optq_wait_lookup_q_valid();
	ubSnapReadSection = gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD] - 1;		//BMP minus 1, replaced with Frame start index
	if (0 == gubMicronSnapReadBMP[OPTQ_PROC_Q_LOOKUP_FRAME_VLD]) {
		_WRITE_OPT_DCCM_MT_DEBUG(OPTQ_PROC_Q_LOOKUP_FRAME_VLD);
		_WRITE_OPT_DCCM_MT_DEBUG(OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
		_WRITE_OPT_DCCM_MT_DEBUG(0x90 << 24 | (0xFFFFFF & mtq->dw10_iFSA0));
		OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0xB, TRUE);
	}
	hal_optmt_wait_form_mt();
	// S17 Set Bin
	if (FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) {
		M_OPT_SET_READ_CMD_BIN_VALUE(mtd, mtq, OPT_GET_LOOKUP_BIN_VALUE);
	}
#if (!NEW_IWL_EN)
	if ((COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) && (FALSE == gubIWLCrossGroup)) { //IWL Cross Group Need Patch The Other Plane Group Snap Read CMD.
		M_OPT_SET_MT_TEMPLATE_FIRST_OP();
	}
#endif /* (!NEW_IWL_EN) */
	//---------- MT TABLE ----------//
	mtq->dw0.bits.busy = 0;
	mtq->dw5.bits.fpu_ptr = uwFPU;
	//mtq->dw3.bits.upd_pol_seq = 1; // default is 1
	//M_SET_OPTCM_POLLING_SEQUENCE_SELECT(POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40);

	mtq->dw10_iFSA0 += ubSnapReadSection;


	mtd->dw1_dat.bits.nor_cq_rsp = 0;
	mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = 1;  // After cmd is downloaded, CE busy's MT will be given high priority

	mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_RD;
	M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);
#if (NEW_IWL_EN)
	hal_optq_store_readinfo_IWL(job);
#else /* (NEW_IWL_EN) */
	hal_optq_store_readinfo(job, FALSE);
#endif /* (NEW_IWL_EN) */

	_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
	//_WRITE_OPT_DCCM_MT_DEBUG(mtq->dw10_iFSA0);
#if DEBUG_DCCCM_BY_QUEUE
	if (gOptStruct.cur_que <= DEBUG_QUEUE_MAX) {
		_WRITE_OPT_DCCM_MT_DEBUG(0x90 << 24 | (0xFFFFFF & mtq->dw10_iFSA0));
	}
#endif

	// Check Trigger Data //
	hal_optmt_trigger();
	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubLookupPtrBackup;
	return TRUE;
}

static BOOL opt_mt_group_IWL_read_data(OPT_JOB_STRUCT_PTR job)
{
	U8 ubPlaneIdx, ubOPTIdx, ubLookupPtrBackup;
	U32 ulOPTInfo;
	U64 uoPlaneSearchResult = OPT_PS_INVALID_RESULT;

	__ASM_VOLATILE_MEMORY__();
	ubLookupPtrBackup = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];

	uoPlaneSearchResult = OPTQLL[R64_OPT_PS_RESULT_IDX_0 + OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_0 + ((IOR_EN ? TRUE : job->slc_mode) << 2)]];
#if (NEW_IWL_EN)
	ubPlaneIdx = gubGlobalPlaneIdx;
	ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
#else /* (NEW_IWL_EN) */
	for (ubPlaneIdx = 0; ubPlaneIdx < NAND_MAX_PLANE; ubPlaneIdx++) {
		ubOPTIdx = GET_VALUE(uoPlaneSearchResult, ubPlaneIdx * 8, 8); // Each opt cmd uses 8 bits to represent the opt index
		if (OPT_INVALID_OPT_INDEX != ubOPTIdx) {  //E19
			if (FALSE == (job->plane_vld & (BIT0 << ubPlaneIdx))) {	// After gathering cmd, There was a switch queue(job->ubReturn == TRUE) before formMT, because the plane search results may change, the plane which is different from plane_vld are not read
				continue;
			}
			else {
				job->dma_done_map |= BIT(ubPlaneIdx);
				hal_optq_internal_pop(ubOPTIdx);
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOPTIdx;
				break;
			}
		}
	}
#endif /* (NEW_IWL_EN) */
	// PS_RESULT cannot all be 0x0F (invalid)
	OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0xF, (ubPlaneIdx == NAND_MAX_PLANE));

	hal_optq_wait_lookup_q_valid();

	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP  |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
		OPTMTW_FORMMT_CMD_MT_TRIG ;
#if (NEW_IWL_EN)
	job->dma_done_map |= BIT(ubPlaneIdx);
	hal_optq_internal_pop(ubOPTIdx);
#endif /* (NEW_IWL_EN) */
	hal_optmt_wait_form_mt();

	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_addr_gen_only);
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	M_SET_OPTCM_POLLING_SEQUENCE_SELECT(POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20);
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */

	mtd->dw0_dat.all |= (/* OPTCM_TRIG_DATA_FSA0_VLD| */ OPTCM_TRIG_DATA_PCA_SEL_FSA0);
	mtd->dw1_dat.bits.nor_cq_rsp = 0;

	//__ASM_VOLATILE_MEMORY__();

	hal_optmt_trigger();

	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP  |
		OPTMTW_FORMMT_ATTR_SEL_TMP  |
		OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
		OPTMTW_FORMMT_DMA_MT_TRIG |
		OPTMTW_FORMMT_PAGE_VLD_SEL_0 |
		OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3 |
		OPTMTW_FORMMT_HW_AUTOGEN_FRAME;

#if (NEW_IWL_EN)
	ulOPTInfo = (((U32)gOptStruct.que_mgr[gOptStruct.cur_que].ubIWLPatchBMP) << OPT_INFO_MICRON_IWL_CROSS_OPERATION_SHIFT);
	gOptStruct.que_mgr[gOptStruct.cur_que].ubIWLPatchBMP &= (~gubMicronIWLGroupBMP[BIT(ubPlaneIdx)]);
	job->ubIWLEn &= (~gubMicronIWLGroupBMP[BIT(ubPlaneIdx)]);
#endif /* (NEW_IWL_EN) */
	job->cmd_cnt--;

	hal_optmt_wait_form_mt();
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_00_05_e0);
#else
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_dma_r_05_e0);
#endif			
#if (NEW_IWL_EN)
	//record need find DMA BMP
	if (OPT_CHK_LOOKUP_GEN_FAIL) {
		mtq->dw0.bits.force_r_fail = TRUE;
		ulOPTInfo |= OPT_INFO_GEN_FAIL;
	}
	if ((!job->ubIWLEn) && ((OPT_MACRO_CMD_STATE_RUN_CACHE_END == job->macro_state) ||
			(OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state))) {
		if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
			hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
			gubClearARMStallReq = TRUE;
		}
		mtq->dw3.bits.allow_switch = 1;
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
		M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM()));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
		M_SET_OPTCM_POLLING_SEQUENCE_SELECT(POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_TLC_MODE_05);
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	}
	else {
		mtq->dw0.bits.busy = 0;
	}
	mtq->dw8_userdefine = (ulOPTInfo | (BIT(ubPlaneIdx) << OPT_INFO_PLANE_SHIFT) | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);
#else /* (NEW_IWL_EN) */
	ulOPTInfo = OPT_INFO_MICRON_SNAP_READ_OPERATION;
	if (OPT_CHK_LOOKUP_GEN_FAIL) {
		mtq->dw0.bits.force_r_fail = TRUE;
		ulOPTInfo |= OPT_INFO_GEN_FAIL;
	}

	if (gubIWLCrossGroup) {
		ulOPTInfo |= OPT_INFO_MICRON_IWL_CROSS_OPERATION;
	}

	if ((OPT_MACRO_CMD_STATE_RUN_CACHE_END == job->macro_state) || (OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA == job->macro_state) ||
		(OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state)) {
		if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
			hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
			gubClearARMStallReq = TRUE;
		}
		mtq->dw3.bits.allow_switch = 1;
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
		M_SET_OPTCM_POLLING_SEQUENCE_SELECT(M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM()));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
		M_SET_OPTCM_POLLING_SEQUENCE_SELECT(POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_TLC_MODE_05);
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	}
	else {
		mtq->dw0.bits.busy = 0;
	}


	mtq->dw8_userdefine = (ulOPTInfo | (job->plane_vld << OPT_INFO_PLANE_SHIFT) | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);
#endif /* (NEW_IWL_EN) */

#if (MicronFlashID4 == IM_140S_ID4)
    mtq->dw1.bits.ldpc_cor_en = (OPT_CHECK_LOOKUP_DISABLE_LDPC_COR) ? FALSE : TRUE;
#else 
    mtq->dw1.bits.ldpc_cor_en = (OPT_CHK_DIS_LDPC_CORRECTION) ? FALSE : TRUE;
#endif


	mtd->dw0_dat.bits.par_rls = 1;
	mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_RD;
	M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);

	if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {
		mtd->dw0_dat.bits.opt_status |= OPT_STATUS_READ_GC_BACKUP_P4K;
	}

	if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
		hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
		gubClearARMStallReq = TRUE;
	}


#if (SUPPORT_OPT_3D_RANDOMIZER)
	if (OPT_CHECK_TABLE_READ_NO_WAIT) {
		U16 uwPage = (mtq->dw10_iFSA0 >> COP0_PAGE_START_POINT(3)) & BITMSK(COP0_PAGE_LENS(3), 0);
        if (!job->slc_mode) {
            OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0X6, TRUE);
        }
		mtd->dw0_dat.bits.seed_init_src = 1;
		mtq->dw7.all = M_OPT_GET_SEEDINIT(uwPage);
	}
#endif /* (SUPPORT_OPT_3D_RANDOMIZER) */
	hal_optmt_trigger();

	if (gubClearARMStallReq) {
		hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
		gubClearARMStallReq = FALSE;
	}
	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubLookupPtrBackup;

	return TRUE;
}
//#endif/*MICRON_140S*/
