/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  hal_cop0_types.h                                                      */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _HAL_COP0_TYPES_H_
#define _HAL_COP0_TYPES_H_

#include "conf.h"
#include "opt_global.h"//蹇呴爤include global鍚﹀墖DIE_IL_EN鍙冩暩鍌充笉閫蹭締
#include "misc/types.h"
#include "misc/def.h"
#include "misc/pca.h"
#include "opt_arch.h"


/*  ------------------------------------------------
 *  cpu <-> cop0 command type
 *  ------------------------------------------------
 */
#define COP0_BLOCK_FMAN                     (0xA)

#define COP0_FMAN_CMD_WRITE                 (0x1)
#define COP0_FMAN_CMD_READ                  (0x2)
#define COP0_FMAN_CMD_ERASE                 (0x3)
#define COP0_FMAN_CMD_UNLOCK                (0x4)

// cop0 head offset
#define COP0_BLOCK_OFFSET                   (28)
#define COP0_SLC_MODE_OFFSET                (27)
#define COP0_CMD_OFFSET                     (24)
#define COP0_FINISH_OFFSET                  (23)
#define COP0_LOCK_OFFSET                    (22)
#define COP0_FLUSH_OFFSET                   (21)
#define COP0_CPU_SEL_OFFSET                 (14)



#define COP0_SRC_CPU0           (0)
#define COP0_SRC_CPU1           (1)
#define COP0_SRC_COP1           (2)


#define COP0_INT_PFA            (BIT7)
#define COP0_INT_MTSTOP         (BIT6)
#define COP0_INT_STA            (BIT5)
#define COP0_INT_EOT            (BIT4)


#define COP0_LINK_WRR_ENA           (1)
#define COP0_LINK_HIGH_WRR_WEIGHT   (2)
#define COP0_LINK_MID_WRR_WEIGHT    (2)
#define COP0_LINK_HIGH0_WEIGHT      (2)
#define COP0_LINK_HIGH1_WEIGHT      (2)




// userdata 0
#define COP0_L4K_NUM_OFFSET                 (29)
#define COP0_L4K_NUM_4K                     (0x0 << COP0_L4K_NUM_OFFSET)
#define COP0_L4K_NUM_8K                     (0x1 << COP0_L4K_NUM_OFFSET)
#define COP0_L4K_NUM_12K                    (0x2 << COP0_L4K_NUM_OFFSET)
#define COP0_L4K_NUM_16K                    (0x3 << COP0_L4K_NUM_OFFSET)

#define COP0_DAT_DEF_CONV_PAGE              BIT28
#define COP0_DAT_DEF_SEED                   BIT27
#define COP0_DAT_DEF_SPVLD_FWSET            BIT26
#define COP0_DAT_DEF_LCA                    BIT25
#define COP0_DAT_DEF_BVLD_BADR              BIT24
#define COP0_DAT_DEF_PCA                    BIT23
#define COP0_DAT_DEF_USERDEFINE             BIT22

#define COP0_ATTR_CONT_MODE                 BIT15    // FW continuous mode
#define COP0_ATTR_SEL_TEMPLATE              BIT14
#define COP0_ATTR_FORCE_EMPTY               BIT13
#define COP0_ATTR_CQ_RSP_OFT                (12)
#define COP0_ATTR_CQ_RSP(x)                 (((x) & 0x1) << COP0_ATTR_CQ_RSP_OFT)
#define COP0_ATTR_QLINK_OFT                 (10)
#define COP0_ATTR_QLINK(priority)           (priority << COP0_ATTR_QLINK_OFT)
#define COP0_PRIORITY_L                     (0x0)
#define COP0_PRIORITY_M                     (0x1)
#define COP0_PRIORITY_H0                    (0x2)
#define COP0_PRIORITY_H1                    (0x3)
#define COP0_ATTR_BMU_ALCT_EN               BIT7
#define COP0_ATTR_AES_BPS                   BIT5
#define COP0_ATTR_MT_TEMPLATE(x)            ((x) & 0xF)
#define COP0_MT_TMPL_READ_STD               (COP0_ATTR_MT_TEMPLATE(0))
#define COP0_MT_TMPL_READ_BUF_MODE          (COP0_ATTR_MT_TEMPLATE(1))
#define COP0_MT_TMPL_READ_STD_CMP_LCA       (COP0_ATTR_MT_TEMPLATE(2))
#define COP0_MT_TMPL_READ_BUF_MODE_CMP_LCA  (COP0_ATTR_MT_TEMPLATE(3))
#define COP0_MT_TMPL_PROG_STD               (COP0_ATTR_MT_TEMPLATE(4))
#define COP0_MT_TMPL_PROG_BUF_MODE          (COP0_ATTR_MT_TEMPLATE(5))
#define COP0_MT_TMPL_PROG_STD_CHK_E3D       (COP0_ATTR_MT_TEMPLATE(6))
#define COP0_MT_TMPL_PROG_BUF_MODE_CHK_E3D  (COP0_ATTR_MT_TEMPLATE(7))
#define COP0_MT_TMPL_PROG_2D_TLC_PARITY     (COP0_ATTR_MT_TEMPLATE(8))
#define COP0_MT_TMPL_ERASE_BLK              (COP0_ATTR_MT_TEMPLATE(9))

#define COP0_MT_TMPL_READ_BUF_MODE_CMP_LCA_GEN_R_FAIL   (COP0_ATTR_MT_TEMPLATE(13))
#define COP0_MT_TMPL_AUTO_GEN_R_FAIL_BUF_MODE           (COP0_ATTR_MT_TEMPLATE(14))
#define COP0_MT_TMPL_AUTO_GEN_R_FAIL                    (COP0_ATTR_MT_TEMPLATE(15))

// userdefine (information to andes)
#define COP0_USERDEFINE_DUMMY_CMD      BIT(0)
#define COP0_USERDEFINE_GET_TEMPERATURE_CMD      BIT(1)
#define COP0_USERDEFINE_MICRON_SYNC_OPEN_UNIT	BIT(2)
#define COP0_USERDEFINE_MICRON_SYNC_OPEN_GCGRUNIT	BIT(3)
#define COP0_USERDEFINE_MICRON_SYNC_OPEN_SPOR_OLDGRUNIT	BIT(4)
#define COP0_USERDEFINE_MICRON_VALLEY_CHECK		BIT(5) // Vender N28 VALLEY CHECK
#define COP0_USERDEFINE_MICRON_DISABLE_IWL_READ		BIT(5)	//MICRON B47R with read cmd

#define COP0_USERDEFINE_PROGRAM_RANDOM      BIT(1)
#define COP0_USERDEFINE_PROGRAM_TABLE       BIT(2)
#define COP0_USERDEFINE_DIS_CACHE              BIT(3)
#define COP0_USERDEFINE_GEN_FAIL            	 BIT(4)
#define COP0_USERDEFINE_IS_QLC_CELL_1ST_PASS		BIT(5)	// TSB QLC & MICRON N48R with Prog cmd
#define COP0_USERDEFINE_PROGRAM_PARITY            BIT(6)
#define COP0_USERDEFINE_END_PROGRAM               BIT(7)  // end prog/erase
#define COP0_USERDEFINE_BYPASS_WDMA			BIT(8)
#define COP0_USERDEFINE_LDPC_CORRECTION     BIT(12)
#define COP0_USERDEFINE_FORCE_ONE_PLANE_READ_USERDEFINE       BIT(7)

#define COP0_USERDEFINE_TOSHIBA_FAST_READ_REQUEST		BIT(8)
#define COP0_USERDEFINE_TABLE_READ_NO_WAIT			BIT(10)
#define COP0_USERDEFINE_MICRON_SPECIALMODE_CHECK		BIT(15) // Check Special mode , when decode mode >= 8 
#if (MicronFlashID4 == IM_140S_ID4)
#define COP0_USERDEFINE_READ_BIN_MASK	BITMSK(3, 12) //Read bin0~7, Mask BIT12~14
#define COP0_USERDEFINE_DECODER_MASK	BITMSK(4, 12) //Decoder value, Mask BIT12~15
#define COP0_USERDEFINE_MICRON_DISABLE_PREREAD	(8) //Program B47R VALLEY CHECK
#define COP0_USERDEFINE_MICRON_ENABLE_PREREAD	(9) //Program B47R VALLEY CHECK
#define COP0_USERDEFINE_MICRON_DISABLE_UL_PREREAD	(10) //Program B47R VALLEY CHECK
#define COP0_USERDEFINE_MICTON_DUMMY_READ		(11) //Read	Micron Dummy Read
#define COP0_USERDEFINE_MICRON_DISABLE_LDPC_COR	(12) //LDPC NO COR
#define COP0_USERDEFINE_MICRON_NDEP_READ		(14) //NDEP read (LDPC NO COR)

#define COP0_USERDEFINE_MICRON_BLOCK_TYPE_MASK	BITMSK(3, 6) //Micron Read Disturb block type BIT6~9
#define COP0_USERDEFINE_PRDH_BLK_TYPE_MASK	BITMSK(3, 6) //Micron Read Disturb PRDH blk type BIT6~8
#define COP0_USERDEFINE_PRDH_BYPASS_READ_CNT_HANDLE	BIT(9)
#else
#define COP0_USERDEFINE_MICRON_DISABLE_PREREAD	BIT(11) //Program N28 VALLEY CHECK
#define COP0_USERDEFINE_MICRON_ENABLE_PREREAD	BIT(12) //Program N28 VALLEY CHECK

#define COP0_USERDEFINE_MICTON_DUMMY_READ		BIT(13) //Read	Micron Dummy Read
#endif /* (MicronFlashID4 == IM_140S_ID4) */


// raidecc information
#define COP0_CONV_PAGE_EN                   BIT31
#define COP0_CONV_PAGE_PAGE_OFT             (22)
#define COP0_CONV_PAGE_PAGE(x)              (((x) & 0x1FF) << COP0_CONV_PAGE_PAGE_OFT)
#define COP0_RAIDECC_PROG_PARITY            BIT20
#define COP0_RAIDECC_2ND_PARITY             BIT19
#define COP0_RAIDECC_OTFENC_EN              BIT18    //program parity disable
#define COP0_RAIDECC_LAST_PAGE              BIT17
#define COP0_RAIDECC_TAG_OFT                (8)
#define COP0_RAIDECC_TAG(tag)               (((tag) & 0x1FF) << COP0_RAIDECC_TAG_OFT)
#define COP0_RAIDECC_PAGE(x)                ((x) & 0xFF)
#define COP0_GET_RAIDECC_TAG(raidecc_info)  (((raidecc_info) >> COP0_RAIDECC_TAG_OFT) & 0x1FF)


// formating
#define COP0_BVLD_BADR(vld, addr)           ((((vld) & 0xFF) << 24) | ((addr) >> 9))
#define COP0_BVLD_BADR_BMU_EN(vld, rbinfo)  ((((vld) & 0xFF) << 24) | (rbinfo))
#define COP0_SPVLD_FWSET(vld, fw_set)       ((((vld) & 0xFF) << 24) | ((fw_set) & 0xFFFFFF))

//Need to sync with opt_arch.h OPT_D_COMM_LENS
#define COP0_OPT_COMM_SIZE                  (0x80)

#define INVALID_IFSA						(0xFFFFFFFF)
#define INVALID_PLANE_BM				(0x0)

#define	ERASE_CNT_LEVEL_NUM		(10)
/*  ------------------------------------------------
 *
 *  ------------------------------------------------
 */

typedef struct cop0_spr_buf_out             SPR_BUF_OUT, *SPR_BUF_OUT_PTR;
typedef struct cop0_spr_buf_in              SPR_BUF_IN, *SPR_BUF_IN_PTR;
typedef struct cop0_spr_buf_page_in         SPR_BUF_PAGE_IN, *SPR_BUF_PAGE_IN_PTR;


/*
 * TIE IN CMD
 */
typedef union  cop0_cmd_head                COP0_CMD_HEAD, *COP0_CMD_HEAD_PTR;
typedef struct cop0_tie_in_cmd              COP0_TIE_IN_CMD, *COP0_TIE_IN_CMD_PTR;  /* 1 cycle */

/*
 * TIE OUT RESULT
 */
typedef union  cop0_result_head             COP0_RESULT_HEAD, *COP0_RESULT_HEAD_PTR;
typedef struct cop0_tie_out_result          COP0_TIE_OUT_RESULT, *COP0_TIE_OUT_RESULT_PTR;  /* 1 cycle */
typedef struct cop0_result                  COP0_RESULT, *COP0_RESULT_PTR;

/*
 * OPT comm info
 */
typedef struct cop0_comm_info_struct        COP0_COMM_INFO_STRUCT, *COP0_COMM_INFO_STRUCT_PTR;

typedef struct cop0_read_info_struct        COP0_READ_INFO_STRUCT, *COP0_READ_INFO_STRUCT_PTR;

typedef struct cop0_patch_cmd_info_struct 		COP0_PATCH_CMD_INFO_STRUCT, *COP0_PATCH_CMD_INFO_STRUCT_PTR;

typedef struct cop0_micron_unit_info_struct        COP0_MICRON_UNIT_INFO_STRUCT, *COP0_MICRON_UNIT_INFO_STRUCT_PTR;


/*
 * spare buffer tie-in format
 */
struct cop0_spr_buf_in { // one LC
	U32 lca;

	union {
		U32 data32;
		struct {
			U8 reserved;
			U8 seq;          /* time stamp sub cnt for gc */
			U8 mark;
			U8 spare_valid;
		} fwset;
	} dw1;
};


struct cop0_spr_buf_page_in {
	SPR_BUF_IN  spr[PAGE_SIZE];
};


/*
 * spare buffer tie-out format (spare_valid move from last byte to first byte)
 */
struct cop0_spr_buf_out { // one LC
	U32 lca;

	union {
		U32 data32;
		struct {
			U8 spare_valid;
			U8 reserved;
			U8 seq;          /* time stamp sub cnt for gc */
			U8 mark;
		} fwset;
	} dw1;
};




/*
 *
 */

union cop0_cmd_head {
	U32                 u32;

	struct {
		U32             tag             :   14;     /* This field is used to define this command's Task-Tag. */
		U32             cpu_sel         :   2;      /* This field is used to indicatd tie-out message should return to which CPU */
		U32             reserved        :   3;
		U32             rev             :   2;      /* enable BMU function -> reserved */
		U32             flush           :   1;      /* This bit is used to enable flush function. */
		U32             lock            :   1;      /* This bit is used to enable Lock function. */
		U32             finish          :   1;      /* This bit is used to define the command format be done. */
		U32             cmd             :   3;      /* This field is used to control Operation CMD */
		U32             slc_mode        :   1;      /* This bit is used to define the command format be done. */
		U32             block           :   4;      /* 4'HA */
	} fields;
};

struct cop0_tie_in_cmd {
	U32             data32;
	COP0_CMD_HEAD   head;
};



/*
 *
 */

union cop0_result_head {
	U32                 u32;

	struct {
		// interrupt message
		// tag[13:8] = reserved for error handling
		// tag[7:0] = the tag of tie-in
		U32             tag             :   8;      /* TAG The tag from TIE IN */
		U32             err_info        :   6;      /* the error information from error cpu */
		U32             cpu_sel         :   2;
		U32             opt_sts         :   5;      /* The OPT_STS from the COPT */
		U32             slv_err         :   1;      /* To identify the slaver error */
		U32             msg_type        :   1;      /* To identify the message type */
		U32             finish          :   1;      /* The last one of TIE OUT sequence */
		U32             cmd             :   3;      /* The CMD from TIE IN */
		U32             slc_mode        :   1;      /* move 1 cmd bit to slc_mode*/
		U32             block           :   4;      /* COP0_BLOCK_FMAN, ... */
	} fields;
};


struct cop0_tie_out_result {
	U32                 data32;
	COP0_RESULT_HEAD    head;
};



struct cop0_result {
	COP0_RESULT_HEAD head;

	union {
		/*
		 * tie-out
		 */
		U32 u32[2];

		/*
		 * tie-out of err-msg
		 */
		struct {
			U32  err_frm : 4;
			U32  int_msg : 8;
			U32  mt_idx  : 8;
			U32  src     : 1;
			U32  slv_id  : 2;
			U32  rsv     : 9;

			U32 err_pca;

		} err_msg;

		/*
		 * tie-out of read cmd
		 */
		SPR_BUF_OUT     read_msg;

		/*
		 * tie-out of write cmd: no data
		 */
		struct {
			U32 reserved1;
			U32 reserved2;
		} write_msg;

	} data;
};

struct cop0_micron_unit_info_struct {
	U16	uwOpenUnit[OPEN_UNIT_TYPE_NUM];
	U16	uwNextOpenUnit[OPEN_UNIT_TYPE_NUM];
	U16 uwD3PageSize;
	union {
		U8 ubAll;
		struct {
			U8 btValidNextOpenGRUnit: 1;
			U8 btValidNextOpenGCGRUnit  : 1;
			U8 btValidNextOpenSPOROldGRUnit : 1;
			U8 Resv  : 5;
		} bits;
	} Flag;
	U8 ubLUNForMoreUnitEn;
};

#if (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
struct cop0_comm_info_struct {
	/*
	 * msg direction: opt -> cpu
	 */
	U64 uoARM_Stall_Req_BM;
	U64 uoAndes_Stop_FormMT_BM;
	U64 uoAndes_Req_ARM_Directly_Stall_BM;
	U64 uoARM_Stop_FormMT_Req_BM;
	//------------------------------------------
	/*
	 * msg direction: cpu -> opt
	 */
	BOOL    disable_cache_read;
	BOOL    disable_cache_prog;
	U8     ubEnterFTLTask;
	U8      prog_timeout_cnt;
	U8      read_timeout_cnt;
	U8 	   SeqProg;
	U8	   SeqRead;
	U8     btUltraDMADis : 1;
	U8     btPolling_waitrdy : 1;//zerio BICS8 Add
	U8     Reserved : 6;
	//------------------------------------------
	U8     ubFWCheckRaidECCRegRequest;
	U8     ubAndesCheckRaidECCRegRequest;
	U8      ubARMModifyDBUFRequest;
	U8      ubAndesModifyDBUFRequest;
	U16     uwOverReadCntThresholdUnitNum;
	U16     uwReadCntSequentialReadWeightingFactor;
	//------------------------------------------
	U64    uoTotalNandWrite;
	U64    uoTotalNandRead;
	U8    ubPreviousProgramPlaneBMP[8];
	//------------------------------------------ // 浠ヤ笂閫氱敤鍏�2 byte
	/*
	 * micron variables
	 */
#if (MicronFlashID4 == IM_140S_ID4)
	volatile U64 uoTotalNandReadinLDPCFrame;
#endif /* IM_140S_ID4 */
	COP0_MICRON_UNIT_INFO_STRUCT micron_info;//16 bytes
	//------------------------------------------
	U32 ulWordLineBypassStateBMP;
#if (MicronFlashID4 == IM_N28A_ID4)
	U32 ulWordLineBypasseXtraPageOverrideAndPrereadBMP; // High 16 Bits = Preread Enable Disable, Low 16 Bits = EXTRA PAGE Override
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
	U32    ulProgLUXTQueueBMP;  //Doing Prog LUXT
	/*
	 * reserve
	 */
#if (MicronFlashID4 == IM_140S_ID4)
#if (READ_DISTURB_PRDH_EN)
#if (PS5013_EN || S17_EN)
	U8      ubARMModifyOPTDRequest;
	U8      ubAndesModifyOPTDRequest;
#endif /* (PS5013_EN || S17_EN) */
	U16		uwOpenTableUnit;
	U8		ubPECG[COP0_PCA_RULE_NUM];
#endif /* (READ_DISTURB_PRDH_EN) */
#endif /* (MicronFlashID4 == IM_140S_ID4) */

	BOOL ubGeneralLevelAssertEn; // for 8B align
#if (MicronFlashID4 == IM_N28A_ID4)
	U8      rsv[COP0_OPT_COMM_SIZE - (85 + sizeof(COP0_MICRON_UNIT_INFO_STRUCT) + 4 + OPT_MARK_BYTES + OPT_WFI_BYTES)];		// 72(閫氱敤) + 4(N28A) + 9(WLBS + GeneralLeval + LUXT) = 85, opt_assert_dccm鐐� byte
#elif (MicronFlashID4 == IM_140S_ID4)
	U8      rsv[COP0_OPT_COMM_SIZE - (89 + sizeof(COP0_MICRON_UNIT_INFO_STRUCT) + 4 + OPT_MARK_BYTES + OPT_PRDH_BYTES + OPT_WFI_BYTES)];	// 72(閫氱敤) + 8(140S) + 9(WLBS + GeneralLeval + LUXT) = 89, opt_assert_dccm鐐� byte, READ_DISTURB 8 byte
#else /* (MicronFlashID4 == IM_N28A_ID4) */
	U8      rsv[COP0_OPT_COMM_SIZE - (81 + sizeof(COP0_MICRON_UNIT_INFO_STRUCT) + 4 + OPT_MARK_BYTES + OPT_WFI_BYTES)];		// 72(閫氱敤) + 9(WLBS + GeneralLeval + LUXT) = 81, opt_assert_dccm鐐� byte
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
#if (S17_EN)
	U32	   ulVersion1;
	U32	   ulVersion2;
#endif /* (S17_EN) */

	U32    opt_assert_dccm;		// ARM 0x0028CFCC, Andes 0x00086FCC
};
#else  /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
	struct cop0_comm_info_struct {
	/*
	 * msg direction: opt -> cpu
	 */
	U64 uoARM_Stall_Req_BM;
	U64 uoAndes_Stop_FormMT_BM;
	U64 uoAndes_Req_ARM_Directly_Stall_BM;
	U64 uoARM_Stop_FormMT_Req_BM;
	//------------------------------------------
	/*
	 * msg direction: cpu -> opt
	 */
	BOOL    disable_cache_read;
	BOOL    disable_cache_prog;
	U8     ubEnterFTLTask;
	U8      prog_timeout_cnt;
	U8      read_timeout_cnt;
	U8 	   SeqProg;
	U8	   SeqRead;
	U8     btUltraDMADis : 1;
	U8     btPolling_waitrdy : 1;//zerio BICS8 Add
	U8     Reserved : 6;
	//------------------------------------------
	U8     ubReserved[2];
	U8      ubARMModifyDBUFRequest;
	U8      ubAndesModifyDBUFRequest;
	U16     uwOverReadCntThresholdUnitNum;
	U16     uwReadCntSequentialReadWeightingFactor;
	//------------------------------------------
	U64    uoTotalNandWrite;
	U64    uoTotalNandRead;
	U8    ubPreviousProgramPlaneBMP[8];
	//------------------------------------------ // 以上通用共72 byte
	/*
	 * hynix variables
	 */
	U8 ubFWCheckRaidECCRegRequest;
	U8 ubAndesCheckRaidECCRegRequest;
	U8 ubBiCS4AIPROnFlag;
	U8 ubLUNForMoreUnitEn;
	U8 ubGeneralLevelAssertEn;
	U8      rsv[COP0_OPT_COMM_SIZE - (77 + 4 + OPT_MARK_BYTES + OPT_WFI_BYTES)];		// 72(閫氱敤) + 5 = 77, opt_assert_dccm鐐� byte
	#if (S17_EN)
	U32	   ulVersion1;
	U32	   ulVersion2;
	#endif  /* (S17_EN) */
	U32    opt_assert_dccm;
	};
#endif

#define COP0_PATCH_CMD_INFO_DEPTH	2

struct cop0_patch_cmd_info_struct {
	U32	ulFSAGroup[COP0_PATCH_CMD_INFO_DEPTH][4];	// previous/current; plane index
	union {
		U8	ubPlaneVld_BM[COP0_PATCH_CMD_INFO_DEPTH];	// previous/current
		U16 uwPlaneVld_BM;
	};
	union {
		U16	uwAll;
		struct {
			U8 ubALURule: 2;
			U8 btIsIWLRead: 1;
			U8 IWLGroupBMP: 2;	//NO USE
			U8 HeadDieIdx: 2;
			U8 reserve0: 1;
			U8 BinValue: 3;
			U8 BlkType: 3;
			U8 btByPassReadCntHandle: 1;
			U8 btreserve1: 1;
		} bits;
	} cmdInfo;
};

struct cop0_read_info_struct {
	/*
	 * msg direction: opt -> cpu
	 */
	COP0_PATCH_CMD_INFO_STRUCT ulReadInfo[CONFIG_NUM_FIP_CE_TOTAL];
};


#endif /* _HAL_COP0_TYPES_H_ */

