/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_arch.h                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_ARCH_H_
#define _OPT_ARCH_H_

#include "misc/types.h"
#include "conf.h"

#if (PS5021_EN)
#include "E21_opt_arch.h"
#elif (S17_EN)
#include "S17_opt_arch.h"
#else /* (PS5021_EN) */
#include "E13_opt_arch.h"
#endif /* (PS5021_EN) */

#endif /* _OPT_ARCH_H_ */
