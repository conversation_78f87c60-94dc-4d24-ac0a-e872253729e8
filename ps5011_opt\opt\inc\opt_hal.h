/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_hal.h                                                             */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_HAL_H_
#define _OPT_HAL_H_


//#include "shr_types.h"
#include "misc/types.h"
#include "hal/cop0/hal_cop0_reg.h"
#include "hal/cop0/hal_cop0_types.h"
#include "opt_main.h"
#include "opt_arch.h"
#include "opt_debug.h"

#ifdef _OPT_HAL_C_
#define EXTERN
#else
#define EXTERN extern
#endif


#if DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA
#define _TRIGGER_FROM_HEAD_       (0)
#define _TRIGGER_FROM_LOOKUP_     (1)
#endif

#define M_OPT_SET_MT_TEMPLATE_ALLOW_SWITCH()				(mtq->dw3.bits.allow_switch = TRUE)
#define M_OPT_SET_MT_TEMPLATE_FIRST_OP()					(mtq->dw0.bits.first_op = TRUE)
#if (NEW_IWL_EN)
EXTERN void hal_optq_store_readinfo_IWL(OPT_JOB_STRUCT_PTR job);
#endif /* (NEW_IWL_EN) */
EXTERN void hal_optq_store_readinfo(OPT_JOB_STRUCT_PTR job, U8 cache_cmd);
EXTERN BOOL hal_optq_check_SwitchQueueCnt(U8 ubQueueIndex);
EXTERN BOOL hal_optq_check_ARM_stall_MTpool_req(U8 ubQueueIndex);
EXTERN BOOL hal_optq_check_stop_formMT_req(U8 ubQueueIndex);
EXTERN void hal_optq_clear_ARM_stall_MTpool_req(U8 ubQueueIndex);
EXTERN void hal_optq_Andes_stop_formMT(U8 ubQueueIndex);
EXTERN BOOL hal_optq_check_Andes_stop_formMT(U8 ubQueueIndex);
EXTERN void hal_optq_StopFormMT_ClearStallReq(U8 ubQueueIndex);
EXTERN void hal_optq_StopFormMT_ClearStopFormMTReq(U8 ubQueueIndex);
EXTERN void hal_optq_Andes_req_directly_stall_MTpool(U8 ubQueueIndex);

EXTERN void hal_optq_wait_lookup_q_valid();
EXTERN void hal_optq_wait_lookup_q_convpage_valid();
EXTERN void hal_optq_wait_lookup_q_valid_and_not_stall();
EXTERN void hal_optq_wait_curq_valid();
EXTERN void hal_optq_ps_gmp_result_set(U8 result_idx);
EXTERN void hal_optq_internal_pop(U8 optidx);
EXTERN void hal_optq_multi_pop();
EXTERN void hal_optq_q_pop();

EXTERN void hal_optmt_wait_form_mt();
EXTERN void hal_optmt_trigger();
EXTERN U8   hal_loopup_page_type();


#undef EXTERN
#endif /* #ifndef _OPT_HAL_H_ */
