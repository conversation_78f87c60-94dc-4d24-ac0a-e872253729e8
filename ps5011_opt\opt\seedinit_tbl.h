/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2020, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  seedinit_tbl.h                                                        */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef SEEDINIT_TBL_H_
#define SEEDINIT_TBL_H_

#include "misc/types.h"
#include "opt_arch.h"

#ifdef _SEEDINIT_TBL_C_
#define EXTERN
#else
#define EXTERN extern
#endif

#define SEED_INIT_NUM ((OPT_DCCM_SEED_INIT_TABLE_LENS >> 2) - 1) // 127

#if (SUPPORT_OPT_3D_RANDOMIZER)
EXTERN volatile U32 *gpulSeedInitTable;
#define M_OPT_GET_SEEDINIT(PAGE)	(gpulSeedInitTable[((PAGE) % SEED_INIT_NUM)])
#else /* (SUPPORT_OPT_3D_RANDOMIZER) */
#define M_OPT_GET_SEEDINIT(PAGE)
#endif /* (SUPPORT_OPT_3D_RANDOMIZER) */
#undef EXTERN
#endif /* SEEDINIT_TBL_H_ */

