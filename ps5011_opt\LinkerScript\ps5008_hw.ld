/* This file is generated by nds_ldsag (version 1.1.1 date 2013-03-20). */
OUTPUT_FORMAT("elf32-nds32", "elf32-nds32",
	"elf32-nds32")
OUTPUT_ARCH(nds32)
ENTRY(_start)
SECTIONS
{
	PROVIDE (__executable_start = 0x00000000);
	sag_lma = 0x00000000 ;
	OPTS_RAM_BEGIN = sag_lma;
	. = 0x00000000;
	.nds32_init		:	{ KEEP(*(.nds32_init)) }
	.interp		:	{ *(.interp) }
	.hash		:	{ *(.hash) }
	.dynsym		:	{ *(.dynsym) }
	.dynstr		:	{ *(.dynstr) }
	.gnu.version		:	{ *(.gnu.version) }
	.gnu.version_d		:	{ *(.gnu.version_d) }
	.gnu.version_r		:	{ *(.gnu.version_r) }
	.rel.init		:	{ *(.rel.init) }
	.rela.init		:	{ *(.rela.init) }
	.rel.text		:	{ *(.rel.text .rel.text.* .rel.gnu.linkonce.t.*) }
	.rela.text		:	{ *(.rela.text .rela.text.* .rela.gnu.linkonce.t.*) }
	.rel.fini		:	{ *(.rel.fini) }
	.rela.fini		:	{ *(.rela.fini) }
	.rel.rodata		:	{ *(.rel.rodata .rel.rodata.* .rel.gnu.linkonce.r.*) }
	.rela.rodata		:	{ *(.rela.rodata .rela.rodata.* .rela.gnu.linkonce.r.*) }
	.rel.data.rel.ro		:	{ *(.rel.data.rel.ro*) }
	.rela.data.rel.ro		:	{ *(.rel.data.rel.ro*) }
	.rel.data		:	{ *(.rel.data .rel.data.* .rel.gnu.linkonce.d.*) }
	.rela.data		:	{ *(.rela.data .rela.data.* .rela.gnu.linkonce.d.*) }
	.rel.tdata		:	{ *(.rel.tdata .rel.tdata.* .rel.gnu.linkonce.td.*) }
	.rela.tdata		:	{ *(.rela.tdata .rela.tdata.* .rela.gnu.linkonce.td.*) }
	.rel.tbss		:	{ *(.rel.tbss .rel.tbss.* .rel.gnu.linkonce.tb.*) }
	.rela.tbss		:	{ *(.rela.tbss .rela.tbss.* .rela.gnu.linkonce.tb.*) }
	.rel.ctors		:	{ *(.rel.ctors) }
	.rela.ctors		:	{ *(.rela.ctors) }
	.rel.dtors		:	{ *(.rel.dtors) }
	.rela.dtors		:	{ *(.rela.dtors) }
	.rela.dyn		:	{ *(rela.dyn) *(.rela__libc_subfreeres) *(.rela__libc_atexit) *(.rela__libc_thread_subfreeres) *(.rela.init_array) *(.rela.fini_array) }
	.rel.got		:	{ *(.rel.got) }
	.rela.got		:	{ *(.rela.got) }
	.rel.sdata		:	{ *(.rel.sdata .rel.sdata.* .rel.gnu.linkonce.s.*) }
	.rela.sdata		:	{ *(.rela.sdata .rela.sdata.* .rela.gnu.linkonce.s.*) }
	.rel.sbss		:	{ *(.rel.sbss .rel.sbss.* .rel.gnu.linkonce.sb.*) }
	.rela.sbss		:	{ *(.rela.sbss .rela.sbss.* .rela.gnu.linkonce.sb.*) }
	.rel.sdata2		:	{ *(.rel.sdata2 .rel.sdata2.* .rel.gnu.linkonce.s2.*) }
	.rela.sdata2		:	{ *(.rela.sdata2 .rela.sdata2.* .rela.gnu.linkonce.s2.*) }
	.rel.sbss2		:	{ *(.rel.sbss2 .rel.sbss2.* .rel.gnu.linkonce.sb2.*) }
	.rela.sbss2		:	{ *(.rela.sbss2 .rela.sbss2.* .rela.gnu.linkonce.sb2.*) }
	.rel.bss		:	{ *(.rel.bss .rel.bss.* .rel.gnu.linkonce.b.*) }
	.rela.bss		:	{ *(.rela.bss .rela.bss.* .rela.gnu.linkonce.b.*) }
	.rel.plt		:	{ *(.rel.plt) }
	.rela.plt		:	{ *(.rela.plt) }
	.init		:	{ KEEP(*(.init)) }
	.plt		:	{ *(.plt) }
	.text		:	{ *(.text .stub .text.* .gnu.linkonce.t.*) KEEP(*(.text.*personality*)) *(.gnu.warning) }
	.fini		:	{ KEEP(*(.fini)) }
	.rodata		:	{ *(.rodata .rodata.* .gnu.linkonce.r.*) }
	.rodata1		:	{ *(.rodata1) }
	.sdata2		:	{ *(.sdata2 .sdata2.* .gnu.linkonce.s2.*) }
	.sbss2		:	{ *(.sbss2 .sbss2.* .gnu.linkonce.sb2.*) }
	.eh_frame_hdr		:	{ *(.eh_frame_hdr) }
	PROVIDE (__etext = .);
	PROVIDE (_etext = .);
	PROVIDE (etext = .);
	. = 0x00082A00;
	EXEC_DATA_BEGIN = .;
	__data_lmastart =  LOADADDR (.eh_frame_hdr) + SIZEOF (.eh_frame_hdr);
	__data_start = .;
	. = ALIGN(0x20) - ((0x20 - .) & (0x20 - 1));
	. = DATA_SEGMENT_ALIGN(0x20, 0x20);
	.eh_frame		:	AT(LOADADDR (.eh_frame_hdr) + SIZEOF (.eh_frame_hdr)) { KEEP(*(.eh_frame)) }
	.gcc_except_table		:	AT(LOADADDR (.eh_frame) + SIZEOF (.eh_frame)) { KEEP(*(.gcc_except_table)) *(.gcc_except_table.*) }
	.tdata		:	AT(LOADADDR (.gcc_except_table) + SIZEOF (.gcc_except_table)) { *(.tdata .tdata.* .gnu.linkonce.td.*) }
	.tbss		:	AT(LOADADDR (.tdata) + SIZEOF (.tdata)) { *(.tbss .tbss.* .gnu.linkonce.tb.*) *(.tcommon) }
	. = ALIGN(32 / 8);
	PROVIDE (__preinit_array_start = .);
	.preinit_array		:	AT(LOADADDR (.tbss) + SIZEOF (.tbss)) { KEEP(*(.preinit_array)) }
	PROVIDE (__preinit_array_end = .);
	PROVIDE (__init_array_start = .);
	.init_array		:	AT(LOADADDR (.preinit_array) + SIZEOF (.preinit_array)) { KEEP(*(.init_array)) }
	PROVIDE (__init_array_end = .);
	PROVIDE (__fini_array_start = .);
	.fini_array		:	AT(LOADADDR (.init_array) + SIZEOF (.init_array)) { KEEP(*(.fini_array)) }
	PROVIDE (__fini_array_end = .);
	.ctors		:	AT(LOADADDR (.fini_array) + SIZEOF (.fini_array)) { KEEP(*crtbegin*.o(.ctors)) KEEP(*(EXCLUDE_FILE (*crtend*.o ) .ctors)) KEEP(*(SORT(.ctors.*))) KEEP(*(.ctors)) }
	.dtors		:	AT(LOADADDR (.ctors) + SIZEOF (.ctors)) { KEEP(*crtbegin*.o(.dtors)) KEEP(*(EXCLUDE_FILE (*crtend*.o ) .dtors)) KEEP(*(SORT(.dtors.*))) KEEP(*(.dtors)) }
	.jcr		:	AT(LOADADDR (.dtors) + SIZEOF (.dtors)) { KEEP(*(.jcr)) }
	.data.rel.ro		:	AT(LOADADDR (.jcr) + SIZEOF (.jcr)) { *(.data.rel.ro.local) *(.data.rel.ro*) }
	.dynamic		:	AT(LOADADDR (.data.rel.ro) + SIZEOF (.data.rel.ro)) { *(.dynamic) }
	.data		:	AT(LOADADDR (.dynamic) + SIZEOF (.dynamic)) { *(.data .data.* .gnu.linkonce.d.*) KEEP(*(.gnu.linkonce.d.*personality*)) SORT(CONSTRUCTORS) . = ALIGN(8); }
	.data1		:	AT(LOADADDR (.data) + SIZEOF (.data)) { *(.data1) . = ALIGN(8); }
	. = ALIGN(4);
	.got		:	AT(LOADADDR (.data1) + SIZEOF (.data1)) { *(.got.plt) *(.got) }
	.sdata_d		:	AT(LOADADDR (.got) + SIZEOF (.got)) { *(.sdata_d .sdata_d.*) }
	.sdata_w		:	AT(LOADADDR (.sdata_d) + SIZEOF (.sdata_d)) { *(.sdata_w .sdata_w.*) }
	.sdata_h		:	AT(LOADADDR (.sdata_w) + SIZEOF (.sdata_w)) { *(.sdata_h .sdata_h.*) }
	.sdata_b		:	AT(LOADADDR (.sdata_h) + SIZEOF (.sdata_h)) { *(.sdata_b .sdata_b.*) }
	.sdata_f		:	AT(LOADADDR (.sdata_b) + SIZEOF (.sdata_b)) { *(.sdata_f .sdata_f.*) }
	_edata = .;
	PROVIDE (edata = .);
	__bss_start = .;
	PROVIDE (__sbss_start = .);
	PROVIDE (___sbss_start = .);
	.sbss_f		:	AT(ADDR(.sbss_f))	{ *(.sbss_f .sbss_f.*) *(.scommon_f .scommon_f.*) }
	.sbss_b		:	AT(ADDR(.sbss_b))	{ *(.sbss_b .sbss_b.*) *(.scommon_b .scommon_b.*) . = ALIGN(2); }
	.sbss_h		:	AT(ADDR(.sbss_h))	{ *(.sbss_h .sbss_h.*) *(.scommon_h .scommon_h.*) . = ALIGN(4); }
	.sbss_w		:	AT(ADDR(.sbss_w))	{ *(.sbss_w .sbss_w.*) *(.scommon_w .scommon_w.*) *(.dynsbss) *(.scommon) . = ALIGN(8); }
	.sbss_d		:	AT(ADDR(.sbss_d))	{ *(.sbss_d .sbss_d.*) *(.scommon_d .scommon_d.*) }
	.bss		:	AT(ADDR(.bss))	{ *(.dynbss) *(.bss .bss.* .gnu.linkonce.b.*) *(COMMON) . = ALIGN(32 / 8); }
	PROVIDE (__sbss_end = .);
	PROVIDE (___sbss_end = .);
	. = ALIGN(32 / 8);
	_end = .;
	. = DATA_SEGMENT_END(.);
	PROVIDE (end = .);
	EXEC_DATA_size = . - EXEC_DATA_BEGIN;
	. = 0x00086B50;
	EXEC_RAM_1_BEGIN = .;
	PROVIDE (_stack = 0x00086F50);
	EXEC_RAM_1_size = . - EXEC_RAM_1_BEGIN;
	OPTS_RAM_size = LOADADDR(.sdata_f) + SIZEOF(.sdata_f) - OPTS_RAM_BEGIN;
	sag_lma = 0x00006FC0 ;
	. = 0x00006FC0;
	DEBUG_REGION_BEGIN = .;
	.signature	.	:	AT(sag_lma)	{ KEEP(*(.signature)) }
	DEBUG_REGION_size = . - DEBUG_REGION_BEGIN;
	sag_lma = 0x48000000 ;
	FLH_IRAM_BEGIN = sag_lma;
	. = 0x48000000;
	FPU_SEQUENCE_BEGIN = .;
	.flh_iram_fpu.data	.	:	AT(sag_lma)	{ *(.flh_iram_fpu.data) }
	FPU_SEQUENCE_size = . - FPU_SEQUENCE_BEGIN;
	FLH_IRAM_size = LOADADDR(.flh_iram_fpu.data) + SIZEOF(.flh_iram_fpu.data) - FLH_IRAM_BEGIN;
	.stab	0 : { *(.stab) }
	.stabstr	0 : { *(.stabstr) }
	.stab.excl	0 : { *(.stab.excl) }
	.stab.exclstr	0 : { *(.stab.exclstr) }
	.stab.index	0 : { *(.stab.index) }
	.stab.indexstr	0 : { *(.stab.indexstr) }
	.note.nds32	0 : { *(.note.nds32) *(.note.nds32.*) }
	.comment	0 : { *(.comment) }
	.debug	0 : { *(.debug) }
	.line	0 : { *(.line) }
	.debug_srcinfo	0 : { *(.debug_srcinfo) }
	.debug_sfnames	0 : { *(.debug_sfnames) }
	.debug_aranges	0 : { *(.debug_aranges) }
	.debug_pubnames	0 : { *(.debug_pubnames) }
	.debug_info	0 : { *(.debug_info .gnu.linkonce.wi.*) }
	.debug_abbrev	0 : { *(.debug_abbrev) }
	.debug_line	0 : { *(.debug_line) }
	.debug_frame	0 : { *(.debug_frame) }
	.debug_str	0 : { *(.debug_str) }
	.debug_loc	0 : { *(.debug_loc) }
	.debug_macinfo	0 : { *(.debug_macinfo) }
	.debug_weaknames	0 : { *(.debug_weaknames) }
	.debug_funcnames	0 : { *(.debug_funcnames) }
	.debug_typenames	0 : { *(.debug_typenames) }
	.debug_varnames	0 : { *(.debug_varnames) }
}
ASSERT(EXEC_DATA_size <= 0x00002000, OVERFLOW);
ASSERT(EXEC_RAM_1_size <= 0x400, OVERFLOW);
ASSERT(OPTS_RAM_size <= 0x00006FC0, OVERFLOW);
ASSERT(DEBUG_REGION_size <= 0x40, OVERFLOW);
ASSERT(FPU_SEQUENCE_size <= 0x1C00, OVERFLOW);
ASSERT(FLH_IRAM_size <= 0x00002000, OVERFLOW);
