/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  hal_cop0.h                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _HAL_COP0_H_
#define _HAL_COP0_H_

#include "hal/cop0/hal_cop0_reg.h"
#include "hal/cop0/hal_cop0_types.h"

#ifdef __XTENSA__
#include <xtensa/tie/PS5008_Xtensa0_M6.h>
#endif




#define _inb(addr)          (*(volatile U8 *)(addr))
#define _inw(addr)          (*(volatile U16 *)(addr))
#define _inl(addr)          (*(volatile U32 *)(addr))
#define _inll(addr)         (*(volatile U64 *)(addr))

#define _outb(data, addr)   (*(volatile U8  *)(addr) = (data))
#define _outw(data, addr)   (*(volatile U16 *)(addr) = (data))
#define _outl(data, addr)   (*(volatile U32 *)(addr) = (data))
#define _outll(data, addr)  (*(volatile U64 *)(addr) = (data))


/*
 * macros
 */

#define HAL_COP0_BBMP_EN()              (r32_COP0[CREGL_CR_BBMP_EN] |= CREG_CR_BBMP_EN_BIT)
#define HAL_COP0_BBMP_DISABLE()         (r32_COP0[CREGL_CR_BBMP_EN] &= ~CREG_CR_BBMP_EN_BIT)
#define HAL_COP0_CHK_OPT_LOAD           (0xDEADBEEF)
#define HAL_COP0_CHK_OPT_RUN            (0x01234567)
#define HAL_CHK_VER_L                   (*(volatile U16 *)(FLASH_IRAM_BASE))
#define HAL_CHK_VER_H                   (*(volatile U16 *)(FLASH_IRAM_BASE + 2))

/*
 * public prototypes
 *
 */

#ifdef WIN32

BOOL win32_hal_cop0_tie_empty(void);
void win32_hal_cop0_tieout_block(void *data);
void win32_hal_cop0_tiein_block(void *cmd);
BOOL win32_hal_cop0_tiein_nonblock(void *cmd);

#define HAL_COP0_TIE_EMPTY()                    win32_hal_cop0_tie_empty()
#define HAL_COP0_TIE_OUT_BLOCK(data)            win32_hal_cop0_tieout_block(data)
#define HAL_COP0_TIE_IN_BLOCK(cmd)              win32_hal_cop0_tiein_block(cmd)
#define HAL_COP0_TIE_IN_NONBLOCK(cmd)           win32_hal_cop0_tiein_nonblock(cmd)

#else

#define HAL_COP0_TIE_EMPTY()                    COP0_DATA_QEMPTY()
#define HAL_COP0_TIE_OUT_BLOCK(data)            (*(reg64 *)(data) = COP0_DATA_POP_BLOCK())
#define HAL_COP0_TIE_IN_BLOCK(cmd)              COP0_CMD_PUSH_BLOCK(*((reg64 *)(cmd)))
#define HAL_COP0_TIE_IN_NONBLOCK(cmd)           ((BOOL)COP0_CMD_PUSH_NONBLOCK(*((reg64 *)(cmd))))

#endif


/*
 * COP0 VP RAM Information
 */
#define COP0_VP_INFO_TAGx(x)    (x + 1)
enum {
	COP0_VP_INFO_OPT_STS = 0,
	COP0_VP_INFO_TAG0,
	COP0_VP_INFO_TAG1,
	COP0_VP_INFO_TAG2,
	COP0_VP_INFO_TAG3
};

#define COP0_VP_INFO_NOR_CQ_RSP (BIT16)

/*
 *
 */

void hal_cop0_init(PCA_RULE_STRUCT_PTR pca_rule, U32 flh_typ, U32 cop0_mt_zone_off, U32 cop0_mt_zone_len, U32 cop0_l4k_zone_off, U32 iram_axi_base);
void hal_cop0_init_opt_master_andes_core(void);
void hal_cop0_init_opt_master(void);
void hal_cop0_set_bbmp_direct_address(U32 baddr, U32 size);
void hal_cop0_set_stall(U32 stall);
void hal_cop0_reset_stall(U32 stall);
BOOL hal_cop0_chk_stall_rdy(U32 stall);
void hal_cop0_set_cq_msg(U8 err_frm, U8 err_sts, U8 mt_idx);
U32  hal_cop0_get_vp_info(U8 mt_idx, U8 info);
void hal_cop0_set_vp_info(U32 vp_info, U8 mt_idx, U8 info);
void hal_cop0_update_vp_info_opt_status(U8 mt_index, U8 opt_status);
void hal_cop0_update_vp_info_tag(U8 mt_index, U8 node, U8 err_info);
U8 hal_cop0_get_page_vld(U8 mt_idx);


#endif /* _HAL_COP0_H_ */

