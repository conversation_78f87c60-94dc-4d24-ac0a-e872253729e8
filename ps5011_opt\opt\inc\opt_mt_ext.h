/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_mt_ext.h                                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_MT_EXT_H_
#define _OPT_MT_EXT_H_


#include "misc/types.h"
#include "opt_main.h"
#include "opt_arch.h"
#include "opt_global.h"
#include "fpu.h"
#include "opt_assert.h"


#ifdef _OPT_MT_EXT_C_
#define EXTERN
#else
#define EXTERN extern
#endif

EXTERN void mt_ext_set_fpu_chk_true_ready(void);
EXTERN void mt_ext_set_fpu_cache_ready_chk_cur_status(void);
EXTERN void mt_ext_set_fpu_cache_ready_chk_pre_status(void);



#undef EXTERN
#endif /* #ifndef _OPT_MT_EXT_H_ */
