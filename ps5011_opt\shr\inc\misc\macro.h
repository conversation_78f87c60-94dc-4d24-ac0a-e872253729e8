/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  macro.h                                                               */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _MACRO_H_
#define _MACRO_H_


/*
 *  provide the global macro definition
 */

#ifdef WIN32
#define STRINGIZE_HELPER(x) #x
#define STRINGIZE(x) STRINGIZE_HELPER(x)
#define WARNING(desc) message(__FILE__ "(" STRINGIZE(__LINE__) ") : Warning: " #desc)
// usage:
// #pragma WARNING(FIXME: Code removed because...)
#endif


/*
 * check struct size @ compile time
 *
 */
#define _CHK_STRUCT_SIZE_EQU(x, y)                  char _dummy_equ_##x[1 - (2 *(sizeof(x) != (y)))]
#define _CHK_STRUCT_SIZE_LESS_AND_EQU(x, y)         char _dummy_less_equ_##x[1 - (2 *(sizeof(x) > (y)))]
#define _CHK_STRUCT_SIZE_ALIGN(x, y)                char _dummy_align_##x[1 - (2 *(((sizeof(x) & ((y) - 1)) != 0)))]



/*
 * please modify your source insight C token macro document before using this macro
 * open [Start Menu]\Documents\Source Insight\C.tom and append a macro _SI(x) into that file.
 * In C source code, use the following macro to let source insight parser ignore your keyword
 *
 * for example:
 * #define ALIGN_DCACHE __attribute__((aligned(XCHAL_DCACHE_LINESIZE)))
 *
 * U32 gMyVar _SI(ALIGN_DCACHE);
 *
 */
#define _SI(X) X


/*
 * use _SI(DATA_ADDR_ALIGN()) to align the data address
 *
 * for example: U32 my_data _SI(DATA_ADDR_ALIGN(8));
 */
#define DATA_ADDR_ALIGN(byte) __attribute__ ((aligned(byte)))


/*
 * use _SI(ICODE) to relocate the function to icode (DDR)
 * use _SI(DCODE) to relocate the data to dcode (DDR)
 *
 * for example: U32 foo _SI(DCODE);
 *          or: U32 my_func() _SI(ICODE);
 */
#if HW == HW_MODEL
#define ICODE
#define DCODE
#else
#define ICODE __attribute__((section(".icode.text")))
#define DCODE __attribute__((section(".dcode.data")))
#endif

/*
 * use _SI(ICCM1) to relocate the function to iccm1 (0x5C0E_0000)
 *
 * for example: U32 my_func() _SI(ICCM1);
 */
#if ICCM1_32K
#ifdef WIN32
#define ICCM1
#else /* XTENSA or ANDES */
#ifdef __XTENSA__
#define ICCM1 __attribute__ ((section(".iram1_0.text")))
#else /* ANDES */
#define ICCM1
#endif /* #ifdef __XTENSA__ */
#endif /* #ifdef WIN32 */
#else /* #if ICCM1_32K */
#define ICCM1
#endif /* #if ICCM1_32K */


/*
 *
 *
 */
#ifndef WIN32
#define _BANK(x)    __attribute__((section(x)))
#define _BANK0      _BANK(".icode0.text")
#define _BANK1      _BANK(".icode1.text")
#define _BANK2      _BANK(".icode2.text")
#define _BANK3      _BANK(".icode3.text")
#define _BANK4      _BANK(".icode4.text")
#define _BANK5      _BANK(".icode5.text")
#define _BANK6      _BANK(".icode6.text")
#define _BANK7      _BANK(".icode7.text")
#define _BANK8      _BANK(".icode8.text")
#define _BANK9      _BANK(".icode9.text")
#define _BANK10     _BANK(".icode10.text")
#define _BANK11     _BANK(".icode11.text")
#define _BANK12     _BANK(".icode12.text")
#define _BANK13     _BANK(".icode13.text")
#define _BANK14     _BANK(".icode14.text")
#define _BANK15     _BANK(".icode15.text")
#else
#define _BANK0
#define _BANK1
#define _BANK2
#define _BANK3
#define _BANK4
#define _BANK5
#define _BANK6
#define _BANK7
#define _BANK8
#define _BANK9
#define _BANK10
#define _BANK11
#define _BANK12
#define _BANK13
#define _BANK14
#define _BANK15
#endif


/*
 *  generate a bit mask
 *  for example: U32 foo = BITMSK(4, 0); will get foo = 0x0F;
 *           or: U32 foo = BITMSK(4, 4); will get foo = 0xF0;
 */
#define BITMSK(LENGTH, OFFSET) ((~((~0LL)<<(LENGTH)))<<(OFFSET))


#define WAIT_REG(REG, MASK, VAL) while (((REG) & (MASK)) != (VAL))

/*
 *
 *
 */
#ifdef WIN32
#define _V
#else
#define _V          volatile
#endif




//================================ Value Macros ===============================

#define BIT_MASK(x)             (((U32)1 << (x)) - 1)
#define REG_FIELD(e, s, x)      ((x & BIT_MASK((e - s + 1))) << s)
#define GET_VALUE(v, s, l)      (((v) >> (s)) & BIT_MASK(l)) //v: value, s: shift, l: len


#define BIT(n)  (1 << (n))

#define MARK(x,y)               (x |= y)
#define UNMARK(x,y)             (x &= (~y))

#define BIT0    BIT(0)
#define BIT1    BIT(1)
#define BIT2    BIT(2)
#define BIT3    BIT(3)
#define BIT4    BIT(4)
#define BIT5    BIT(5)
#define BIT6    BIT(6)
#define BIT7    BIT(7)
#define BIT8    BIT(8)
#define BIT9    BIT(9)
#define BIT10   BIT(10)
#define BIT11   BIT(11)
#define BIT12   BIT(12)
#define BIT13   BIT(13)
#define BIT14   BIT(14)
#define BIT15   BIT(15)
#define BIT16   BIT(16)
#define BIT17   BIT(17)
#define BIT18   BIT(18)
#define BIT19   BIT(19)
#define BIT20   BIT(20)
#define BIT21   BIT(21)
#define BIT22   BIT(22)
#define BIT23   BIT(23)
#define BIT24   BIT(24)
#define BIT25   BIT(25)
#define BIT26   BIT(26)
#define BIT27   BIT(27)
#define BIT28   BIT(28)
#define BIT29   BIT(29)
#define BIT30   BIT(30)
#define BIT31   BIT(31)
#define BIT32 (((U64)0x01) << 32)
#define BIT33 (((U64)0x01) << 33)
#define BIT34 (((U64)0x01) << 34)
#define BIT35 (((U64)0x01) << 35)
#define BIT36 (((U64)0x01) << 36)
#define BIT37 (((U64)0x01) << 37)
#define BIT38 (((U64)0x01) << 38)
#define BIT39 (((U64)0x01) << 39)
#define BIT40 (((U64)0x01) << 40)
#define BIT41 (((U64)0x01) << 41)
#define BIT42 (((U64)0x01) << 42)
#define BIT43 (((U64)0x01) << 43)
#define BIT44 (((U64)0x01) << 44)
#define BIT45 (((U64)0x01) << 45)
#define BIT46 (((U64)0x01) << 46)
#define BIT47 (((U64)0x01) << 47)
#define BIT48 (((U64)0x01) << 48)
#define BIT49 (((U64)0x01) << 49)
#define BIT50 (((U64)0x01) << 50)
#define BIT51 (((U64)0x01) << 51)
#define BIT52 (((U64)0x01) << 52)
#define BIT53 (((U64)0x01) << 53)
#define BIT54 (((U64)0x01) << 54)
#define BIT55 (((U64)0x01) << 55)
#define BIT56 (((U64)0x01) << 56)
#define BIT57 (((U64)0x01) << 57)
#define BIT58 (((U64)0x01) << 58)
#define BIT59 (((U64)0x01) << 59)
#define BIT60 (((U64)0x01) << 60)
#define BIT61 (((U64)0x01) << 61)
#define BIT62 (((U64)0x01) << 62)
#define BIT63 (((U64)0x01) << 63)

#if 1
#define SET_BIT0    BIT(0)
#define SET_BIT1    BIT(1)
#define SET_BIT2    BIT(2)
#define SET_BIT3    BIT(3)
#define SET_BIT4    BIT(4)
#define SET_BIT5    BIT(5)
#define SET_BIT6    BIT(6)
#define SET_BIT7    BIT(7)
#define SET_BIT8    BIT(8)
#define SET_BIT9    BIT(9)
#define SET_BIT10   BIT(10)
#define SET_BIT11   BIT(11)
#define SET_BIT12   BIT(12)
#define SET_BIT13   BIT(13)
#define SET_BIT14   BIT(14)
#define SET_BIT15   BIT(15)
#define SET_BIT16   BIT(16)
#define SET_BIT17   BIT(17)
#define SET_BIT18   BIT(18)
#define SET_BIT19   BIT(19)
#define SET_BIT20   BIT(20)
#define SET_BIT21   BIT(21)
#define SET_BIT22   BIT(22)
#define SET_BIT23   BIT(23)
#define SET_BIT24   BIT(24)
#define SET_BIT25   BIT(25)
#define SET_BIT26   BIT(26)
#define SET_BIT27   BIT(27)
#define SET_BIT28   BIT(28)
#define SET_BIT29   BIT(29)
#define SET_BIT30   BIT(30)
#define SET_BIT31   BIT(31)
#define SET_BIT32 BIT32
#define SET_BIT33 BIT33
#define SET_BIT34 BIT34
#define SET_BIT35 BIT35
#define SET_BIT36 BIT36
#define SET_BIT37 BIT37
#define SET_BIT38 BIT38
#define SET_BIT39 BIT39
#define SET_BIT40 BIT40
#define SET_BIT41 BIT41
#define SET_BIT42 BIT42
#define SET_BIT43 BIT43
#define SET_BIT44 BIT44
#define SET_BIT45 BIT45
#define SET_BIT46 BIT46
#define SET_BIT47 BIT47
#define SET_BIT48 BIT48
#define SET_BIT49 BIT49
#define SET_BIT50 BIT50
#define SET_BIT51 BIT51
#define SET_BIT52 BIT52
#define SET_BIT53 BIT53
#define SET_BIT54 BIT54
#define SET_BIT55 BIT55
#define SET_BIT56 BIT56
#define SET_BIT57 BIT57
#define SET_BIT58 BIT58
#define SET_BIT59 BIT59
#define SET_BIT60 BIT60
#define SET_BIT61 BIT61
#define SET_BIT62 BIT62
#define SET_BIT63 BIT63


#define CHK_BIT0    BIT(0)
#define CHK_BIT1    BIT(1)
#define CHK_BIT2    BIT(2)
#define CHK_BIT3    BIT(3)
#define CHK_BIT4    BIT(4)
#define CHK_BIT5    BIT(5)
#define CHK_BIT6    BIT(6)
#define CHK_BIT7    BIT(7)
#define CHK_BIT8    BIT(8)
#define CHK_BIT9    BIT(9)
#define CHK_BIT10   BIT(10)
#define CHK_BIT11   BIT(11)
#define CHK_BIT12   BIT(12)
#define CHK_BIT13   BIT(13)
#define CHK_BIT14   BIT(14)
#define CHK_BIT15   BIT(15)
#define CHK_BIT16   BIT(16)
#define CHK_BIT17   BIT(17)
#define CHK_BIT18   BIT(18)
#define CHK_BIT19   BIT(19)
#define CHK_BIT20   BIT(20)
#define CHK_BIT21   BIT(21)
#define CHK_BIT22   BIT(22)
#define CHK_BIT23   BIT(23)
#define CHK_BIT24   BIT(24)
#define CHK_BIT25   BIT(25)
#define CHK_BIT26   BIT(26)
#define CHK_BIT27   BIT(27)
#define CHK_BIT28   BIT(28)
#define CHK_BIT29   BIT(29)
#define CHK_BIT30   BIT(30)
#define CHK_BIT31   BIT(31)
#define CHK_BIT32 BIT32
#define CHK_BIT33 BIT33
#define CHK_BIT34 BIT34
#define CHK_BIT35 BIT35
#define CHK_BIT36 BIT36
#define CHK_BIT37 BIT37
#define CHK_BIT38 BIT38
#define CHK_BIT39 BIT39
#define CHK_BIT40 BIT40
#define CHK_BIT41 BIT41
#define CHK_BIT42 BIT42
#define CHK_BIT43 BIT43
#define CHK_BIT44 BIT44
#define CHK_BIT45 BIT45
#define CHK_BIT46 BIT46
#define CHK_BIT47 BIT47
#define CHK_BIT48 BIT48
#define CHK_BIT49 BIT49
#define CHK_BIT50 BIT50
#define CHK_BIT51 BIT51
#define CHK_BIT52 BIT52
#define CHK_BIT53 BIT53
#define CHK_BIT54 BIT54
#define CHK_BIT55 BIT55
#define CHK_BIT56 BIT56
#define CHK_BIT57 BIT57
#define CHK_BIT58 BIT58
#define CHK_BIT59 BIT59
#define CHK_BIT60 BIT60
#define CHK_BIT61 BIT61
#define CHK_BIT62 BIT62
#define CHK_BIT63 BIT63

#define CLR_BIT0  (~BIT0)
#define CLR_BIT1  (~BIT1)
#define CLR_BIT2  (~BIT2)
#define CLR_BIT3  (~BIT3)
#define CLR_BIT4  (~BIT4)
#define CLR_BIT5  (~BIT5)
#define CLR_BIT6  (~BIT6)
#define CLR_BIT7  (~BIT7)
#define CLR_BIT8  (~BIT8)
#define CLR_BIT9  (~BIT9)
#define CLR_BIT10 (~BIT10)
#define CLR_BIT11 (~BIT11)
#define CLR_BIT12 (~BIT12)
#define CLR_BIT13 (~BIT13)
#define CLR_BIT14 (~BIT14)
#define CLR_BIT15 (~BIT15)
#define CLR_BIT16 (~BIT16)
#define CLR_BIT17 (~BIT17)
#define CLR_BIT18 (~BIT18)
#define CLR_BIT19 (~BIT19)
#define CLR_BIT20 (~BIT20)
#define CLR_BIT21 (~BIT21)
#define CLR_BIT22 (~BIT22)
#define CLR_BIT23 (~BIT23)
#define CLR_BIT24 (~BIT24)
#define CLR_BIT25 (~BIT25)
#define CLR_BIT26 (~BIT26)
#define CLR_BIT27 (~BIT27)
#define CLR_BIT28 (~BIT28)
#define CLR_BIT29 (~BIT29)
#define CLR_BIT30 (~BIT30)
#define CLR_BIT31 (~BIT31)
#define CLR_BIT32 (~BIT32)
#define CLR_BIT33 (~BIT33)
#define CLR_BIT34 (~BIT34)
#define CLR_BIT35 (~BIT35)
#define CLR_BIT36 (~BIT36)
#define CLR_BIT37 (~BIT37)
#define CLR_BIT38 (~BIT38)
#define CLR_BIT39 (~BIT39)
#define CLR_BIT40 (~BIT40)
#define CLR_BIT41 (~BIT41)
#define CLR_BIT42 (~BIT42)
#define CLR_BIT43 (~BIT43)
#define CLR_BIT44 (~BIT44)
#define CLR_BIT45 (~BIT45)
#define CLR_BIT46 (~BIT46)
#define CLR_BIT47 (~BIT47)
#define CLR_BIT48 (~BIT48)
#define CLR_BIT49 (~BIT49)
#define CLR_BIT50 (~BIT50)
#define CLR_BIT51 (~BIT51)
#define CLR_BIT52 (~BIT52)
#define CLR_BIT53 (~BIT53)
#define CLR_BIT54 (~BIT54)
#define CLR_BIT55 (~BIT55)
#define CLR_BIT56 (~BIT56)
#define CLR_BIT57 (~BIT57)
#define CLR_BIT58 (~BIT58)
#define CLR_BIT59 (~BIT59)
#define CLR_BIT60 (~BIT60)
#define CLR_BIT61 (~BIT61)
#define CLR_BIT62 (~BIT62)
#define CLR_BIT63 (~BIT63)
#endif

//=========================== End: Value Macros ===============================






















#define BC_512B         0x0200
#define BC_4KB          0x1000
#define BC_8KB          0x2000
#define BC_12KB         0x3000
#define BC_16KB         0x4000
#define BC_32KB         0x8000
#define BC_256KB        0x00040000
#define BC_512KB        0x00080000
#define BC_1MB          0x00100000
#define BC_16MB         0x01000000

#define BUF_ALL_VLD     0xFF







#endif // _MACRO_H_

