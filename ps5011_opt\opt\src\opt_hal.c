/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_hal.c                                                             */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_HAL_C_

#include "opt_hal.h"
#include "opt_micron_read_disturb.h"

#if (NEW_IWL_EN)
inline void hal_optq_store_readinfo_IWL(OPT_JOB_STRUCT_PTR job)
{
	__ASM_VOLATILE_MEMORY__();
	U8 ubPlaneIndex;
	U16 uwUserDefine = job->user_define;
	COP0_PATCH_CMD_INFO_STRUCT *pReadInfo;
	pReadInfo = &read_info->ulReadInfo[gOptStruct.cur_que];
#if (READ_DISTURB_PRDH_EN)
	COP0_MICRON_STORE_STRUCT *pMicronStore;
	pMicronStore = &gpMicronPatchInfo->Info[gOptStruct.cur_que];
#endif /* (READ_DISTURB_PRDH_EN) */
	if (0 == job->ubIWLPlaneIdx) {
		pReadInfo->uwPlaneVld_BM = job->plane_vld;
		pReadInfo->cmdInfo.uwAll = ((M_OPT_GET_LOOKUP_BIN_VALUE(uwUserDefine) << 8) | (TRUE << 2) | mtq->dw1.bits.alu_sel);
#if (READ_DISTURB_PRDH_EN)
		pReadInfo->cmdInfo.bits.BlkType = M_OPT_GET_LOOKUP_PRDH_BLK_TYPE(uwUserDefine);
		pReadInfo->cmdInfo.bits.btByPassReadCntHandle = M_OPT_CHECK_LOOKUP_PRDH_BYPASS_READ_CNT_HANDLE(uwUserDefine);
		pMicronStore->ulFWPCA[0] = job->pca;
#endif /* (READ_DISTURB_PRDH_EN) */
	}

	pReadInfo->ulFSAGroup[0][job->ubIWLPlaneIdx++] = mtq->dw10_iFSA0;

	__ASM_VOLATILE_MEMORY__();
}
#endif  /* (NEW_IWL_EN) */
inline void hal_optq_store_readinfo(OPT_JOB_STRUCT_PTR job, U8 cache_cmd)
{
	__ASM_VOLATILE_MEMORY__();
	U8 ubPlaneIndex;
	U32 uliFSA[4];
	U16 uwUserDefine = job->user_define;
	COP0_PATCH_CMD_INFO_STRUCT *pReadInfo;
	pReadInfo = &read_info->ulReadInfo[gOptStruct.cur_que];
#if (READ_DISTURB_PRDH_EN)
	COP0_MICRON_STORE_STRUCT *pMicronStore;
	pMicronStore = &gpMicronPatchInfo->Info[gOptStruct.cur_que];
#endif /* (READ_DISTURB_PRDH_EN) */

	if (OPT_MACRO_CMD_STATE_RUN_CACHE_END == job->macro_state) {	// 0x3F MT那筆僅將前一次的current搬到previous
		for (ubPlaneIndex = 0; ubPlaneIndex < 4; ubPlaneIndex++)  {
			pReadInfo->ulFSAGroup[0][ubPlaneIndex] = pReadInfo->ulFSAGroup[1][ubPlaneIndex];
			pReadInfo->ulFSAGroup[1][ubPlaneIndex] = INVALID_IFSA;
		}
		pReadInfo->ubPlaneVld_BM[0] = pReadInfo->ubPlaneVld_BM[1];
		pReadInfo->ubPlaneVld_BM[1] = INVALID_PLANE_BM;
#if (READ_DISTURB_PRDH_EN)
		pMicronStore->ulFWPCA[0] = pMicronStore->ulFWPCA[1];
		pMicronStore->ulFWPCA[1] = INVALID_IFSA;
#endif /* (READ_DISTURB_PRDH_EN) */
	}
	else {
		if (cache_cmd && (pReadInfo->ulFSAGroup[cache_cmd][0]) != INVALID_IFSA) {	// cache時將前一次的current搬到previous
			for (ubPlaneIndex = 0; ubPlaneIndex < 4; ubPlaneIndex++)  {
				pReadInfo->ulFSAGroup[0][ubPlaneIndex] = pReadInfo->ulFSAGroup[1][ubPlaneIndex];
			}
			pReadInfo->ubPlaneVld_BM[0] = pReadInfo->ubPlaneVld_BM[1];
#if (READ_DISTURB_PRDH_EN)
			pMicronStore->ulFWPCA[0] = pMicronStore->ulFWPCA[1];
#endif /* (READ_DISTURB_PRDH_EN) */
		}

		if (READ_CMD_DMA_SYNC_CNT_EN) {
			uliFSA[0] = mtq->dw10_iFSA0;
			uliFSA[1] = mtq->dw12_iFSA1;
			uliFSA[2] = mtq->dw13_iFSA2;
			uliFSA[3] = mtq->dw14_iFSA3;

			for (ubPlaneIndex = 0; ubPlaneIndex < 4; ubPlaneIndex++) {	// valid plane的iFSA往前搬記在前面
				if (ubPlaneIndex < job->cmd_cnt) {
					pReadInfo->ulFSAGroup[cache_cmd][ubPlaneIndex] = uliFSA[ubPlaneIndex];
				}
				else {	// 後面invalid plane都清成INVALID_IFSA
					pReadInfo->ulFSAGroup[cache_cmd][ubPlaneIndex] = INVALID_IFSA;
				}
			}
		}
		else {
			pReadInfo->ulFSAGroup[cache_cmd][0] = mtq->dw10_iFSA0;
			pReadInfo->ulFSAGroup[cache_cmd][1] = mtq->dw12_iFSA1;
			pReadInfo->ulFSAGroup[cache_cmd][2] = mtq->dw13_iFSA2;
			pReadInfo->ulFSAGroup[cache_cmd][3] = mtq->dw14_iFSA3;
		}

		if (FALSE == cache_cmd) {
			for (ubPlaneIndex = 0; ubPlaneIndex < 4; ubPlaneIndex++) {	// 重新一段read command sequence時將cache階段時 記下的iFSA清為 INVALID_IFSA
				pReadInfo->ulFSAGroup[1][ubPlaneIndex] = INVALID_IFSA;
			}
			pReadInfo->ubPlaneVld_BM[1] = INVALID_PLANE_BM;
#if (READ_DISTURB_PRDH_EN)
			pMicronStore->ulFWPCA[1] = INVALID_IFSA;
#endif /* (READ_DISTURB_PRDH_EN) */
		}

		pReadInfo->ubPlaneVld_BM[cache_cmd] = job->plane_vld;
#if (READ_DISTURB_PRDH_EN)
		pMicronStore->ulFWPCA[cache_cmd] = job->pca;
#endif /* (READ_DISTURB_PRDH_EN) */
		pReadInfo->cmdInfo.uwAll = 0;
		pReadInfo->cmdInfo.bits.ubALURule = mtq->dw1.bits.alu_sel;
		pReadInfo->cmdInfo.bits.BinValue = job->ubBinValue;
#if (READ_DISTURB_PRDH_EN)
		pReadInfo->cmdInfo.bits.BlkType = M_OPT_GET_LOOKUP_PRDH_BLK_TYPE(uwUserDefine);
		pReadInfo->cmdInfo.bits.btByPassReadCntHandle = M_OPT_CHECK_LOOKUP_PRDH_BYPASS_READ_CNT_HANDLE(uwUserDefine);
#endif /* (READ_DISTURB_PRDH_EN) */
	}
#if (!NEW_IWL_EN)
	if (job->ubIWLEn) {
		pReadInfo->cmdInfo.bits.btIsIWLRead = TRUE;
	}
	else {
		pReadInfo->cmdInfo.bits.btIsIWLRead = FALSE;
	}
#endif /* (!NEW_IWL_EN) */
	__ASM_VOLATILE_MEMORY__();
}

inline BOOL hal_optq_check_SwitchQueueCnt(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	if (gulSwitchQueue_BM & (BIT0 << ubQueueIdx)) {
		return TRUE;
	}
	else {
		return FALSE;
	}
}

inline BOOL hal_optq_check_ARM_stall_MTpool_req(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	// 1. N48 QLC Program 因OPTC深度不夠(12)seq 需要16(4PLANE * (L/U/X/T))
	// End Program的時候 實際上還沒做完完整的LUXT  導致會切QUEUE及STALL判斷，
	// 如果這時候Andes stall 成功 會讓Banking把Cell Program到一半的狀態清掉，因此加條件不能讓ARM STALL
	// 2. N28 Pre-read Disable 因OPTC深度不夠(12)seq 需要16(4PLANE * (L/U/X/T))，及需要POLL true ready在每個Page結束，
	// 所以每個PAGE type的最後一個PLANE都拉End Program(總共16個page，4個page type，分成4個一組)，導致會切QUEUE及STALL判斷，
	// 又因為Banking會強制把FLASH pre-read狀態清掉，因此加條件不能讓ARM STALL
	if ((FALSE == GET_PROG_LUXT_QUEUE(ubQueueIdx)) && (FALSE == GET_PREREAD_DISABLE_STATE_QUEUE(ubQueueIdx)) && (cpu_comm->uoARM_Stall_Req_BM & (BIT0 << ubQueueIdx))) {
		return TRUE;
	}
	else {
		return FALSE;
	}
}

inline BOOL hal_optq_check_stop_formMT_req(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	// FW兩種Case拉Stop Form MT，一種是patch init and (AOM IOR Patch)，一種是Program fail搬完要修改FSA的Flow，與stall request 處理方式一樣
	if ((FALSE == GET_PROG_LUXT_QUEUE(ubQueueIdx)) && (FALSE == GET_PREREAD_DISABLE_STATE_QUEUE(ubQueueIdx)) && (cpu_comm->uoARM_Stop_FormMT_Req_BM & (BIT0 << ubQueueIdx))) {
		return TRUE;
	}
	else {
		return FALSE;
	}
}

inline void hal_optq_clear_ARM_stall_MTpool_req(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoARM_Stall_Req_BM &= (~(BIT0 << ubQueueIdx));
	gulSwitchQueue_BM &=  (~(BIT0 << ubQueueIdx));
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_Andes_stop_formMT(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoAndes_Stop_FormMT_BM |= (BIT0 << ubQueueIdx);
	__ASM_VOLATILE_MEMORY__();
}

inline BOOL hal_optq_check_Andes_stop_formMT(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	if (cpu_comm->uoAndes_Stop_FormMT_BM & (BIT0 << ubQueueIdx)) {
		return TRUE;
	}
	else {
		return FALSE;
	}
}

inline void hal_optq_StopFormMT_ClearStallReq(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoAndes_Stop_FormMT_BM |= (BIT0 << ubQueueIdx);
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoARM_Stall_Req_BM &= (~(BIT0 << ubQueueIdx));
	gulSwitchQueue_BM &=  (~(BIT0 << ubQueueIdx));
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_StopFormMT_ClearStopFormMTReq(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoAndes_Stop_FormMT_BM |= (BIT0 << ubQueueIdx);
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoARM_Stop_FormMT_Req_BM &= (~(BIT0 << ubQueueIdx));
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_Andes_req_directly_stall_MTpool(U8 ubQueueIdx)
{
	__ASM_VOLATILE_MEMORY__();
	cpu_comm->uoAndes_Req_ARM_Directly_Stall_BM |= (BIT0 << ubQueueIdx);
	__ASM_VOLATILE_MEMORY__();
}


inline void hal_optq_wait_lookup_q_valid()
{
	__ASM_VOLATILE_MEMORY__();
	while ((OPTQB[OPTQB_LOOKUP_Q_VALID] & ELEMENT_VALID_MASK) != ELEMENT_VALID_MASK);
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_wait_lookup_q_convpage_valid()
{
	__ASM_VOLATILE_MEMORY__();
	while ((OPTQB[OPTQB_LOOKUP_Q_VALID] & ELEMENT_RS_VALID_MASK) != ELEMENT_RS_VALID_MASK);
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_wait_curq_valid()
{
	__ASM_VOLATILE_MEMORY__();
	while (OPTQB[OPTQB_CURQ_VALID] != ELEMENT_VALID_MASK);
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_ps_gmp_result_set(U8 result_idx)
{
	__ASM_VOLATILE_MEMORY__();
	OPTQB[R8_OPT_GMP_RESULT_SEL] = (OPT_GMP_RESULT_EXTRACT | result_idx);
	__ASM_VOLATILE_MEMORY__();
	while (OPTQB[R8_OPT_GMP_RESULT_EXTRACT_ST] != IDLE);          // 等HW idle
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_internal_pop(U8 optidx)
{
	__ASM_VOLATILE_MEMORY__();
	OPTQB[R8_OPT_PS_INTERNAL_POP] = optidx;
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_multi_pop()
{
	__ASM_VOLATILE_MEMORY__();
	OPTQB[R8_OPT_PS_MULTI_POP]  = 0;        // write any value will trigger HW to pop multiple OPT queue entries and also reorder LINK0 content.
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optq_q_pop()
{
	__ASM_VOLATILE_MEMORY__();
#if OPT_SUPPORT_DCCM_DEBUG_RECORD_Q_POP
	_WRITE_OPT_DCCM_PATH_DEBUG(Q_POP(OPTQL[OPTQ_PROC_Q_HEAD_PCA]));
	_WRITE_OPT_DCCM_PCA_DEBUG(OPTQ_OPT_HEAD_CMD_PTR);
#endif
	OPTQB[OPTQB_Q_POP] = 1;
	__ASM_VOLATILE_MEMORY__();
}

inline void hal_optmt_wait_form_mt()
{
	__ASM_VOLATILE_MEMORY__();
	while (OPTQB[OPTMTB_FORMMT_WAIT]);
	__ASM_VOLATILE_MEMORY__();
}

#if DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA

#if OPT_SUPPORT_DCCM_BEHAVIOR
inline void hal_optmt_trigger(void)
{
	__ASM_VOLATILE_MEMORY__();
#if (!MICRON_140S)
	_SET_WL_BYPASS_STATUS_OPT_STATUS();
	_SET_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_OPT_STATUS();
	_SET_WL_BYPASS_DISABLE_PREREAD_STATUS_OPT_STATUS();
#endif /* (!MICRON_140S) */
	__ASM_VOLATILE_MEMORY__();
	_WRITE_OPT_DCCM_BEHAVIOR((gOptStruct.cur_que << 24) | ((mtq->dw5.bits.pol_seq_sel) << 20) |
		((mtq->dw0.bits.busy) ? BIT19 : 0) |
		((mtd->dw2_mt_cfg1.bits.mt_hard_lock) ?    BIT18 : 0) |
		((mtd->dw1_dat.bits.nor_cq_rsp) ? BIT17 : 0) |
		((mtd->dw0_dat.bits.par_rls) ? BIT16 : 0) |
		((mtq->dw5.bits.fpu_ptr) << 0));
	__ASM_VOLATILE_MEMORY__();
	OPTQB[OPTMTB_TRIGGER] = 1;
	__ASM_VOLATILE_MEMORY__();
}

#else /* #if OPT_SUPPORT_DCCM_BEHAVIOR */
inline void hal_optmt_trigger(void)
{

	__ASM_VOLATILE_MEMORY__();
#if ((!MICRON_140S) && (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON))
	_SET_WL_BYPASS_STATUS_OPT_STATUS();
	_SET_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_OPT_STATUS();
	_SET_WL_BYPASS_DISABLE_PREREAD_STATUS_OPT_STATUS();
#endif /* (!MICRON_140S) */
#if DEBUG_DCCCM_BY_QUEUE
	if (((OPTCML[5] >> 16) & 0xFF) <= DEBUG_QUEUE_MAX) {
		_WRITE_OPT_DCCM_MT_DEBUG(OPTCML[5]);	//Fpu
		_WRITE_OPT_DCCM_MT_DEBUG(OPTCML[10]);	//FSA0
	}
#endif /* DEBUG_DCCCM_BY_QUEUE */
	__ASM_VOLATILE_MEMORY__();
	OPTQB[OPTMTB_TRIGGER] = 1;
	__ASM_VOLATILE_MEMORY__();
}

#endif /* #if OPT_SUPPORT_DCCM_BEHAVIOR */

#else /* #if DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA */
inline void hal_optmt_trigger()
{
	__ASM_VOLATILE_MEMORY__();
	_WRITE_OPT_DCCM_PCA_DEBUG(OPTCML[15]);
	__ASM_VOLATILE_MEMORY__();
#if (!MICRON_140S)
	_SET_WL_BYPASS_STATUS_OPT_STATUS();
	_SET_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_OPT_STATUS();
	_SET_WL_BYPASS_DISABLE_PREREAD_STATUS_OPT_STATUS();
#endif /* (!MICRON_140S) */
	__ASM_VOLATILE_MEMORY__();
	OPTQB[OPTMTB_TRIGGER] = 1;
	__ASM_VOLATILE_MEMORY__();
}
#endif /* #if DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA */

U8 hal_loopup_page_type()
{
	U8 ubLUM = 0;
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
	ubLUM = OPT_LOOKUP_PAGE_ADDR % 3;
#else
	ubLUM = OPT_LOOKUP_LMU_ADDR;
#endif
	return ubLUM;
}
