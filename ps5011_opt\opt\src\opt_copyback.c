/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_copyback.c                                                        */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_COPYBACK_C_

#if OPT_SUPPORT_INTERNAL_COPYBACK

#include "opt_main.h"
#include "opt_arch.h"
#include "opt_const.h"
#include "opt_hal.h"
#include "opt_mt_ext.h"
#include "opt_copyback.h"
#include "fpu.h"
#include "opt_debug.h"
#include "nds32_intrinsic.h"
#include "opt_api.h"

static inline BOOL opt_mt_group_copyback_read(OPT_JOB_STRUCT_PTR job, U32 s_pln_0, U32 s_pln_1, U32 s_pln_2, U32 s_pln_3);
static inline BOOL opt_mt_group_copyback_prog(OPT_JOB_STRUCT_PTR job, U32 t_pln_0, U32 t_pln_1, U32 t_pln_2, U32 t_pln_3, U8 transfer, U8 lmu);
static BOOL opt_mt_group_copyback_fun(OPT_JOB_STRUCT_PTR job, U8 lmu);


BOOL opt_macro_cmd_tlc_copyback(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job) //15
{
	if (job->macro_state == OPT_MACRO_CMD_STATE_INIT) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_CB_LOWER;
	}

	if (job->macro_state == OPT_MACRO_CMD_STATE_RUN_CB_LOWER) {
		if (opt_mt_group_copyback_fun(job, 0)) {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_CB_MIDDLE;
		}
	}

	if (job->macro_state == OPT_MACRO_CMD_STATE_RUN_CB_MIDDLE) {
		if (opt_mt_group_copyback_fun(job, 1)) {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_CB_UPPER;
		}
	}

	if (job->macro_state == OPT_MACRO_CMD_STATE_RUN_CB_UPPER) {
		if (opt_mt_group_copyback_fun(job, 2)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}
	return FALSE;
}

inline BOOL opt_mt_group_copyback_read(OPT_JOB_STRUCT_PTR job, U32 s_pln_0, U32 s_pln_1, U32 s_pln_2, U32 s_pln_3)// plane0, plane1, plane2, plane3
{
	volatile U16 fpu;

	_DBM2(_MSG_COPYBACK_, "opt_mt_group_copyback_read cac_cmd:%d ele:%x\n", 0, OPTQB[OPTQB_PROC_CMD_Q_ELEMENT_CNT]);
	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD          |
		OPTMTW_FORMMT_ATTR_SEL_TMP       |
		OPTMTW_FORMMT_FORM_ALL_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD     |
		OPTMTW_FORMMT_CMD_MT_TRIG;
#if (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC)
	fpu = gwFPU_slc_read[0][0];
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
	fpu = FPU_OFFSET(gFpuEntryList.fpu_entry_00_32_35_read_copyback);
#else /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
#error Nand flash type define error
#endif /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
	hal_optmt_wait_form_mt();

	//---------- MT TABLE ----------//
	mtq->dw7_misc_cfg_1.bits.fpu_ptr = fpu;
	//    mtq->dw1_raidecc_cfg.bits.alu_sel = (job->slc_mode);

	mtq->dw1_raidecc_cfg.bits.dis_udma = 1;

	mtq->dw11_iFSA0 = s_pln_0;
	mtq->dw12_iFSA1 = s_pln_1;
	mtq->dw13_iFSA2 = s_pln_2;
	mtq->dw14_iFSA3 = s_pln_3;

	mtq->dw0_ctrl_map.bits.pfa_int_en = 0;/*read command always should not fail, right?*/

	//---------- TRIGGER DATA ----------//
	mtd->dw1_dat.bits.nor_cq_rsp = 0;
	mtd->dw0_dat.bits.par_rls = 0;
	mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_RD;

	M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);

	// Check Trigger Data //
	_DBM(_MSG_MT_READ_CMD_, "Read cac_cmd\n");

	opt_print_TD_MTD();

	__ASM_VOLATILE_MEMORY__();
#if DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA
	//_WRITE_OPT_DCCM_PCA_DEBUG(OPTQL[OPTQ_PROC_Q_HEAD_PCA]);
	_WRITE_OPT_DCCM_PCA_DEBUG(s_pln_0);
#else
	_WRITE_OPT_DCCM_PCA_DEBUG(OPTCML[15]);
#endif
	OPTQB[OPTMTB_TRIGGER] = 1;
	__ASM_VOLATILE_MEMORY__();
	return TRUE;

}

inline BOOL opt_mt_group_copyback_prog(OPT_JOB_STRUCT_PTR job, U32 t_pln_0, U32 t_pln_1, U32 t_pln_2, U32 t_pln_3, U8 transfer, U8 lmu)// plane0, plane1, plane2, plane3
{
	U16 fpu;

	_DBM2(_MSG_COPYBACK_, "opt_mt_group_copyback_prog cac_cmd:%d ele:%x\n", 0, OPTQB[OPTQB_PROC_CMD_Q_ELEMENT_CNT]);
	hal_optq_wait_curq_valid();
	OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD |
		OPTMTW_FORMMT_ATTR_SEL_TMP |
		OPTMTW_FORMMT_FORM_ALL_PLANE_FSA |
		OPTMTW_FORMMT_FSA0_FROM_HEAD |
		OPTMTW_FORMMT_CMD_MT_TRIG;
#if (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC)
	fpu = gProg_trig_cmd_lmu_copyback_fpu[transfer][lmu];
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
	//    if(2 == transfer)//slc
	//    {
	//      fpu = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_85_11_10_copyback);
	//    }
	//    else//mlc
	{
		fpu = FPU_OFFSET(gFpuEntryList.fpu_entry_mlc_85_11_10_copyback);
	}

#else /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
#error Nand flash type define error
#endif /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
	hal_optmt_wait_form_mt();

	//---------- MT TABLE ----------//
	mtq->dw7_misc_cfg_1.bits.fpu_ptr = fpu;
	//    mtq->dw1_raidecc_cfg.bits.alu_sel = (job->slc_mode);
	mtq->dw1_raidecc_cfg.bits.dis_udma = 1;
	mtq->dw11_iFSA0 = t_pln_0;
	mtq->dw12_iFSA1 = t_pln_1;
	mtq->dw13_iFSA2 = t_pln_2;
	mtq->dw14_iFSA3 = t_pln_3;

	mtq->dw0_ctrl_map.bits.pfa_int_en = 1;

	//---------- TRIGGER DATA ----------//
#if (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC)
	if (2 == lmu)
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
	if (transfer)
#else /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
#error Nand flash type define error
#endif /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
	{
		//mtd->dw1_dat.bits.nor_cq_rsp = 1;/*tie-in decide it*/
		mtd->dw0_dat.bits.par_rls = 1;
	}
	else {
		mtd->dw1_dat.bits.nor_cq_rsp = 0;
		mtd->dw0_dat.bits.par_rls = 0;
	}
	mtd->dw0_dat.bits.opt_status = lmu;
	mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_NOT_RD;

	M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);

	// Check Trigger Data //
	_DBM(_MSG_MT_READ_CMD_, "Read cac_cmd\n");

	opt_print_TD_MTD();

	__ASM_VOLATILE_MEMORY__();
#if DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA
	//        _WRITE_OPT_DCCM_PCA_DEBUG(OPTQL[OPTQ_PROC_Q_HEAD_PCA]);
	_WRITE_OPT_DCCM_PCA_DEBUG(t_pln_0);
#else
	_WRITE_OPT_DCCM_PCA_DEBUG(OPTCML[15]);
#endif
	OPTQB[OPTMTB_TRIGGER] = 1;
	__ASM_VOLATILE_MEMORY__();
#if (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC)
	if (2 == lmu)
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
	if (transfer)
#else /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
#error Nand flash type define error
#endif /* (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) */
	{
		hal_optq_q_pop();
	}
	return TRUE;

}

BOOL opt_mt_group_copyback_fun(OPT_JOB_STRUCT_PTR job, U8 lmu)
{
	COP0_COPY_BACK_STRUCT_PTR cb;
	U8 plane_cnt;
	U8 pl_sbit;
	U32 pca;
	U8 transfer;

	//cpbk.plane_vld now is rsv, always all plane
#if 0
	plane_cnt = multi_plane_prog_1st_split_plane_cnt[((COPY_BACK_USER_DEF_STRUCT_PTR) & (job->user_define))->cpbk.plane_vld];
#else
	//TSB 1z and Micron b05a plane number is 2
	plane_cnt = 2;
#endif
	/*
	 * Two plane read and prog only take one MTQ space,
	 * because the FPU is all plane not single plane
	 * */
	if (_chk_other_cmd_mtq_free_cnt(gOptStruct.cur_que, 1 * plane_cnt * MT_1_MT_TRIG)) {
		return FALSE;
	}

	pl_sbit = OPT_CREG_PLANE_START_POINT;

	cb = (COP0_COPY_BACK_STRUCT_PTR)(OPT_D_CPBK_BASE + (U32)((COPY_BACK_USER_DEF_STRUCT_PTR) & (job->user_define))->cpbk.dccm_ofs);
	pca = cb->s_pca[lmu];

	pca &= (~(gubPlaneMask << OPT_CREG_PLANE_START_POINT));

	opt_mt_group_copyback_read(job, pca, pca | (1 << pl_sbit), pca | (2 << pl_sbit), pca | (3 << pl_sbit));
#if (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC)
	pca = cb->t_pca;
	transfer = (pca & gOptStruct.ulPCAMaskLMU) >> OPT_CREG_LMU_START_POINT; // First, Foggy, Fine
#elif (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_MLC)
	pca = cb->t_pca[lmu];
	if ((cb->s_pca[1] == 0xFFFFFFFF) && (cb->t_pca[1] == 0xFFFFFFFF)) {
		transfer = 1;
		job->macro_state = OPT_MACRO_CMD_STATE_DONE;
	}
	else {
		transfer = lmu;
	}

#else
#error Nand flash type define error
#endif

	opt_mt_group_copyback_prog(job, pca, pca | (1 << pl_sbit), pca | (2 << pl_sbit), pca | (3 << pl_sbit), transfer, lmu);

	return TRUE;
#if 0
	U32 *s_pca;
	U32 *t_pca;
	U8 transfer;
	U8 loop;
	U32 read_page_pln0 = 0, read_page_pln1 = 0, read_page_pln2 = 0, read_page_pln3 = 0;
	U32 prog_page_pln0 = 0, prog_page_pln1 = 0, prog_page_pln2 = 0, prog_page_pln3 = 0;
	//job -> user_define = 0;
	_DBM1(_MSG_COPYBACK_, "user_define: %x\n", job->user_define);


	s_pca = (U32 *)(job->user_define + OPT_D_CPBK_BASE);
	t_pca = (U32 *)(job->user_define + OPT_D_CPBK_BASE + 12);
	transfer = (*t_pca & gOptStruct.ulPCAMaskLMU) >> OPT_CREG_LMU_START_POINT; // First, Foggy, Fine
	_DBM1(_MSG_COPYBACK_, "transfer: %x\n", transfer);
	_DBM1(_MSG_COPYBACK_, "t_pca: %x\n", *t_pca);

	for (loop = 0; loop < 3; loop++) { // lower middle upper
		read_page_pln0 = *(s_pca + loop);
		read_page_pln1 = *(s_pca + loop) + (1 << OPT_CREG_PLANE_START_POINT);
		prog_page_pln0 = *t_pca;
		prog_page_pln1 = *t_pca + (1 << OPT_CREG_PLANE_START_POINT);
		if (OPT_COP0_PLANE_LENS > 1) {
			read_page_pln2 = *(s_pca + loop) + (2 << OPT_CREG_PLANE_START_POINT);
			read_page_pln3 = *(s_pca + loop) + (3 << OPT_CREG_PLANE_START_POINT);
			prog_page_pln2 = *t_pca + (2 << OPT_CREG_PLANE_START_POINT);
			prog_page_pln3 = *t_pca + (3 << OPT_CREG_PLANE_START_POINT);
		}

		_DBM1(_MSG_COPYBACK_, "copyback loop: %x\n", loop);
		//      _DBM2(_MSG_COPYBACK_, "read_page_pln0: %x, read_page_pln1: %x, \n", read_page_pln0, read_page_pln1);
		//      _DBM2(_MSG_COPYBACK_, "prog_page_pln0: %x, prog_page_pln1: %x, \n", prog_page_pln0, prog_page_pln1);
		opt_mt_group_copyback_read(job, read_page_pln0, read_page_pln1, read_page_pln2, read_page_pln3);
		opt_mt_group_copyback_prog(job, prog_page_pln0, prog_page_pln1, prog_page_pln2, prog_page_pln3, transfer, loop);
	}
	return TRUE;
#endif
}

#endif /* #if OPT_SUPPORT_INTERNAL_COPYBACK */

