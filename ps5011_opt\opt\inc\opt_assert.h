/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_assert.h                                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_ASSERT_H_
#define _OPT_ASSERT_H_

typedef struct assert_list   COP0_ASSERT_STRUCT, *COP0_ASSERT_STRUCT_PTR;


/*
ASSERT CODE:
General:0x0FXX
Critical:0x8FXX
*/
#define CRITICAL_ANDES_ASSERT_ERROR 	(0x00008000)

enum ASSERT_ERROR_TYPE { // Max error is 16 for each type
	ASSERT_BYTE_ALIGN_ERROR = 0x0F00, //End:0x1 Type: Critical
	ASSERT_FLOW_ERROR = 0x0F10, //End:0xF Type: Critical or General
	ASSERT_PCA_ERROR = 0x0F20, //End:0xB Type: Critical
	ASSERT_LOOKUP_PTR_ERROR = 0x0F30,  //End:0x7 Type: Critical
	ASSERT_RAID_ERROR = 0x0F40, //End:0x1 Type: Critical
	ASSERT_CODE_SIZE_OVERFLOW_ERROR = 0x0F50, //End:0x0 Type: Critical
	ASSERT_IOR_ERROR = 0x0F60, //End:0x7 Type: Critical or General
	ASSERT_WORDLINE_BYPASS_ERROR = 0x0F70,//End:0xF Type: Critical or General
	ASSERT_ARM_FW_FLOW_ERROR = 0x0F80,//End:0x1 Type: Critical
	ASSERT_END = 0x0FF0
};

struct assert_list {
	union {
		U32 ptn;
		struct {
			U16 error;
			U16 idx;
		};
	};
};

#define _setvalue(v,n)  \
    ({  \
        v = n;  \
    })




#if OPT_SUPPORT_ASSERT_DEBUG
#define OPT_ASSERT(ErrorCode, Condition)do{\
	if(Condition){\
		OPTErrorAssert(ErrorCode);\
	}\
}while(0)

#define OPT_CRITICAL_ASSERT(ErrorCode, Condition) do{\
	if(Condition){\
		OPTCriticalErrorAssert(CRITICAL_ANDES_ASSERT_ERROR|ErrorCode);\
	}\
}while(0)
#else
#define OPT_ASSERT(ErrorCode, Condition)
#define OPT_CRITICAL_ASSERT(ErrorCode, Condition)
#endif

#endif /* _OPT_ASSERT_H_ */
