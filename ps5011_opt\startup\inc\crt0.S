##==============================================================================
##
##	crt0.S
##
##	nds32 startup code
##
##==============================================================================
##
## Copyright (c) 1995, 1996, 1997, 1998 Cygnus Solutions
##
## The authors hereby grant permission to use, copy, modify, distribute,
## and license this software and its documentation for any purpose, provided
## that existing copyright notices are retained in all copies and that this
## notice is included verbatim in any distributions. No written agreement,
## license, or royalty fee is required for any of the authorized uses.
## Modifications to this software may be copyrighted by their authors
## and need not follow the licensing terms described here, provided that
## the new terms are clearly indicated on the first page of each file where
## they apply.
##
##------------------------------------------------------------------------------
#define EN_PS8036  0
#include "interrupt.h"
.extern GENERAL_EXCEPTION_HANDLER
	.section .nds32_init, "ax"
exception_vector:
	.align 2
!========================================================================
! Vector table
!========================================================================
	j _start                          !  (0) Trap Reset
	j OS_Trap_TLB_Fill                !  (1) Trap TLB fill
	j OS_Trap_PTE_Not_Present         !  (2) Trap PTE not present
	j OS_Trap_TLB_Misc                !  (3) Trap TLB misc
	j OS_Trap_TLB_VLPT_Miss           !  (4) Trap TLB VLPT miss
	j OS_Trap_Machine_Error           !  (5) Trap Machine error
	j OS_Trap_Debug_Related           !  (6) Trap Debug related
	j GENERAL_EXCEPTION_HANDLER	      !  (7) General exception (overlay)
	j OS_Trap_Syscall                 !  (8) Syscall
#if EN_PS8036
	j OS_Trap_Interrupt_HW0           !  (9) Interrupt HW0, UHS2 INT
	j OS_Trap_Interrupt_HW1           ! (10) Interrupt HW1, MISC interrupt.. ex: timer
	j OS_Trap_Interrupt_HW2           ! (11) Interrupt HW2, SD/MMC INT
	j OS_Trap_Interrupt_HW3           ! (12) Interrupt HW3, FIP INT
	j OS_Trap_Interrupt_HW4           ! (13) Interrupt HW4
	j OS_Trap_Interrupt_HW5           ! (14) Interrupt HW5
	j OS_Trap_SWI                     ! (15) Interrupt SW0
#endif
	.org 252
1:	j	1b
exception_vector_end:
exception_handler:
	.align  2
	.long	tlb_exception_handler
	.long	error_exception_handler
	.long	syscall_handler
ISR_TABLE:
#if EN_PS8036
	.long	HW0_ISR      !UHS2 INT
	.long	HW1_ISR      !MISC interrupt.. ex: timer
	.long	HW2_ISR      !SD/MMC INT
	.long	HW3_ISR      !FIP INT
	.long	HW4_ISR
	.long	HW5_ISR
#endif
/*
 * exception handlers
 */

OS_Trap_TLB_Fill:
OS_Trap_PTE_Not_Present:
OS_Trap_TLB_Misc:
OS_Trap_TLB_VLPT_Miss:  !chk
1:	b	1b
	SAVE_ALL
	/* li	$r0, #0x4*/
	b	tlb_exception_handler

OS_Trap_Machine_Error:  !chk
1:	b	1b
	SAVE_ALL
	li	$r0, #0x5
	b	error_exception_handler

OS_Trap_Debug_Related:  !chk
1:	b	1b
	SAVE_ALL
	li	$r0, #0x6
	b	error_exception_handler

OS_Trap_Syscall:  !chk
	SYSCALL_SAVE_ALL
        bal	syscall_handler
	SYSCALL_RESTORE_ALL
	iret
#if EN_PS8036
OS_Trap_Interrupt_HW0:
	SAVE_ALL_HW0
    li      $r0, 0x0
	la	    $r1, HW0_ISR
    jral    $r1
    RESTORE_ALL_HW0
	iret

common_ISR_wrapper:
	SAVE_ALL_HW
	la	$r1, ISR_TABLE
	lw	$r1, [$r1+$r0<<2]
	jral	$r1
	RESTORE_ALL_HW
	iret

OS_Trap_Interrupt_HW1:
	push 	$r0
    li      $r0, 0x01
	b	common_ISR_wrapper

OS_Trap_Interrupt_HW2:
	push 	$r0
    li      $r0, 0x02
	b	common_ISR_wrapper

OS_Trap_Interrupt_HW3:
	push 	$r0
    li      $r0, 0x03
	b	common_ISR_wrapper

OS_Trap_Interrupt_HW4:
	push 	$r0
    li      $r0, 0x04
	b	common_ISR_wrapper

OS_Trap_Interrupt_HW5:
	push 	$r0
    li      $r0, 0x05
	b	common_ISR_wrapper

OS_Trap_SWI:
	SAVE_ALL
	li      $r0, 0x0f
	la      $r1, SW0_ISR
	jral    $r1
	RESTORE_ALL
	iret
#endif
!DUMMY_ISR:
!    iret

##------------------------------------------------------------------------------
## Startup code
	.set NDS_ADR24, 1

	.if NDS_ADR24
	.set CPE_SDRAMC_BASE, 0x00E02000
	.else
	.set CPE_SDRAMC_BASE, 0x90300000
	.endif

	.section .text
	.global	_start
	.weak   _call_exit
	.weak   _SDA_BASE_
	.weak   _FP_BASE_
	.align	2
	.func	_start
	.type	_start, @function

_start:
    ! YH, enable EDLM and set base address 0x00100000
    !movi55 $r0, #1
    !sethi   $r0,#0x100   ! for PS8036 1MB
    !sethi   $r0,#0x8     ! for PS5008 32KB
    sethi   $r0,#0x80     ! for PS5011 512KB
    ori     $r0,$r0,#0x1
    mtsr    $r0, $DLMB
    dsb

	! set $PSW.INTL to 0
    mfsr   $r0, $PSW
	ori    $r0, $r0, 0x6
	xori   $r0, $r0, 0x6
	mtsr   $r0, $PSW
	!ori    $r0, $r0, 1		! enable GIE, //disable GIE enbale when retrun to main()
	mtsr   $r0, $IPSW
	dsb

    mfsr    $r0, $INT_MASK  ! enable HW interrupt
    !ori     $r0, $r0, #0x0F ! enable HW0_ISR, HW1_ISR(misc int), HW2_ISR, HW3_ISR(FIP int)
    !ori     $r0, $r0, #0x0A ! enable HW1_ISR(misc int), HW3_ISR(FIP int) for PS8036
    ori     $r0, $r0, #0x00 ! disable all interrupt for PS5008
    mtsr    $r0, $INT_MASK
    dsb

	! set IVB.ESZ = 0 (vector table entry size = 4 bytes)
	mfsr   $r1, $IVB
	li     $r0, 0xc000
	or	   $r1, $r1, $r0
	xor    $r1, $r1, $r0
	mtsr   $r1, $IVB
	dsb

#if EN_PS8036
/*----------------------------------------------------------------------
        Adjust OSC Registers
----------------------------------------------------------------------*/
	! set overlay fixed region to 80K
	.set OVRAM_CTRL_BASE,	0x00800000	! 24-bit address map
	.set OFF_FIXED_REGION_SIZE, 0x4
	.set OFF_OVL_REGION_BASE, 0x8
	.set OFF_OVL_REGION_END, 0xc
	.set OFF_OVL_REGION_DMA, 0x10

    move    $r2, 0x14000			! 80K, for EN_OVERLAY
    !move    $r2, 0x20000            ! 128K, for EN_FPGA + !EN_OVERLAY + EN_FW

	li	$r3, OVRAM_CTRL_BASE
	swi	$r2, [$r3 + OFF_FIXED_REGION_SIZE]
#endif
/*----------------------------------------------------------------------*/

	la	$gp, _SDA_BASE_	    ! init GP for small data access
	la	$fp, _FP_BASE_      ! init FP
	la	$sp, _stack         ! init SP

#if EN_PS8036
	bal	init_MMU
#endif

	!bal	init_sdram !YH mark, for support OSC
//#if EN_PS8036
	bal	load_elf
//#endif

	!bal	uart_init  ! YH mark

	la	$lp, forever_loop	! main() returns here
	la	$r0, opt_main
	mtsr	$r0, $IPC
	iret

forever_loop:
	b	forever_loop
#if EN_PS8036
init_MMU:
	!  [8:0] = 00 00 10 10 0
	! ~[8:0] = 11 11 01 01 1 = 1_1110_1011 = 0x1eb

	mfsr    $r0, $MMU_CTL
	ori     $r0, $r0, 0x1ff
	xori    $r0, $r0, 0x1eb ! ntc3,ntc2: noncacheable,
				! ntc1,ntc0: cacheable,writeback
				! page size = 4kb
	mtsr    $r0, $MMU_CTL
	isb


! $MMU_CFG:
!	[1:0] MMPS(Memory Management Protection Scheme)
!	MMPS:
!		0: No memory management
!		1: Protection Unit
!		2: TLB MMU
!		3: Reserved
! note: skip TLB_MISC if MMPS != 2
	mfsr    $r0, $MMU_CFG
	andi    $r0, $r0, 0x3
	xori    $r0, $r0, 0x2
	bnez    $r0,skip_TLB_MISC

	movi	$r0, 0
	mtsr	$r0, $TLB_MISC	! set cid = 0
! default page size = 4kb for tlb op operations
	isb
#endif
skip_TLB_MISC:
	ret
#if EN_PS8036
init_sdram:

	!  set SDRAM register
	li	$r0, CPE_SDRAMC_BASE
	li	$r1, #0x00011312
	swi	$r1, [$r0+#0x0]
	li	$r1, #0x004800a0
	swi	$r1, [$r0+#0x4]

.if NDS_ADR24
	! judge SDRAM data width and size, bank size for 24-bit address
	li	$r1, #0x00001023
.else
	li	$r1, #0x00002326
.endif
	swi	$r1, [$r0+#0x8]

	!  set to precharge
	li	$r1, 0x00000010      ! IPREC
	swi	$r1, [$r0+#0xc]

!  Waiting for SDRAM to set up
1:
	lwi	$r1, [$r0+#0xc]
	andi	$r1, $r1, 0x1c
	bnez	$r1, 1b

!  set mode register
	li	$r1, #0x4
	swi	$r1, [$r0+#0xc]

#!  Waiting for SDRAM to set up
1:
	lwi	$r1, [$r0+#0xc]
	bnez	$r1, 1b

!  set to refresh
	li      $r1, #0x8
	swi     $r1, [$r0+#0xc]
!  Waiting for SDRAM to set up
1:
	lwi     $r1, [$r0+#0xc]
	bnez    $r1, 1b

.if NDS_ADR24
	li	$r1, #0x00001008           !base = 0x00800000, bank enable
.else
	li	$r1, #0x00001100           !base = 0x10000000, bank enable
.endif
	swi	$r1, [$r0+#0x10]           !bank 0
	li	$r1, #0x0
	swi	$r1, [$r0+#0x14]           !bank 1 (clear/disable)
	swi	$r1, [$r0+#0x18]           !bank 2 (clear/disable)
	swi	$r1, [$r0+#0x1c]           !bank 3 (clear/disable)

end_init_sdram:
	ret

	.size   _start, .-_start
	.endfunc

	.global	abort
	.align	2
	.type	abort, @function
#endif
abort:
	syscall 1
.end
