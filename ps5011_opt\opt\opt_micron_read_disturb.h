/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  opt_micron_read_disturb.h
 *
 *
 *
 ****************************************************************************/

#ifndef _OPT_MICRON_READ_DISTURB_H_
#define _OPT_MICRON_READ_DISTURB_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "hal/cop0/hal_cop0_types.h"
#include "opt_arch.h"
#include "opt_const.h"
#include "opt_main.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#ifdef _OPT_MICRON_READ_DISTURB_C_
#define EXTERN
#else
#define EXTERN extern
#endif


#define MAX_SUPPORT_BLK_NUM_PER_VB			(64)
#define MAX_SUPPORT_CE_NUM					(16)
#define MAX_SUPPORT_DIE_NUM					(32)
#define SCAN_INFO_QUEUE_DEPTH				(2)
#define INVALID_URN_VALUE					(0xFFFF)
#define BLK_TYPE_OPEN_NUM					(BLK_TYPE_FULL)
#define EVENT_TYPE_FULL_NUM					(EVENT_TYPE_OPEN_BLK)


#define OPT_DCCM_MICRON_RESTORE_FWPCA_BASE		(OPT_DCCM_READ_DISTURB_BASE)
#define OPT_DCCM_MICRON_READ_CNT_BASE			(OPT_DCCM_MICRON_RESTORE_FWPCA_BASE + OPT_DCCM_MICRON_RESTORE_FWPCA_SIZE)
#define OPT_DCCM_MICRON_SCAN_INFO_BASE			(OPT_DCCM_MICRON_READ_CNT_BASE + OPT_DCCM_MICRON_READ_CNT_SIZE)

#define OPT_DCCM_MICRON_RESTORE_FWPCA_SIZE		(0x80) // only use 16CE * 4 byte * 2 depth = 128
#define OPT_DCCM_MICRON_READ_CNT_SIZE 			(sizeof(COP0_MICRON_READ_CNT_STRUCT))
/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum {
	BLK_TYPE_NONE,
	BLK_TYPE_OPEN_TABLE,
	BLK_TYPE_OPEN_GR,
	BLK_TYPE_OPEN_INIT_INFO,
	BLK_TYPE_OPEN_VT,
	BLK_TYPE_OPEN_VT_CHILD,
	BLK_TYPE_OPEN_RS_PARITY,
	BLK_TYPE_FULL
} PRDH_BLK_TYPE;



typedef enum {
	EVENT_TYPE_FULL_QLC,
	EVENT_TYPE_FULL_D3SLC,
	EVENT_TYPE_FULL_SLC,
	EVENT_TYPE_OPEN_BLK,
	EVENT_TYPE_NUM
} TRIGGER_EVENT_TYPE;
/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

typedef union cop0_prdh_read_cnt_struct				COP0_PRDH_READ_CNT_STRUCT, *COP0_PRDH_READ_CNT_STRUCT_PTR;
typedef struct cop0_prdh_scan_info_struct			COP0_PRDH_SCAN_INFO_STRUCT, *COP0_PRDH_SCAN_INFO_STRUCT_PTR;
typedef struct cop0_micron_read_cnt_struct			COP0_MICRON_READ_CNT_STRUCT, *COP0_MICRON_READ_CNT_STRUCT_PTR;
typedef struct cop0_micron_scan_info_struct			COP0_MICRON_SCAN_INFO_STRUCT, *COP0_MICRON_SCAN_INFO_STRUCT_PTR;
typedef struct cop0_micron_store_struct				COP0_MICRON_STORE_STRUCT, *COP0_MICRON_STORE_STRUCT_PTR;
typedef struct cop0_micron_patch_info_struct		COP0_MICRON_PATCH_INFO_STRUCT, *COP0_MICRON_PATCH_INFO_STRUCT_PTR;


union cop0_prdh_read_cnt_struct {
	U32 ulReadCnt;
	struct {
		U16 uwReadCnt;
		U16 uwURN;
	} prdh;
};

struct cop0_prdh_scan_info_struct {
	COP0_PRDH_READ_CNT_STRUCT Record;
	U32 ulFWPCA;
};

struct cop0_micron_read_cnt_struct {
	COP0_PRDH_READ_CNT_STRUCT OpenSLCBlk[BLK_TYPE_OPEN_NUM][MAX_SUPPORT_BLK_NUM_PER_VB];//1792
	COP0_PRDH_READ_CNT_STRUCT Full[EVENT_TYPE_FULL_NUM][MAX_SUPPORT_DIE_NUM];//128*3
	U32 ulAndesRecordOpenSLCUnit[BLK_TYPE_OPEN_NUM];//28
	U32 ulRsv[25];//100
};

struct cop0_micron_scan_info_struct {
	U32 ulScanBMP[EVENT_TYPE_NUM];
	COP0_PRDH_SCAN_INFO_STRUCT Full[EVENT_TYPE_FULL_NUM][MAX_SUPPORT_DIE_NUM][SCAN_INFO_QUEUE_DEPTH];
	COP0_PRDH_SCAN_INFO_STRUCT OpenSLCBlk[BLK_TYPE_OPEN_NUM];
};

struct cop0_micron_store_struct {
	U32	ulFWPCA[COP0_PATCH_CMD_INFO_DEPTH];
};

struct cop0_micron_patch_info_struct {
	COP0_MICRON_STORE_STRUCT Info[MAX_SUPPORT_CE_NUM];
};

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_READ_DISTURB_SLC_FORCE_COPY_THRESHOLD_ADJUSTMENT(x)      ((x) * 100)


/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
EXTERN volatile COP0_MICRON_SCAN_INFO_STRUCT_PTR 		gpMicronScanInfo;
EXTERN volatile COP0_MICRON_READ_CNT_STRUCT_PTR   		gpMicronReadCnt;
EXTERN volatile COP0_MICRON_PATCH_INFO_STRUCT_PTR   	gpMicronPatchInfo;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
void OPTMicronReadCntHandle(OPT_JOB_STRUCT_PTR pJob, U8 ubCmdCnt);

#endif /* _OPT_MICRON_READ_DISTURB_H_ */
