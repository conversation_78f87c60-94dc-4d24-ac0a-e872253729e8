/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_main.h                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_MAIN_H_
#define _OPT_MAIN_H_

#include "misc/types.h"
#include "hal/cop0/hal_cop0_reg.h"
#include "hal/cop0/hal_cop0_types.h"
#include "opt_arch.h"
#include "opt_global.h"
#include "fpu.h"
#include "opt_assert.h"

#ifdef _OPT_MAIN_C_
#define EXTERN
#else
#define EXTERN extern
#endif


#define __ASM_VOLATILE_MEMORY__()   asm volatile("" ::: "memory")
#define __ASM_DSB()	__asm("DSB");


#if (MicronFlashID4 == IM_N28A_ID4)
#define _N28_FOR_SIZE_BUILD_CODE __attribute__((optimize("Os")))
#define MICRON_N28_MAX_PLANE_NUM	(4)
#else /* (MicronFlashID4 == IM_N28A_ID4) */
#define _N28_FOR_SIZE_BUILD_CODE
#endif /* (MicronFlashID4 == IM_N28A_ID4) */

#define OPT_CHK_PCA_DIFF_BIT(pca_a, pca_b) (pca_a^pca_b)
#define OPT_CHK_PCA_IN_SAME_MULTI_PLANE(diff_bit_rmp_pca, pca_rule)   (0 == (gOptStruct.ulPCAMaskMultiPlane[pca_rule] & diff_bit_rmp_pca))
#define OPT_CHK_PCA_IN_SAME_DIE(diff_bit_rmp_pca, pca_rule)           (0 == (gOptStruct.ulPCAMaskDie[pca_rule] & diff_bit_rmp_pca))
#define OPT_CHK_PCA_IN_SAME_BLOCK(diff_bit_rmp_pca, pca_rule)        (0 == (gOptStruct.ulPCAMaskBlock & diff_bit_rmp_pca))
#define OPT_CHK_PCA_IN_SAME_UNIT(diff_bit_pca)        (0 == (gOptStruct.ulPCAMaskFWUnit & diff_bit_pca))

#define FW_INVALID_UNIT				(0x3FFF)
#define INVALID_UNIT				(0x1FFF) // 用於給VT的GR GCGR unit的default值，隨便給個大於MAX_UNIT

#define DEFAULT_ASSERT_CODE		(0x01234567)

#define M_GET_FW_VERSION_1		((CUSTOMER_CODE << 24) | (FW_MODE << 16) | (CONTROLLER_VERSION_MINOR << 8) | (CONTROLLER_VERSION_MAJOR))
#define M_GET_FW_VERSION_2		((SUB_CODE_VERSION_2 << 24) | (SUB_CODE_VERSION_1  << 16) | (VERSION_TAG << 8) | (FW_ATTRIBUTE))

#define MT_TRIGGER_CNT_EMPTY			(0)

enum {
	PLANE_SEARCH_ROUND_IDX0 = 0,
	PLANE_SEARCH_ROUND_IDX1 = 1,
	PLANE_SEARCH_ROUND_IDX2 = 2,
	PLANE_SEARCH_ROUND_IDX3 = 3,
	PLANE_SEARCH_ROUND_IDX4 = 4,
	PLANE_SEARCH_ROUND_IDX5 = 5,
};


#define OPT_CHECK_PLANE_SEARCH_ROUND_NO_WAIT(PSIdx)    (OPTQB[R8_OPT_PS_RESULT_NW_0 + (PSIdx)])
#define OPT_CHECK_PLANE_SEARCH_QUEUE_CNT(SLC)         (OPTQB[R8_OPT_PS_RESULT_MLC_QUEUE_CNT + (SLC)])
#define OPT_CHECK_TIMEOUT()                       (OPTQB[OPTQB_PROC_Q_TIMEOUT] & BIT0)
#define OPT_PLANE_SEARCH_READ_CMD_CNT()         (OPTQB[R8_OPT_PS_READ_CMD_CNT])
#define OPT_PROCESS_QUEUE_ELEMENT_CNT()                (OPTQB[OPTQB_PROC_CMD_Q_ELEMENT_CNT] & OPTQB_PROC_CMD_Q_ELEMENT_CNT_MASK)
#define OPT_PLANE_SEARCH_NON_READ_PTR()           (OPTQB[R8_OPT_PS_NOT_READ_PTR])

#define M_OPT_CHK_GC_BACKUP_P4K_ENABLE_BY_MT_TEMPLATE()	(mtq->dw9.bits.gc)
#define M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()	(COP0_BACKUP_P4K_WORKAROUND && M_OPT_CHK_GC_BACKUP_P4K_ENABLE_BY_MT_TEMPLATE())

#if (PS5021_EN || S17_EN)
#define M_OPT_SET_WRITABLE_COUNT(cur_que)		// HW diff, S17 default doesn't need to re-write writable count
#define OPT_SET_TRIG_DATA_FSA0_VLD			(0)
#endif /* (PS5021_EN || S17_EN) */
#define M_OPT_CHK_LOOKUP_RAIDECC_PROGRAM_PARITY		((LOOKUP_RS_PROG_PARITY_W) && (FALSE == LOOKUP_RS_OTFENC_W)) // force program parity時, 會同時拉program parity及on-the-fly

enum {
	COP0_JOB_CMD_COPYBACK = 0,
	COP0_JOB_CMD_PROG,
	COP0_JOB_CMD_READ,
	COP0_JOB_CMD_ERASE,
	COP0_JOB_CMD_UNLOCK,
	COP0_JOB_CMD_BARRIER,
	COP0_JOB_CMD_IOR_NO_DMA,
	COP0_JOB_CMD_VENDER_CMD = 7, //(max)
	COP0_JOB_CMD_INVALID	= 0xF
};

enum {
	MT_1_MT_TRIG = 1,
	MT_2_MT_TRIG = 2
};

//#define COP0_JOB_PARAM_MT_TMEPLATE_SEL      (BITMSK(3, 0))
//#define COP0_JOB_PARAM_SLC_MODE             (BIT5)
//#define COP0_JOB_PARAM_WL_SKIP              (BIT6)
//#define COP0_JOB_PARAM_Q_LINK_LIMIT_SEL     (BIT8|BIT9)
//#define COP0_JOB_PARAM_Q_LINK_PRIORITY      (BIT10|BIT11)   // 0: low, 1: mid, 2: high
//#define COP0_JOB_PARAM_CQ_RESPONSE          (BIT12)         // 0: response only if error, 1: always response
//#define COP0_JOB_PARAM_FORCE_EMPTY          (BIT13)         // 1: MT table set force empty
//#define COP0_JOB_PARAM_MT_CLK_SEL           (BIT14)
//#define COP0_JOB_PARAM_FW_CONTINUOUS        (BIT15)

#define OPT_JOB_HANDLE_CNT                  (2)
#define OPT_JOB_HANDLE_CNT_MASK                  (1)

#if (PS5021_EN)
#define OPT_PS_INVALID_RESULT       (0x3F3F3F3F3F3F3F3F)
#define OPT_INVALID_OPT_INDEX		(0x3F)

#elif (S17_EN)
#define OPT_PS_INVALID_RESULT       (0x1F1F1F1F1F1F1F1F)
#define OPT_INVALID_OPT_INDEX		(0x1F)

#else /* (PS5021_EN) */
#define OPT_PS_INVALID_RESULT       (0x0F0F0F0F0F0F0F0F)
#define OPT_INVALID_OPT_INDEX		(0x0F)

#endif /* (PS5021_EN) */

#define OPT_INVALID_IOR_GROUP_ID	(0x0F)

//#define OPT_BLOCKING_BY_WAITING_NEW_CMD     (0)
//#define OPT_BLOCKING_BY_WAITING_FREE_MT     (1)

/*
 * parser state
 */
#define OPT_PARSER_STATE_IDLE               (0) // idle, let parser switch q
#define OPT_PARSER_STATE_GATHER             (1)
#define OPT_PARSER_STATE_RUN_MACRO          (2)
#define OPT_PARSER_STATE_CLOSE_MACRO        (3)
#define OPT_PARSER_STATE_UNDEFINE           (4)

/*
 * gather state
 */
enum {
	OPT_GATHER_STATE_WAIT_1ST_PLANE = 0,
	OPT_GATHER_STATE_WAIT_READ_NEXT_PLANE,
	OPT_GATHER_STATE_WAIT_OTHER_NEXT_PLANE,
};

/*
 * gather status
 */
#define OPT_NEW_GATHER_STATUS_INIT              (0)
#define OPT_NEW_GATHER_STATUS_SUCCESS           (1)
#define OPT_NEW_GATHER_STATUS_FAIL              (2)
#define OPT_NEW_GATHER_STATUS_TIME_OUT          (3)
#define OPT_NEW_GATHER_STATUS_STALL             (4)
#define OPT_NEW_GATHER_STATUS_BANKING_OTHER_Q   (5)

/*
 * macro cmd
 */
enum {
	OPT_MACRO_CMD_UNDEFINE = 0,

	OPT_MACRO_CMD_ERASE,

	OPT_MACRO_CMD_NORMAL_READ,
	OPT_MACRO_CMD_CACHE_READ_START,
	OPT_MACRO_CMD_CACHE_READ_END,
	OPT_MACRO_CMD_CACHE_READ,

	OPT_MACRO_CMD_D2_NORMAL_PROG,
	OPT_MACRO_CMD_D2_CACHE_PROG_START,
	OPT_MACRO_CMD_D2_CACHE_PROG_END,
	OPT_MACRO_CMD_D2_CACHE_PROG,

	OPT_MACRO_CMD_COPYBACK,

	OPT_MACRO_CMD_IOR_NO_DMA,
	OPT_MACRO_CMD_DUMMY_CMD,
	OPT_MACRO_CMD_GET_TEMPERATURE_CMD,
	OPT_MACRO_CMD_SYNC_OPEN_UNIT_CMD,
#if (MicronFlashID4 == IM_N28A_ID4)
	OPT_MACRO_CMD_VALLEY_CHECK_CMD,
#endif /* (MicronFlashID4 == IM_N28A_ID4) */
	OPT_MACRO_CMD_VENDER_CMD,

	OPT_MACRO_CMD_CNT
};

/*
 * macro cmd's state
 */
enum {
	OPT_MACRO_CMD_STATE_SETUP = 0,
	OPT_MACRO_CMD_STATE_INIT,
	OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1,
	OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2,
	OPT_MACRO_CMD_STATE_RUN_PRE_CACHE,
	OPT_MACRO_CMD_STATE_RUN_CACHE_NEXT,
	OPT_MACRO_CMD_STATE_RUN_CACHE_END,
	OPT_MACRO_CMD_STATE_RUN_POST_CACHE,
	OPT_MACRO_CMD_STATE_RUN_CACHE_END_DMA,
	OPT_MACRO_CMD_STATE_RUN_SPLIT_1,
	OPT_MACRO_CMD_STATE_RUN_SPLIT_2,
	OPT_MACRO_CMD_STATE_RUN_SPLIT_3,
	OPT_MACRO_CMD_STATE_RUN_SPLIT_4,
	OPT_MACRO_CMD_STATE_RUN_CB_LOWER,
	OPT_MACRO_CMD_STATE_RUN_CB_MIDDLE,
	OPT_MACRO_CMD_STATE_RUN_CB_UPPER,
	OPT_MACRO_CMD_STATE_RUN_VENDER_CMD,
	OPT_MACRO_CMD_STATE_WAIT_CLOSE,
	OPT_MACRO_CMD_STATE_DONE,
};

/*
 * opt status
 */
#define OPT_STATUS_CACHE_SHIFT	(4)
#define OPT_STATUS_CACHE_BIT_NUM	(1)
#define OPT_STATUS_WL_BYPASS_STATUS_SHIFT	(5)
#define OPT_STATUS_WL_BYPASS_STATUS_BIT_NUM	(1)
#define OPT_STATUS_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_SHIFT	(6)
#define OPT_STATUS_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_BIT_NUM	(1)
#define OPT_STATUS_WL_BYPASS_DISABLE_PREREAD_STATUS_SHIFT	(7)
#define OPT_STATUS_WL_BYPASS_DISABLE_PREREAD_STATUS_BIT_NUM	(1)

/*
 * USERDEFINE
 */
#define OPT_INFO_DIE_IL_BMP_SHIFT	(12)
#define OPT_INFO_PLANE_SHIFT	(16)
#define OPT_INFO_IWL_BMP_SHIFT	(27)
#define OPT_INFO_CACHE_CMD	(BIT24)
#define OPT_INFO_NORMAL_CMD	(0)
#define OPT_INFO_RMP_BYPASS	(BIT25)
#define OPT_INFO_RMP_USE		(0)
#define OPT_INFO_PFA			(BIT26)
#define OPT_INFO_POL_FAIL		(0)
#define OPT_INFO_TOSHIBA_FAST_READ_OPERATION	(BIT27)
#if(NEW_IWL_EN)
#define OPT_INFO_NORMAL_READ_OPERATION	(0)
#define OPT_INFO_MICRON_IWL_CROSS_OPERATION_SHIFT	(27)
#else /* (NEW_IWL_EN) */
#define OPT_INFO_MICRON_SNAP_READ_OPERATION	(BIT27)
#define OPT_INFO_NORMAL_READ_OPERATION	(0)
#define OPT_INFO_MICRON_IWL_CROSS_OPERATION	(BIT28)
#endif /* (NEW_IWL_EN) */
#define OPT_INFO_GEN_FAIL		(BIT31)	 // reserved, temp use

#define OPT_INFO_PFA_MARK			0x77
#define OPT_INFO_POL_FAIL_MARK	0x88

#define OPT_STATUS_READ_GC_BACKUP_P4K (BIT0)

/*
 * Constant
 */
#define TLC_FSP_DEPTH		(3)
#define GATHER_1ST_MACRO			(0)
#define GATHER_NEXT_MACRO			(1)
// N28
#define WordLineBypasseXtraPageOverride	(2)
#define WordLineBypassWithEnablePreRead	(0)
#define WordLineBypassWithDisableeXtraPagePreRead		(1)
#define WordLineBypassWithDisableLowerAndUpperPagePreRead	(2)
#define ValleyCheckMTPResource			(6)

//B47R Shared Pages
#define IM_B47R_SECTION_1	(4)
#define IM_B47R_SECTION_2	(1048)
#define IM_B47R_SECTION_3	(1064)
#define IM_B47R_SECTION_4	(2108)
#define IM_B47R_SECTION_5	(2112)

//B37R Shared Pages
#define IM_B37R_SECTION_1	(4)
#define IM_B37R_SECTION_2	(760)
#define IM_B37R_SECTION_3	(776)
#define IM_B37R_SECTION_4	(1532)
#define IM_B37R_SECTION_5	(1536)

//N48R Shared Pages
#define IM_N48_SECTION_1	(8)
#define IM_N48_SECTION_2	(1400)
#define IM_N48_SECTION_3	(1416)
#define IM_N48_SECTION_4	(2808)
#define IM_N48_SECTION_5	(2816)

//========================================== OPT STATUS DEFINITION ====================================
enum {
	ENUM_CACHE_READ = 0,
	ENUM_PLANE_READ_DMA,
	ENUM_READ_DMA,
	ENUM_READ_END_DMA,
	ENUM_MAX_IDX
};

#if(OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
#define GET_WORDLINEBYPASS_STATE_QUEUE(QUEUE_IDX) ((cpu_comm->ulWordLineBypassStateBMP & BIT(QUEUE_IDX)) ? TRUE : FALSE)
#define _SET_WL_BYPASS_STATUS_OPT_STATUS() {mtd->dw0_dat.bits.opt_status |= (GET_WORDLINEBYPASS_STATE_QUEUE(gOptStruct.cur_que)<<OPT_STATUS_WL_BYPASS_STATUS_SHIFT);}
#endif


#if (MicronFlashID4 == IM_N28A_ID4)
#define N28_WORDLINEBYPASS_EXTRA_PAGE_AND_PREREAD_BMP_SHIFT (16)
#define SET_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(QUEUE_IDX) {cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP |= BIT(QUEUE_IDX);}
#define CLEAR_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(QUEUE_IDX) {cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP &= (~ (U32)BIT(QUEUE_IDX));}
#define GET_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(QUEUE_IDX) ((cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP & BIT(QUEUE_IDX)) ? TRUE : FALSE)
#define SET_PREREAD_DISABLE_STATE_QUEUE(QUEUE_IDX) {cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP |= ((U32)BIT(QUEUE_IDX) << N28_WORDLINEBYPASS_EXTRA_PAGE_AND_PREREAD_BMP_SHIFT);}
#define CLEAR_PREREAD_DISABLE_STATE_QUEUE(QUEUE_IDX) {cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP &= (~((U32)BIT(QUEUE_IDX) << N28_WORDLINEBYPASS_EXTRA_PAGE_AND_PREREAD_BMP_SHIFT));}
#define GET_PREREAD_DISABLE_STATE_QUEUE(QUEUE_IDX) (((cpu_comm->ulWordLineBypasseXtraPageOverrideAndPrereadBMP >> N28_WORDLINEBYPASS_EXTRA_PAGE_AND_PREREAD_BMP_SHIFT) & BIT(QUEUE_IDX)) ? TRUE : FALSE)
#define _SET_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_OPT_STATUS() {mtd->dw0_dat.bits.opt_status |= (GET_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(gOptStruct.cur_que)<<OPT_STATUS_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_SHIFT);}
#define _SET_WL_BYPASS_DISABLE_PREREAD_STATUS_OPT_STATUS() {mtd->dw0_dat.bits.opt_status |= (GET_PREREAD_DISABLE_STATE_QUEUE(gOptStruct.cur_que)<<OPT_STATUS_WL_BYPASS_DISABLE_PREREAD_STATUS_SHIFT);}
#else /* (MicronFlashID4 == IM_N28A_ID4) */
#define SET_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(QUEUE_IDX)
#define CLEAR_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(QUEUE_IDX)
#define GET_WORDLINEBYPASS_EXTRA_PAGE_OVERRIDE_STATE_QUEUE(QUEUE_IDX) (FALSE)
#define SET_PREREAD_DISABLE_STATE_QUEUE(QUEUE_IDX)
#define CLEAR_PREREAD_DISABLE_STATE_QUEUE(QUEUE_IDX)
#define GET_PREREAD_DISABLE_STATE_QUEUE(QUEUE_IDX) (FALSE)
#define _SET_WL_BYPASS_EXTRA_PAGE_OVERRIDE_STATUS_OPT_STATUS()
#define _SET_WL_BYPASS_DISABLE_PREREAD_STATUS_OPT_STATUS()
#endif /* (MicronFlashID4 == IM_N28A_ID4) */

#if ((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
#define SET_PROG_LUXT_QUEUE(QUEUE_IDX) {cpu_comm->ulProgLUXTQueueBMP |= BIT(QUEUE_IDX);}
#define CLEAR_PROG_LUXT_QUEUE(QUEUE_IDX) {cpu_comm->ulProgLUXTQueueBMP &= (~(U32)BIT(QUEUE_IDX));}
#define GET_PROG_LUXT_QUEUE(QUEUE_IDX) ((cpu_comm->ulProgLUXTQueueBMP & BIT(QUEUE_IDX)) ? TRUE : FALSE)
#else /*((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))*/
#define SET_PROG_LUXT_QUEUE(QUEUE_IDX)
#define CLEAR_PROG_LUXT_QUEUE(QUEUE_IDX)
#define GET_PROG_LUXT_QUEUE(QUEUE_IDX) (FALSE)
#endif /*((MicronFlashID4 == IM_140S_ID4) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))*/

#if OPT_SUPPORT_PROVIDE_WRITE_OPT_STATUS_
#define _SET_PROG_OPT_STATUS_(cache, pln_vld) {mtd->dw0_dat.bits.opt_status = ((cache << OPT_STATUS_CACHE_SHIFT) | pln_vld);}
#else
#define _SET_PROG_OPT_STATUS_(cache, pln_vld)
#endif

#define	M_OPT_GET_MTD_DIE_NUM() (mtd->dw3_dat.bits.die_num)


#define M_FPU_GET_READ_STATUS_BUSY_20(ubDieNum) (FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_busy_20))
#define M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20)
#define M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40)

#if (!USE_S17_FPU)
#define M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_TLC_MODE_01)
#define M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_40_PREV_02(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PRE_TLC_MODE_02)
#define M_FPU_GET_POLL_SELECT_SLC_READ_STATUS_70_BUSY_20_CURR_04(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_MODE_04)
#define M_FPU_GET_POLL_SELECT_SLC_READ_STATUS_70_BUSY_40_PREV_08(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PRE_SLC_MODE_08)
#else
#define M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CURR_TLC_MODE_01)
#define M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_40_PREV_02(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PREV_TLC_MODE_02)
#define M_FPU_GET_POLL_SELECT_SLC_READ_STATUS_70_BUSY_20_CURR_04(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_MODE_04)
#define M_FPU_GET_POLL_SELECT_SLC_READ_STATUS_70_BUSY_40_PREV_08(ubDieNum) (POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PRE_SLC_MODE_08)
#endif

typedef struct opt_pack_addr_rule OPT_PACK_ADDR_RULE, *OPT_PACK_ADDR_RULE_PTR;
typedef struct opt_job_struct OPT_JOB_STRUCT, *OPT_JOB_STRUCT_PTR;
typedef struct opt_que_mgr_struct OPT_QUE_MGR_STRUCT, *OPT_QUE_MGR_STRUCT_PTR;
typedef struct opt_struct OPT_STRUCT, *OPT_STRUCT_PTR;

typedef struct opt_rs_mgr_struct OPT_RS_MGR_STRUCT, *OPT_RS_MGR_STRUCT_PTR;

typedef struct read_disturb_threshold_struct *READ_DSITURB_THRESHOLD_STRUCT_PTR;

struct opt_pack_addr_rule {
	U32                     shift;
	U32                     bit_no;
	U32                     mask;
};

typedef union {
	U64 uoAll;
	struct {
		U64 Reserve0 	    : 4;
		U64 ReadCntLow	: 11;
		U64 btOverReadCntThresholdLow	: 1;
		U64 uwEraseCntLow	: 16;
		U64 Reserve1 	    : 4;
		U64 ReadCntHigh	: 11;
		U64 btOverReadCntThresholdHigh	: 1;
		U64 uwEraseCntHigh : 16;
	} uo;
} ArmReadCnt_t;

typedef struct ReadCntHighByte  {
	U8 ReadCntLow 	    : 4;
	U8 ReadCntHigh 	    : 4;
} ReadCntHighByte_t;

struct read_disturb_threshold_struct {
	U16     uwTLCReadVerifyReadCntThreshold[ERASE_CNT_LEVEL_NUM];
	U16     uwSLCReadVerifyReadCntThreshold[ERASE_CNT_LEVEL_NUM];
	U16     uwTLCForceCopyReadCntThreshold[ERASE_CNT_LEVEL_NUM];
	U16     uwSLCForceCopyReadCntThreshold[ERASE_CNT_LEVEL_NUM];
	U16     uwTLCEraseCntRange[ERASE_CNT_LEVEL_NUM];
	U16     uwSLCEraseCntRange[ERASE_CNT_LEVEL_NUM];
};
typedef union {
	U8 ubAll;
	struct {
		U8 btNeedSwitchStatus : 1;
		U8 btSwitchtoOpen : 1;  //True : close to open;  FALSE : open to close
		U8 btSwitchtoOpenAndeXtraPageOverride : 1;  //True : XP is prog ; FALSE : XP is Not Prog. It's for 3 pass program QLC
		U8 Resv : 5;
	} bits;
} WordLineStatusBypassResult_t;

typedef struct LastPageResult {
	U16 uwLastPage;
	U16 uweXtraPage;
} LastPageResult_t;

/*
 * a opt_job represents a set of cmds:
 *   they are gathered as a multi-plane macro-cmd including 3-plane case
 *     - though in 3-plane case, it is not a 3-plane macro_cmd;
 *         they are splitted to a macro_cmd with 2 multi-plane/single-plane prog
 *     - if only 2-plane are valid in a 4-plane flash,
 *         they may also be splitted to a macro_cmd with 2 single-plane)
 */
#pragma pack(8)
struct opt_job_struct {
	BOOL                    cache_next;
	BOOL                    cache_prev;
	BOOL                    fsp;                    // full-sequence-prog or one-pass prog of M3D MLC or two-pass prog of M3D TLC
	BOOL            single_plane_read;
	U8                      gather_state;
	U8                      gather_status;
	U8                      cmd_cnt;                // including dummy cmd
	U8                      plane_vld;              // bitmap of valid plane
	//----------------------------------------------------------------
	U32                     pca;
	U32                     user_define;
	//----------------------------------------------------------------
	U8                      cmd;
	U8                      slc_mode;
	U16                     page;
	U8                      ubDie;
	U8                      macro_cmd;
	U8                      macro_state;
	U8                      macro_close_cmd;
	//----------------------------------------------------------------
	U8                      ubPlaneCnt;
	U8                      raidecc_dummy_cnt;
	U8                      raidecc_dummy_pln_vld;
	U8                      lmu;
	U32                     rmp_pca;
	//----------------------------------------------------------------
	BOOL                    RS_parity;
	U16                     uwParam_RAM_Index;       // for PFA
	U16                     uwExpectProgramPage;	// FPU bin value
	U8                      ior_ps_exe_result_idx;
	U8                      ior_ps_cross_group_exist;
	U8                      dma_done_map;
	//----------------------------------------------------------------
	BOOL                  auto_gen_fail;
	U8			ubRMP_bypass;	// VBRMP, BBRMP必定同時為1 or 0
	U8						ubMinGroupID;
	U8			ubReturn;
	U8			ubMicronDummyRead;
	U8			ubD1;
	U8			ubIWLEn;		// For IWL Feature
	U8			ubIWLPlaneIdx;
	//----------------------------------------------------------------
	U8			ubBinValue;

	U8			ubMixPlaneBMP;
	U8			ubPlaneP2LMap;
	U8                     ubReserve[5];
	U64                     ps_result_pln_0_7;
};
#pragma pack()

#pragma pack(8)
struct opt_que_mgr_struct {
	U32                     state;
	U16                     uwReserve0;//ps:plane search for die il using
	U16                     uwPage;
	//----------------------------------------------------------------
	/*
	 * job
	 */
	OPT_JOB_STRUCT          job_handle[OPT_JOB_HANDLE_CNT];
	//----------------------------------------------------------------
	U8                      head_job;
	U8                      tail_job;
	U8						ubIWLPatchBMP;
	U8                      ubReserve;
	U16                     uwContinuousUnit;
	U16                     uwSequentialReadWeightingFactorCnt;
	//----------------------------------------------------------------
};
#pragma pack()

struct opt_struct {
	BOOL                    disable_cache_read;
	BOOL                    disable_cache_prog;
	U8                      cur_que;
	U8                      ubPCAMaskFWUnitShift;
	/*
	 * pca mask, block page die CE CH LMU plane entry
	 */
	U32                     ulPCAMaskFWUnit;   //use to check multiplane's block and die
	U32                     ulPCAMaskBlock;     //block
	// PCA 4 Rule
	U32                     ulPCAMaskDie[COP0_PCA_RULE_NUM];       //die
	U32                     ulPCAMaskPage[COP0_PCA_RULE_NUM];  // page
	U32                     ulPCAMaskLMU[COP0_PCA_RULE_NUM];      // lmu
	U32                     ulPCAMaskDieInterleave[COP0_PCA_RULE_NUM];	//die interleave
	U32                     ulPCAMaskMultiPlane[COP0_PCA_RULE_NUM];
	//----------------------------------------------------------------
	OPT_QUE_MGR_STRUCT      que_mgr[CONFIG_NUM_FIP_CE_TOTAL];
	//----------------------------------------------------------------
	/*
	 * subroutine for running macro cmd
	 */
	BOOL                    (*run_macro_cmd[OPT_MACRO_CMD_CNT])(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
};

struct opt_rs_mgr_struct {
	U8 ubReleaseReservedMTPoolResource;
	U8 ubMTPoolResourceToProgramData;
	U8 ubRSTagList[RS_TAG_NUM];
};

EXTERN volatile COP0_MTD_STRUCT_PTR       mtd;
EXTERN volatile FPL_MTQ_STRUCT_PTR        mtq;
EXTERN volatile COP0_COMM_INFO_STRUCT_PTR cpu_comm;
EXTERN volatile COP0_READ_INFO_STRUCT_PTR read_info;
EXTERN volatile COP0_ASSERT_STRUCT_PTR    opt_assert;

EXTERN volatile OPT_RS_MGR_STRUCT gOPTRSMgr;
EXTERN volatile U8       *gpubReadCntLowByte;
EXTERN volatile ReadCntHighByte_t *gpubReadCntHighByte;
EXTERN OPT_STRUCT gOptStruct _SI(DATA_ADDR_ALIGN(8));
EXTERN U8 gubOPTPreviousJobPtr[JOB_BACKUP_QUEUE_NUM];
EXTERN OPT_JOB_STRUCT gOPTPreviousJob[JOB_BACKUP_QUEUE_NUM][JOB_BACKUP_LENGTH];
EXTERN volatile READ_DSITURB_THRESHOLD_STRUCT_PTR gpReadDisturbThreshold;
EXTERN U8 gubEntryPerPlane;//For Debug Cmd Seq
EXTERN U8 gubMaxPlane;
EXTERN U8 gubMaxCE;
EXTERN U8 gubMaxDie;
EXTERN U8 gubMaxChannel;
EXTERN U8 gubPlaneMask;
EXTERN U8 gubProgramParityQueue;
EXTERN U8 gubTotalPlaneBank;
EXTERN U8 gubClearARMStallReq;
EXTERN U8 gubIWLFlag;
EXTERN U32 gulSwitchQueue_BM;
EXTERN U16 guwLastProgramPageInPlaneBank[OPEN_UNIT_TYPE_NUM][CONFIG_NUM_FIP_CE_TOTAL * 4 * CONFIG_NUM_DIE]; //GR, GCGR * PLANEBANK NUM
EXTERN U8 gubUnreachLastPagePlaneNum[OPEN_UNIT_TYPE_NUM];

#if ((CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_QLC)|| (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) || (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_TLC))

#if (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC)
enum {
	TLC_FIRST_PROG = 0,
	TLC_FOGGY_PROG,
	TLC_FINE_PROG,
};
#endif

enum {
	TLC_LOW_PG = 0,
	TLC_MID_PG,
	TLC_UP_PG,
	QLC_TOP_PG,	//Reip Porting 3D-V7 QLC Add

	TLC_10_1A_PROG = 0,
	TLC_11_PROG,
	TLC_15_PROG,

	TLC_SINGLE_READ = 0,
	TLC_CACHE_READ,

	FLASH_DIE0 = 0,
	FLASH_DIE1,

	TLC_PROG_END
};
#endif /* ((CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_QLC)|| (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_2D_TLC) || (CONFIG_NAND_FLASH_TYPE == NAND_FLASH_TYPE_3D_TLC)) */

enum {
	DEBUG_CMD_SEQ_READ_TRIG_PAGE = 1,
	DEBUG_CMD_SEQ_READ_DMA,
	DEBUG_CMD_SEQ_PROG_DMA,
	DEBUG_CMD_SEQ_PROG_STATUS,
	DEBUG_CMD_SEQ_ERASE_TRIG,
	DEBUG_CMD_SEQ_ERASE_STATUS
};

EXTERN int  main(void);
EXTERN void opt_init(void);

EXTERN void opt_scheduler(void);
EXTERN void opt_parser(OPT_QUE_MGR_STRUCT_PTR que_mgr);
EXTERN U32  opt_gather_1st_macro(OPT_QUE_MGR_STRUCT_PTR que_mgr);
EXTERN U32  opt_gather_next_macro(OPT_QUE_MGR_STRUCT_PTR que_mgr);
EXTERN void opt_set_macro_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr);
EXTERN BOOL opt_macro_cmd_ior_no_dma_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);

EXTERN BOOL opt_macro_cmd_dummy_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_get_temperature_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_sync_open_unit_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_valley_check_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_vender_cmd(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);

EXTERN BOOL opt_mt_group_valley_check_cmd(OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_mt_group_vender_cmd(OPT_JOB_STRUCT_PTR job);
EXTERN void handle_MTpool_stall(U8 ubMTPoolResourceEmpty);

EXTERN void OPTErrorAssert(U32 ulErrorCode);
EXTERN void OPTCriticalErrorAssert(U32 ulErrorCode);
EXTERN BOOL OPTOtherCmdStopFormMTCheck(U8 ubCnt);

#undef EXTERN
#endif /* _OPT_MAIN_H_ */
