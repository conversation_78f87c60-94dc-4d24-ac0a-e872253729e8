/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_api.h                                                             */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef OPT_API_H_
#define OPT_API_H_
#include "opt_global.h"

#ifdef _OPT_API_C_
#define EXTERN
#else
#define EXTERN extern
#endif


#if SUPPORT_BVCI_WR_IF
EXTERN void bvci_write (U32 addr, U32 data, U8 by_map);
EXTERN U32 bvci_read (U32 addr);
#else
#define bvci_write(addr, data, by_map)
#define bvci_read(addr)    (0)
#endif

EXTERN void axim_write (U32 addr, U64 data, U8 by_map);
EXTERN U64  axim_read  (U32 addr);


#undef EXTERN
#endif /* OPT_API_H_ */
