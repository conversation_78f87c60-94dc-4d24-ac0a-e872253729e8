/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  uart.h                                                                */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _UART_H_
#define _UART_H_

#include "conf.h"
#include "misc/types.h"
#include "res/res_sema.h"


/*
 *  definitions
 *
 */

#if (HW == HW_FPGA)
#define REF_CLK           (30000000)
#define CPU_CLK           REF_CLK
#else
#define REF_CLK           (25000000)
#define CPU_CLK           REF_CLK
#endif


/*
 *  baud rate
 */
#define BAUDRATE(x)       (x)
#define BAUDRATE_9600     BAUDRATE(9600)
#define BAUDRATE_11400    BAUDRATE(11400)
#define BAUDRATE_19200    BAUDRATE(19200)
#define BAUDRATE_38400    BAUDRATE(38400)
#define BAUDRATE_56000    BAUDRATE(56000)
#define BAUDRATE_57600    BAUDRATE(57600)
#define BAUDRATE_115200   BAUDRATE(115200)


/*
 *
 */
#define UART_LOCK()         hal_sema_lock(RES_HW_SEMA_ID_UART)
#define UART_UNLOCK()       hal_sema_unlock(RES_HW_SEMA_ID_UART)


/*
 *
 */
#define UART_SEND_CQ()  uart_send_cq_to_host(0x00000000, 0xFE, 0x7, 0x0, 0x0)
#define UART_SET_XMDOEM_MODE(bool)  uart_set_xmodem_mode(bool)


/*
 *
 */
#if CONFIG_UART_ENABLE
#ifdef WIN32
#define UART_INIT(x)                                    uart_init(x)
#define UART_OUT_STR(str)                               do{ UART_LOCK(); uart_print_str(str); UART_UNLOCK();} while(0)
#define UART_OUT_DEC(u32)                               do{ UART_LOCK(); uart_print_u32(NULL, u32, FALSE, FALSE); UART_UNLOCK();} while(0)
#define UART_OUT_HEX(u32)                               do{ UART_LOCK(); uart_print_u32(NULL, u32, TRUE, FALSE); UART_UNLOCK();} while(0)
#define UART_OUT_STR_DEC(str, u32, crlf)                do{ UART_LOCK(); uart_print_u32(str, u32, FALSE, crlf); UART_UNLOCK();} while(0)
#define UART_OUT_STR_HEX(str, u32, crlf)                do{ UART_LOCK(); uart_print_u32(str, u32, TRUE, crlf); UART_UNLOCK();} while(0)
#define UART_OUT_HEX_U64(u64)                           do{ UART_LOCK(); uart_print_u64_hex(NULL, u64, FALSE); UART_UNLOCK();} while(0)
#define UART_OUT_STR_HEX_U64(str, u64, crlf)            do{ UART_LOCK(); uart_print_u64_hex(str, u64, crlf); UART_UNLOCK();} while(0)
#define UART_OUT_BUF(buf8)                              do{ UART_LOCK(); uart_print_buf(buf8); UART_UNLOCK();} while(0)
#define UART_PRINT_HEX2(str1, str2, v1, v2)             do{ UART_LOCK(); uart_print_hex2(str1, str2, v1, v2); UART_UNLOCK();} while(0)
#define UART_PRINT_HEX3(str1, str2, str3, v1, v2, v3)   do{ UART_LOCK(); uart_print_hex3(str1, str2, str3, v1, v2, v3); UART_UNLOCK();} while(0)
#else
#define UART_INIT(x)                                    uart_init(x)
#define UART_OUT_STR(str)                               do{ UART_LOCK(); uart_print_str(str); UART_UNLOCK();} while(0)
#define UART_OUT_DEC(u32)                               do{ UART_LOCK(); uart_print_u32(NULL, u32, FALSE, FALSE); UART_UNLOCK();} while(0)
#define UART_OUT_HEX(u32)                               do{ UART_LOCK(); uart_print_u32(NULL, u32, TRUE, FALSE); UART_UNLOCK();} while(0)
#define UART_OUT_STR_DEC(str, u32, crlf)                do{ UART_LOCK(); uart_print_u32(str, u32, FALSE, crlf); UART_UNLOCK();} while(0)
#define UART_OUT_STR_HEX(str, u32, crlf)                do{ UART_LOCK(); uart_print_u32(str, u32, TRUE, crlf); UART_UNLOCK();} while(0)
#define UART_OUT_HEX_U64(u64)                           do{ UART_LOCK(); uart_print_u64_hex(NULL, u64, FALSE); UART_UNLOCK();} while(0)
#define UART_OUT_STR_HEX_U64(str, u64, crlf)            do{ UART_LOCK(); uart_print_u64_hex(str, u64, crlf); UART_UNLOCK();} while(0)
#define UART_OUT_BUF(buf8)                              do{ UART_LOCK(); uart_print_buf(buf8); UART_UNLOCK();} while(0)
#define UART_PRINT_HEX2(str1, str2, v1, v2)             do{ UART_LOCK(); uart_print_hex2(str1, str2, v1, v2); UART_UNLOCK();} while(0)
#define UART_PRINT_HEX3(str1, str2, str3, v1, v2, v3)   do{ UART_LOCK(); uart_print_hex3(str1, str2, str3, v1, v2, v3); UART_UNLOCK();} while(0)
#endif
#else /* #if CONFIG_UART_ENABLE */
#define UART_INIT(x)
#define UART_OUT_STR(str)
#define UART_OUT_DEC(u32)
#define UART_OUT_HEX(u32)
#define UART_OUT_STR_DEC(str, u32, crlf)
#define UART_OUT_STR_HEX(str, u32, crlf)
#define UART_OUT_HEX_U64(u64)
#define UART_OUT_STR_HEX_U64(str, u64, crlf)
#define UART_OUT_BUF(buf8)
#define UART_PRINT_HEX2(str1, str2, v1, v2)
#define UART_PRINT_HEX3(str1, str2, str3, v1, v2, v3)
#endif /* #if CONFIG_UART_ENABLE */


#if CONFIG_UART_ENABLE_RX
#define UART_GET_CHAR(x)                    x = uart_get_char()
#define UART_POLL_GET_CHAR(x)               x = uart_poll_get_char()
#define UART_GET_DEC_U32(x)                 x = uart_get_val()
#else /* #if CONFIG_UART_ENABLE_RX */
#define UART_GET_CHAR(x)
#define UART_POLL_GET_CHAR(x)
#define UART_GET_DEC_U32(x)
#endif /* #if CONFIG_UART_ENABLE_RX */


/*
 *  data types
 *
 */
enum UART_RX_STATE {
	UART_RX_WAIT_CMD = 0,
	UART_RX_WAIT_C,
	UART_RX_BUSY,
	UART_RX_WAIT_DATA, // 3
	UART_RX_EOT,
	UART_RX_CAN
};

enum UART_PARSING_STATE {
	UART_PARS_INIT = 0,
	UART_PARS_RECV_OP,
	UART_PARS_RECV_DEVICE
};


#define UART_CMD_PARA_MAX_CNT   64
#define VUC_FEATURE_INDEX   48//the 48th index in nvme param aray is feature param
typedef struct uart_vuc_struct       UART_VUC_STRUCT, *UART_VUC_STRUCT_PTR;

struct uart_vuc_struct {
	U8 nvme_param[UART_CMD_PARA_MAX_CNT];
	U8 param_idx;
	_V U8 rx_state;
	U8 parsing_state;//uart parsing state machine // 0: (init); 1:(first recv byte is 0xD0 or 0xD1 or 0xD2); 2: second recv byte is 0xE8
	U8 is_cmd_ready; //0: cmd not ready; 1: cmd ready
	void *xmod;
};



/*
 *  public prototypes
 *
 */

void _uart_tx_byte(U8 val);
void uart_init(U32 baudrate);
void uart_print_str(const char *str);
void uart_print_u32(const char *str, U32 value, BOOL hex, BOOL crlf);
void uart_print_u64_hex(const char *str, U64 value, BOOL crlf);
void uart_print_buf(const U8 *buf);
void uart_print_hex2(char *str1, char *str2, U32 value1, U32 value2);
void uart_print_hex3(char *str1, char *str2, char *str3, U32 value1, U32 value2, U32 value3);
void uart_send_data(UART_VUC_STRUCT_PTR uart_vuc, U8 *buf_addr, U32 len);
void uart_recv_data(UART_VUC_STRUCT_PTR uart_vuc, U8 *buf_addr, U32 len);
void uart_byte_parsing(UART_VUC_STRUCT_PTR uart_vuc, U8 read_data);
void uart_send_cq_to_host(U32 cmd_specific, U8 status_code, U8 status_code_type, U8 more, U8 do_not_retry);
void uart_set_xmodem_mode(BOOL is_in_xmodem_mode);
void uart_put_char(U8 ubData);


#endif /* _UART_H_ */


