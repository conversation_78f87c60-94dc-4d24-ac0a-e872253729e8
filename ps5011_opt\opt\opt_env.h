#ifndef OPT_ENV_H_
#define OPT_ENV_H_
#include "opt_symbol.h"

/* Configuration Symbol in AndeSight IDE */
#ifdef CATEGORY_CUSTOMER
#define OPT_CATEGORY_CUSTOMER			(CATEGORY_CUSTOMER)
#else /* CATEGORY_CUSTOMER */
#define OPT_CATEGORY_CUSTOMER			(CUSTOMER_MAINSTREAM)
#endif /* CATEGORY_CUSTOMER */

#ifdef CATEGORY_FLASH
#define OPT_CATEGORY_FLASH              (CATEGORY_FLASH) //B47R TLC, N48R QLC
#else /* _CONFIG_FLASH_TYPE_ */
#define OPT_CATEGORY_FLASH              (FLASH_N48R_QLC) //B47R TLC, N48R QLC
#endif /* _CONFIG_FLASH_TYPE_ */

#ifdef CATEGORY_CONTROLLER
#define OPT_CATEGORY_CONTROLLER			(CATEGORY_CONTROLLER) //PS5017 PS5013 
#else /* CATEGORY_CONTROLLER */
#define OPT_CATEGORY_CONTROLLER			(CONTROLLER_PS5021) //PS5017 PS5013
#endif /* CATEGORY_CONTROLLER */

#endif /* OPT_ENV_H_ */

