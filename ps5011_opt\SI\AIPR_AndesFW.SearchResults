---- cpu_comm Matches (54 in 6 files) ----
Opt_arch.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\inc):#define MTP_NOR_MT_CNT		((cpu_comm->MTPool_NOR_MTCnt) ? cpu_comm->MTPool_NOR_MTCnt : 60)
Opt_arch.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\inc):#define MTP_QOS_MT_CNT		((cpu_comm->MTPool_QOS_MTCnt) ? cpu_comm->MTPool_QOS_MTCnt : 60)
Opt_arch.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\inc):#define MTP_ERR_MT_CNT		((cpu_comm->MTPool_ERR_MTCnt) ? cpu_comm->MTPool_ERR_MTCnt : 8)
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	if (cpu_comm->uoARM_Stall_Req_BM & (BIT0 << ubQueueIndex)) {
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	if (cpu_comm->uoARM_Stop_FormMT_Req_BM & (BIT0 << ubQueueIndex)) {
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoARM_Stall_Req_BM &= (~(BIT0 << ubQueueIndex));
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoAndes_Stop_FormMT_BM |= (BIT0 << ubQueueIndex);
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	if (cpu_comm->uoAndes_Stop_FormMT_BM & (BIT0 << ubQueueIndex)) {
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoAndes_Stop_FormMT_BM |= (BIT0 << ubQueueIndex);
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoARM_Stall_Req_BM &= (~(BIT0 << ubQueueIndex));
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoAndes_Stop_FormMT_BM |= (BIT0 << ubQueueIndex);
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoARM_Stop_FormMT_Req_BM &= (~(BIT0 << ubQueueIndex));
Opt_hal.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoAndes_Req_ARM_Directly_Stall_BM |= (BIT0 << ubQueueIndex);
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	_setvalue( cpu_comm->opt_assert_dccm, (m << 16) | n);
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	gOptStruct.enable_die_ilv = cpu_comm->enable_die_ilv;
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	gOptStruct.disable_cache_read = cpu_comm->disable_cache_read;
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	gOptStruct.disable_cache_prog = cpu_comm->disable_cache_prog;
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	if (cpu_comm->uoARM_Stop_FormMT_Req_BM) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	if (cpu_comm->uoARM_Stall_Req_BM) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm = (COP0_COMM_INFO_STRUCT_PTR) ( OPT_DCCM_COMM_BASE);
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	//memset(cpu_comm, 0, sizeof(COP0_COMM_INFO_STRUCT));
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->opt_assert_dccm = 0x01234567;
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):		gOptStruct.disable_cache_read = cpu_comm->disable_cache_read;
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):		gOptStruct.disable_cache_prog = cpu_comm->disable_cache_prog;
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	else if ((COP0_JOB_CMD_PROG == job->cmd) && cpu_comm->SeqProg) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	else if (((COP0_JOB_CMD_PROG == first_job->cmd) && cpu_comm->SeqProg) || ((COP0_JOB_CMD_READ == first_job->cmd) && cpu_comm->SeqRead)) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	else if ((COP0_JOB_CMD_PROG == job->cmd) && cpu_comm->SeqProg) {
Opt_main.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\inc):EXTERN volatile COP0_COMM_INFO_STRUCT_PTR cpu_comm;
Opt_prog.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):			cpu_comm->uoTotalNandWrite++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				//cpu_comm->ub2PCacheStartCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				//cpu_comm->ub1PCacheStartCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				//cpu_comm->ub1PCacheStartCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub2PCacheReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub2PNormalReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub1PCacheReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub1PNormalReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub1PCacheReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub1PNormalReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub1PCacheReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->ub1PNormalReadCnt++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	cpu_comm->uoTotalNandRead++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):	if (cpu_comm->uwReadCntSequentialReadWeightingFactor) {
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):		if ((FALSE == OPTQ_PROC_Q_LOOKUP_RMP_BPS) && (TRUE == cpu_comm->ubEnterFTLTask)) {
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				if (pQueueManager->uwSequentialReadWeightingFactorCnt > cpu_comm->uwReadCntSequentialReadWeightingFactor) {
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				cpu_comm->ubAndesModifyDBUFRequest = TRUE;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				while (TRUE != cpu_comm->ubAndesModifyDBUFRequest);
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):				if (TRUE == cpu_comm->ubARMModifyDBUFRequest) {
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):					cpu_comm->ubAndesModifyDBUFRequest = FALSE;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):					if (((TRUE == OPTQ_PROC_Q_LOOKUP_SLC_MODE) && ((uwReadCnt == cpu_comm->uwSLCReadVerifyReadCntThreshold) || (uwReadCnt == cpu_comm->uwSLCForceSwapUnitReadCntThreshold))) ||
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						((FALSE == OPTQ_PROC_Q_LOOKUP_SLC_MODE) && ((uwReadCnt == cpu_comm->uwTLCReadVerifyReadCntThreshold) || (uwReadCnt == cpu_comm->uwTLCForceSwapUnitReadCntThreshold)))) {
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):						cpu_comm->uwOverReadCntThresholdUnitNum++;
Opt_read.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\aipr\branch_performance\andes\andesbin\edam00.0-f_aipr_branch\ps5011_opt\opt\src):					cpu_comm->ubAndesModifyDBUFRequest = FALSE;
