/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_api.c                                                             */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_API_C_
#include "opt_api.h"
#include "opt_arch.h"

#if SUPPORT_BVCI_WR_IF
void bvci_write (U32 addr, U32 data, U8 by_map)
{
	if (addr & 0x04) {
		OPTQL[OPTQ_AXIM_H4B_WR] = data;
		OPTQB[OPTB_AXIM_WSTRB] = by_map << 4 /*0xF0*/;
		addr &= 0xFFFFFFF8;
	}
	else {
		OPTQL[OPTQ_AXIM_L4B_WR] = data;
		OPTQB[OPTB_AXIM_WSTRB] = by_map /*0x0F*/;
	}

	OPTQL[OPTQ_AXIM_ADDR] = addr;

	OPTQB[OPTB_AXIM_DIR] |= SET_DIR_WRITE;

	OPTQB[OPTB_AXIM_TRIG] |= SET_AXIM_TRIG;

	while (OPTQB[OPTB_AXIM_TRIG] & CHK_AXIM_BUSY);
}


U32 bvci_read (U32 addr)
{
	if (addr & 0x04) {
		OPTQL[OPTQ_AXIM_ADDR] = addr & 0xFFFFFFF8;
	}
	else {
		OPTQL[OPTQ_AXIM_ADDR] = addr;
	}

	OPTQB[OPTB_AXIM_DIR] &= CLR_DIR_READ;

	OPTQB[OPTB_AXIM_TRIG] |= SET_AXIM_TRIG;

	while (OPTQB[OPTB_AXIM_TRIG] & CHK_AXIM_BUSY);

	if (addr & 0x04) {
		return OPTQL[OPTQ_AXIM_H4B_RD];
	}
	else {
		return OPTQL[OPTQ_AXIM_L4B_RD];
	}
}

#endif


void axim_write (U32 addr, U64 data, U8 by_map)
{
	OPTQLL[OPTQ_AXIM_LL8B_WR] = data;
	OPTQB[OPTB_AXIM_WSTRB] = by_map     /*0xFF*/;

	OPTQL[OPTQ_AXIM_ADDR] = addr;

	OPTQB[OPTB_AXIM_DIR] = 1;
	OPTQB[OPTB_AXIM_TRIG] = 1;

	while (OPTQB [OPTB_AXIM_TRIG]);
}


U64 axim_read (U32 addr)
{
	OPTQL[OPTQ_AXIM_ADDR] = addr;
	OPTQB[OPTB_AXIM_DIR] = 0;
	OPTQB[OPTB_AXIM_TRIG] = 1;

	while (OPTQB[OPTB_AXIM_TRIG]);

	return OPTQLL[OPTQ_AXIM_LL8B_RD];
}



