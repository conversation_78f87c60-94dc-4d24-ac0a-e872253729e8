#include "opt_main.h"
#include "opt_arch.h"
#include "opt_const.h"
#include "opt_hal.h"
#include "opt_mt_ext.h"
#include "opt_erase.h"
#include "fpu.h"
#include "opt_debug.h"
#include "nds32_intrinsic.h"
#include "opt_api.h"

__attribute__((optimize("Os")))
static BOOL opt_mt_group_erase(OPT_JOB_STRUCT_PTR job);

BOOL opt_macro_cmd_erase(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (opt_mt_group_erase(job)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}
	return FALSE;
}

BOOL opt_mt_group_erase(OPT_JOB_STRUCT_PTR job)
{
	U32 ulTotalPlaneCnt; // Logical Plane Bank Number
	U32 ulPlaneIdx;
	U32 plane_cnt;
	U32 plane;
	U32 ulOPTInfo = 0;
	U8 ubNeedMTCnt;
	U8 ubPollingSequenceSelect;
	U8 ubProgramPlaneBMP;
	U8 ubMixPlaneBMP;
	U16 uwFPU;

	if (job->ubMixPlaneBMP) {
		ulTotalPlaneCnt = job->ubPlaneCnt;
	}
	else {
		ulTotalPlaneCnt = bit_vld_2_bit_cnt[job->plane_vld];
	}

	ubMixPlaneBMP = (job->ubMixPlaneBMP | BIT(ulTotalPlaneCnt - 1)); //鏈�緦涓��bit闇�鑷繁鑸�

	ubNeedMTCnt = ulTotalPlaneCnt * MT_1_MT_TRIG + (PFA_EN ? 0 : MT_1_MT_TRIG);	// PFA_EN鏅備笉鐢ㄥ涓�櫦MT鍘籶ol fail

	__ASM_VOLATILE_MEMORY__();

	if (OPTOtherCmdStopFormMTCheck(ubNeedMTCnt)) {
		return FALSE;
	}
	__ASM_VOLATILE_MEMORY__();

	for (ulPlaneIdx = 0; ulPlaneIdx < ulTotalPlaneCnt; ulPlaneIdx++) { // ulTotalPlaneCnt means Total Physical Plane Bank Number, Maybe Split to Many CMD Round.
		ubProgramPlaneBMP = 0;
		job->ubPlaneP2LMap	= 0;
		plane_cnt = 1;
		while (FALSE == (BIT(ulPlaneIdx) & ubMixPlaneBMP)) {
			plane_cnt++;
			ulPlaneIdx++;// For Caculate Next CMD Round, Skip This Round Plane Bank
		}

		for (plane = 0; plane <= plane_cnt; plane++) { // plane_cnt means multi Plane Number for this CMD Round.
			ubPollingSequenceSelect = 0;
			hal_optq_wait_curq_valid();
			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD  |
				OPTMTW_FORMMT_ATTR_SEL_TMP   |
				OPTMTW_FORMMT_COPY_ONE_PLANE_FSA   |
				OPTMTW_FORMMT_FSA0_FROM_HEAD       |
				OPTMTW_FORMMT_CMD_MT_TRIG;
			hal_optmt_wait_form_mt();

			if (mtq->dw0.bits.force_w_fail || OPT_CHK_HEAD_GEN_FAIL) {
				job->auto_gen_fail = TRUE;
			}

			//---------- MT TABLE ----------//FPU_ENTRY_ERASE
			if (plane == plane_cnt) {
				// chk status mt
				if (job->auto_gen_fail) {	// multi-plane鏈塸lane瑕乬en fail灏眊en fail, 蹇呴爤鏁呮剰pol ready bit渚嗛�STA error
					uwFPU = M_FPU_GET_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM());
				}
				else {
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
					uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_78_70_CURR_MODE_01);
#else
					uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_70_01_CURR_TLC_MODE);
#endif
				}

				mtq->dw0.bits.busy = 0;
				mtq->dw0.bits.sta_no_stop = 0;

				mtq->dw3.bits.allow_switch = 1;
			}
			else if (plane == (plane_cnt - 1)) {
				// last plane with auto poll
				if (job->slc_mode) {
					uwFPU = ((plane_cnt > 1) && (OPT_CATEGORY_FLASH != FLASH_HYNIX_V8_TLC)) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_slc_erase) + (2 + FPU_NTODT_ITEM_SHIFT_5_BYTE)) : FPU_OFFSET(gFpuEntryList.fpu_entry_slc_erase);
				}
				else {
					uwFPU = (plane_cnt > 1) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_erase) + (2 + FPU_NTODT_ITEM_SHIFT_5_BYTE)) : FPU_OFFSET(gFpuEntryList.fpu_entry_erase);
				}
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)
				ubPollingSequenceSelect  = (PFA_EN) ? ((job->slc_mode) ? POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CURR_SLC_MODE_04 : POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CURR_TLC_MODE_01) : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20; //0xE0;
#else
				ubPollingSequenceSelect  = (PFA_EN) ? M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM()) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()); //0xE0;
#endif
				if(cpu_comm->btPolling_waitrdy){//zerio BICS8 Add
					ubPollingSequenceSelect = POL_SEQ_FPU_ENTRY_READ_STATUS_78_BUSY_40;
				}

				mtq->dw0.bits.pfa_int_en = PFA_EN;
				if (PFA_EN) {
					// mtd->dw1_dat.bits.nor_cq_rsp = X,  鐢眂md in姹哄畾瑕佷笉瑕佸洖CQ
					plane = plane_cnt; // PFA鐩存帴pol fail, 鎵�互瑷畾鐐簆lane_cnt, 璁撲笅闈㈢洿鎺elease parameter ram璺焢op opt cmd
					mtq->dw3.bits.allow_switch = 1;
					if (job->auto_gen_fail) {	// multi-plane鏈塸lane瑕乬en fail灏眊en fail
						mtq->dw0.bits.force_w_fail = TRUE;
					}
				}
				else {
					mtd->dw1_dat.bits.nor_cq_rsp = 0;
				}

				mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = 1;  // cmd涓嬪畬鏈冭畵CE busy鐨凪T鍎厛鍋�(high priority)
			}
			else {
				if (0 == plane) {
					uwFPU = ((job->slc_mode) ? FPU_OFFSET(gFpuEntryList.fpu_entry_slc_60h_row_erase) : FPU_OFFSET(gFpuEntryList.fpu_entry_tlc_60h_row_erase));
					if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
						M_OPT_SET_MT_TEMPLATE_FIRST_OP();
					}
				}
				else {
					if ((job->slc_mode) && (OPT_CATEGORY_FLASH == FLASH_HYNIX_V8_TLC))
					{
						uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_60h_row_erase);
					}else
					{
						uwFPU = (FPU_OFFSET(gFpuEntryList.fpu_entry_slc_60h_row_erase) + (2 + FPU_NTODT_ITEM_SHIFT_5_BYTE));
					}
				}

				mtq->dw0.bits.busy = 0;
				//mtd->dw1_dat.bits.nor_cq_rsp = 1;

				if (OPT_CHK_HEAD_PROGRAM_PARITY_USERDEFINE) {
					// avoid deadlock, mt_prio_lck can't be set
				}
				else {
					mtd->dw2_mt_cfg1.bits.mt_hard_lock = 1;
				}
			}

			job->ubPlaneP2LMap |= (((OPTQL[OPTQ_PROC_Q_HEAD_PCA] >> COP0_PLANE_START_POINT(COP0_PCA_RULE_0)) & gubPlaneMask) << (OPT_HEAD_PLANE_ADDR << 1));

			if (plane == plane_cnt) {
				ulOPTInfo = (job->ubRMP_bypass) ? OPT_INFO_RMP_BYPASS : OPT_INFO_RMP_USE;
				ulOPTInfo |= OPT_INFO_POL_FAIL;
				if (job->auto_gen_fail) {
					ulOPTInfo |= OPT_INFO_GEN_FAIL;
				}
				mtq->dw13_iFSA2 = (ulOPTInfo | (job->ubPlaneP2LMap << OPT_INFO_PLANE_SHIFT) | OPT_INFO_POL_FAIL_MARK);
			}
			else {
				mtq->dw3.bits.erase = 1;
				mtq->dw13_iFSA2 = ((job->ubPlaneP2LMap << OPT_INFO_PLANE_SHIFT) | OPT_INFO_POL_FAIL_MARK);
			}
			// 閬垮厤ultra W/R闁嬪暉鏅� 灏庤嚧閫欑瓎CQ姣攗ltraDMA鍓嶉潰涓�瓎鍏堝畬鎴� (ex: multi-plane erase 鍙仛plane A閫犳垚MT hard lock閹栦綇閫欏�queue, 鐒℃硶浣渞ead error handle鐨剄ueue)
			mtq->dw0.bits.ultra_w_en = 0;
			mtq->dw1.bits.dis_udma = 1;
			mtq->dw8_userdefine = (ulOPTInfo | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);

			//---------- TRIGGER DATA ----------//
			if (plane != (plane_cnt - 1)) {
				mtd->dw0_dat.bits.par_rls = 1;
			}

			M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);
			ubProgramPlaneBMP |= OPT_HEAD_PLANE_BIT;
			_SET_PROG_OPT_STATUS_(0, ubProgramPlaneBMP);
			M_SET_OPTCM_FPU_POL_SEQ(uwFPU, ubPollingSequenceSelect);

			_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
			hal_optmt_trigger();

			if (plane != (plane_cnt - 1)) {
				hal_optq_q_pop();
			}
		}
	}
	if (PFA_EN) {
		if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
			hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// 鍥犵偤HW bug, 鐢ㄤ締鍙栦唬MT trigger data鐨凪TP_stall
			hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
		}
	}
	return TRUE;
}
