#include "opt_global.h"

#if OPT_PS8311_ONLY
#include "shr_types.h"

//////////////////////////////////////////////////////////////////////
#define UART_REG    ((volatile U8 *)0xF8FA0000) // 8311

//////////////////////////////////////////////////////////////////////
// UART_REG
// 8 bits offset

#define UART0_ID0           (0x00 >> 0)     // [7:0], [31:8] reserved
#define UART0_ID1           (0x04 >> 0)     // [7:0], [31:8] reserved
#define UART0_ID2           (0x08 >> 0)     // [7:0], [31:8] reserved
#define UART0_ID3           (0x0C >> 0)     // [7:0], [31:8] reserved

#define UART0_RX            (0x10 >> 0)     // [7:0], [31:8] reserved
#define UART0_TX            (0x10 >> 0)     // [7:0], [31:8] reserved

#define UART0_STATUS        (0x14 >> 0)
// reserved                 [31:8]
#define CHK_TX_EMPTY        CHK_BIT7
#define SET_EN_TX_INT       SET_BIT6
#define CHK_RX_EMPTY        CHK_BIT5
#define CHK_RX_FULL_1       CHK_BIT4
#define CHK_RX_FULL         CHK_BIT3
#define SET_EN_RX_INT       SET_BIT2
#define CHK_RX_OVERFLOW     CHK_BIT1
#define CHK_RX_FRAME_ERR    CHK_BIT0

#define UART0_BAUD_L        (0x18 >> 0)     // [7:0], [31:8] reserved
#define UART0_BAUD_H        (0x1C >> 0)     // [7:0], [31:8] reserved

//////////////////////////////////////////////////////////////////////
// baud rate

//#define SYS_CLK             30  // MHz
#if OPT_PS5008_ONLY
#define SYS_CLK             10  // MHz
#else
#define SYS_CLK             30  // MHz
#endif

#define BAUDRATE(x)         (((SYS_CLK * ((8*1000*1000) / x)) >> 5) - 1)

#define BAUDRATE_9600       BAUDRATE(9600)
#define BAUDRATE_11400      BAUDRATE(11400)
#define BAUDRATE_19200      BAUDRATE(19200)
#define BAUDRATE_38400      BAUDRATE(38400)
#define BAUDRATE_56000      BAUDRATE(56000)
#define BAUDRATE_57600      BAUDRATE(57600)
#define BAUDRATE_115200     BAUDRATE(115200)

extern void uart_init(U32 baudrate);

#endif