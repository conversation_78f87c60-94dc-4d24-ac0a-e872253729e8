/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  E21_opt_arch.h                                                        */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _E21_OPT_ARCH_H_
#define _E21_OPT_ARCH_H_

//#include "shr_types.h"
#include "misc/types.h"
#include "opt_global.h"
#include "seedinit_tbl.h"

#ifdef _OPT_ARCH_C_
#define EXTERN
#else
#define EXTERN extern
#endif

// -------------------------------------------------
// DCCM : 0x0020_0000 (57344 Byte)
// -------------------------------------------------
/*  _______
* |_______| MT_POOL for HW use 0x200000 ~ 0x205400 (A. MT pool for HW use) 0x5400
* |_______| GLOBAL VARIABLE    0x205400 ~ 0x207400 (B. Reserved for Andes global variable) 0x2000
* |_______| Read Info          0x207400 ~ 0x207880 (C. Record FSA for lite patch cmd) 0x480
* |_______| Read Disturb       0x207880 ~ 0x209100 (D. Reserved for Read Disturb) 0x1880
* |_______| debug info         0x209100 ~ 0x209550 (F. Info for Andes debug) 0x450
* |_______| STACK              0x209550 ~ 0x209950 (E. STACK) 0x400
* |_______| CPBK base          0x209950 ~ 0x209950 ()
* |_______| COMM               0x209950 ~ 0x2099D0 (G. 聊天室) 0x80
* |_______| No Use             0x2099D0 ~ 0x20DFFF (H. No use)
 *
 * */

/*
 * Memory Mapping Definitions and Register Base
 */
#define OPT_REG_BASE                            (0x8010A000)
#define OPT_DCCM_RAM_BASE                          (0x00200000)  //(0x00200000)~(0x0020DFFF)
#define OPT_DCCM_RAM_LENS                          (0xE000)//56K

//===================================================================//
#define OPT_DCCM_READ_INFO_BASE			(0x00207400)	// 在GLOBAL_LENS的範圍內
#if (OPT_DCCM_READ_INFO_BASE < 0x00207400)
#error "Wrong OPT_DCCM_READ_INFO_BASE BASE , check .sag define"
#endif
#define OPT_DCCM_READ_INFO_SIZE			(0x480)	// 每個queue 36B * 32 queue,  (4B FSA * 4 plane * 2組) + 2B  plane bitmap, 2bit ALU rule, 14bits reserved

#define OPT_DCCM_READ_DISTURB_BASE                   (OPT_DCCM_READ_INFO_BASE + OPT_DCCM_READ_INFO_SIZE)
#define OPT_DCCM_READ_DISTURB_LENS                   (OPT_DCCM_READ_CNT_LOW_BYTE_LENS+OPT_DCCM_READ_CNT_HIGH_BYTE_LENS+OPT_DCCM_READ_DISTURB_THRESHOLD_LENS)

#if (OPT_DCCM_READ_DISTURB_BASE != 0x000207880)
#error "Wrong OPT_DCCM_READ_DISTURB_BASE"
#endif

/* read disturb 分三段: read cnt low byte, read cnt high byte, read disturb threshold */
#define OPT_DCCM_READ_CNT_LOW_BYTE_LENS                   (0x1000)
#define OPT_DCCM_READ_CNT_HIGH_BYTE_LENS                  (0x800)
#define OPT_DCCM_READ_DISTURB_THRESHOLD_BASE              (OPT_DCCM_READ_DISTURB_BASE + OPT_DCCM_READ_CNT_LOW_BYTE_LENS + OPT_DCCM_READ_CNT_HIGH_BYTE_LENS)
#define OPT_DCCM_READ_DISTURB_THRESHOLD_LENS              (0x80)
/* read disturb */

#define OPT_DCCM_DEBUG_BASE                        (OPT_DCCM_READ_DISTURB_BASE + OPT_DCCM_READ_DISTURB_LENS)
#define OPT_DCCM_DEBUG_LENS                        (0x450 - OPT_DCCM_CPBK_LENS)

#if (OPT_DCCM_DEBUG_BASE != 0x00209100)
#error "Wrong OPT_DCCM_DEBUG_BASE"
#endif

#if OPT_SUPPORT_INTERNAL_COPYBACK
#define OPT_DCCM_CPBK_LENS                         (0x00000100)
#else
#define OPT_DCCM_CPBK_LENS                         (0)
#endif
#define OPT_DCCM_CPBK_BASE                         (OPT_DCCM_DEBUG_BASE + OPT_DCCM_DEBUG_LENS)

#define OPT_DCCM_STACK_BASE                        (OPT_DCCM_CPBK_BASE + OPT_DCCM_CPBK_LENS)
#define OPT_DCCM_STACK_LENS                        (0x400)

#define OPT_DCCM_COMM_BASE                         (OPT_DCCM_STACK_BASE + OPT_DCCM_STACK_LENS)             // 0x00209950
#define OPT_DCCM_COMM_LENS                         (0x88)

#define OPT_DCCM_BANK_BASE                         (OPT_DCCM_COMM_BASE + OPT_DCCM_COMM_LENS)             // 0x002099D0
#define SPACE_FOR_BANKING                       	(0x28)

#define OPT_DCCM_SEED_INIT_TABLE_BASE				(OPT_DCCM_BANK_BASE + SPACE_FOR_BANKING)
#define OPT_DCCM_SEED_INIT_TABLE_LENS				(0x200) // 128 * 4

#define OPT_DCCM_BIN_VALUE_TABLE_BASE				(OPT_DCCM_SEED_INIT_TABLE_BASE + OPT_DCCM_SEED_INIT_TABLE_LENS)
#define OPT_DCCM_BIN_VALUE_TABLE_LENS				(0x8)

#if (OPT_DCCM_STACK_BASE != 0x00209550)
#error "Wrong STACK BASE, .sag define STACK range 0x209550 to 0x209950!"
#endif

#if (OPT_DCCM_COMM_BASE != 0x00209950)
#error "Wrong OPT_DCCM_COMM_BASE"
#endif

#if (OPT_DCCM_SEED_INIT_TABLE_BASE != 0x00209A00)
#error "Wrong OPT_DCCM_SEED_INIT_TABLE_BASE"
#endif /* (OPT_DCCM_SEED_INIT_TABLE_BASE != 0x00209A00) */

#if (OPT_DCCM_BIN_VALUE_TABLE_BASE != 0x00209C00)
#error "Wrong OPT_DCCM_BIN_VALUE_TABLE_BASE"
#endif /* (OPT_DCCM_BIN_VALUE_TABLE_BASE != 0x00209C00) */

#if ((OPT_DCCM_BIN_VALUE_TABLE_BASE + OPT_DCCM_BIN_VALUE_TABLE_LENS) > (OPT_DCCM_RAM_BASE + OPT_DCCM_RAM_LENS))
#error "DCCM overflow!"
#endif

#define OPT_S_ASSERT_BASE                       (0x0000E000 - 4)
#define TRIGGER_CNT_PER_BANKING_REQUEST         (3)
#define CPU_CNT                                 (2) //cpu0 and cpu1
#define NO_BANKING_REQUEST                      (0xFFFFFFFF)

// -------------------------------------------------
// MT_POOL
// -------------------------------------------------
#define MTP_NOR_MT_CNT		(224)
#define MTP_QOS_MT_CNT		(16)
#define MTP_ERR_MT_CNT		(16)

#if ((MTP_NOR_MT_CNT % 4 == 3) || (MTP_QOS_MT_CNT % 4 == 3) || (MTP_ERR_MT_CNT % 4 == 3))
#error "E21 resource cannot be 4n+3"
#endif /*((MTP_NOR_MT_CNT % 4 == 3) || (MTP_QOS_MT_CNT % 4 == 3) || (MTP_ERR_MT_CNT % 4 == 3))*/

#define OPT_D_MTP_MT_BASE	(0)	// from 0x00080000
#define MTP_MT_SIZE			(64)
#define OPT_D_MTP_NOR_BASE		(OPT_D_MTP_MT_BASE)
#define OPT_D_MTP_QOS_BASE		(OPT_D_MTP_NOR_BASE + (MTP_NOR_MT_CNT * MTP_MT_SIZE))
#define OPT_D_MTP_ERR_BASE			(OPT_D_MTP_QOS_BASE + (MTP_QOS_MT_CNT * MTP_MT_SIZE))
#define OPT_D_MTP_MT_LENS               (MTP_NOR_MT_CNT + MTP_QOS_MT_CNT + MTP_ERR_MT_CNT) * (MTP_MT_SIZE)

#define OPT_D_MTP_TRIGGERDATA_BASE 	(OPT_D_MTP_MT_BASE + OPT_D_MTP_MT_LENS)
#define MTP_TRIGGERDATA_SIZE			(16)
#define OPT_D_MTP_TRIGGERDATA_NOR_BASE		(OPT_D_MTP_TRIGGERDATA_BASE)
#define OPT_D_MTP_TRIGGERDATA_QOS_BASE		(OPT_D_MTP_TRIGGERDATA_NOR_BASE + (MTP_NOR_MT_CNT * MTP_TRIGGERDATA_SIZE))
#define OPT_D_MTP_TRIGGERDATA_ERR_BASE		(OPT_D_MTP_TRIGGERDATA_QOS_BASE + (MTP_QOS_MT_CNT * MTP_TRIGGERDATA_SIZE))
#define OPT_D_MTP_TRIGGERDATA_LENS                     (MTP_NOR_MT_CNT + MTP_QOS_MT_CNT + MTP_ERR_MT_CNT) * (MTP_TRIGGERDATA_SIZE)

#define OPT_D_MTP_LL_BASE			(OPT_D_MTP_TRIGGERDATA_BASE + OPT_D_MTP_TRIGGERDATA_LENS)
#define OPT_D_MTP_LL_NOR_BASE		(OPT_D_MTP_LL_BASE)
#define OPT_D_MTP_LL_QOS_BASE		(OPT_D_MTP_LL_NOR_BASE + ((MTP_NOR_MT_CNT +  MTP_ERR_MT_CNT + 7)/8*8))		// 8B align

#define OPT_D_MTP_RSC_BASE			(OPT_D_MTP_LL_QOS_BASE + ((MTP_QOS_MT_CNT +  MTP_ERR_MT_CNT + 7)/8*8))	// 8B align
#define OPT_D_MTP_RSC_NOR_BASE		(OPT_D_MTP_RSC_BASE)
#define OPT_D_MTP_RSC_QOS_BASE			(OPT_D_MTP_RSC_NOR_BASE + ((MTP_NOR_MT_CNT + 7)/8*8))
#define OPT_D_MTP_RSC_ERR_BASE			(OPT_D_MTP_RSC_QOS_BASE + ((MTP_QOS_MT_CNT + 7)/8*8))

#define OPT_FIP_IRAM_BASE                       (0x80180000)

#define OPT_Q_INFO_REG                          (OPT_REG_BASE + 0x000) // 0x8010A000
#define OPT_CUR_MT_REG                          (0x0020F000) //shadow memory. For MT pool

#define OPTQB                                   ((_V U8  *)OPT_Q_INFO_REG)
#define OPTQW                                   ((_V U16 *)OPT_Q_INFO_REG)
#define OPTQL                                   ((_V U32 *)OPT_Q_INFO_REG)
#define OPTQLL                                  ((_V U64 *)OPT_Q_INFO_REG)

#define OPTCMB                                  ((_V U8  *)OPT_CUR_MT_REG)
#define OPTCMW                                  ((_V U16 *)OPT_CUR_MT_REG)
#define OPTCML                                  ((_V U32 *)OPT_CUR_MT_REG)
#define OPTCMLL                                 ((_V U64 *)OPT_CUR_MT_REG)

#define DBUF_ERASE_CNT_BASE                    	(0x2236F000)

#define ERASE_CNT_UNIT_MASK           (0xFFFFFFF8)
#define ERASE_CNT_STRUCT_SIZE           (4)
/*
 * OPT Q_INFO registers
 */

#define OPTQL_OPT_Q_INFO_0				(0x00>>2)
#define OPTQB_CURQ_VALID                       (0x00)  //PROC_Q_HEAD_ELEMENT_VALID & PROC_Q_HEAD_ELEMENT_RDY

#define OPTQB_Q_POP                                (0x01)  //PROC_Q_POP
#define   SET_PROC_Q_POP                            SET_BIT0

#define OPTQB_LOOKUP_PTR_UPDATE           (0x02)  //PROC_Q_LOOKUP_PTR

#define OPTQB_LOOKUP_Q_VALID                (0x03)  //PROC_Q_LOOKUP_ELEMENT_VALID & PROC_Q_LOOKUP_ELEMENT_RDY
#define 	OPTQB_ELEMENT_VALID                    (BIT0)
#define 	OPTQB_ELEMENT_RDY                       (BIT1)
/* CN 版PROC_Q_LOOKUP_CONVPAGE_RDY為bit 27, spec寫bit 26有誤且無HEAD_CONVPAGE_RDY */
#define 	OPTQB_CONVPAGE_RDY            	    (BIT3)   // CONVPAGE_RDY較ELEMENT_RDY慢, 建議要看parity時再check CONVPAGE_RDY | OPTQB_ELEMENT_RDY, 平常只check OPTQB_ELEMENT_RDY  
#define 	OPTQB_LOOKUP_Q_TIMEOUT             (BIT7)

#define ELEMENT_VALID_MASK                      (0x03)  //(OPTQB_ELEMENT_VALID | OPTQB_ELEMENT_RDY)
#define ELEMENT_RS_VALID_MASK                 (0x0F)  //(OPTQB_ELEMENT_VALID | OPTQB_ELEMENT_RDY |OPTQB_CONVPAGE_RDY), 目前bit 26等同bit 1, 所以bit 24~27同時都會被拉

#define CHK_PROC_Q_TIMEOUT                      CHK_BIT7

#define OPTQB_STOP_PROC_Q                      (0x05)
#define STOP_PROC_Q     					    SET_BIT0

#define OPTQL_SWITCHQ                           (0x08 >> 2)   //OPT_SWITCHQ (Offset: 00Bh~008h)

#define OPTQB_SWITCHQ_ARBITER_VALID             (0x08)
#define CHK_SWITCHQ_ARB_VALID                   CHK_BIT0

#define OPTQB_SWITCHQ_ARBITER_RESULT            (0x09)

#define OPTQB_SWITCHQ                           (0x0A)
#define SET_SWITCHQ_QOS                         SET_BIT5

#define OPTQB_PROC_Q                            (0x0B)
#define CHK_PROC_Q_ID                           (0x1F)
#define CHK_PROC_Q_QOS                          CHK_BIT5
#define SET_PROC_Q_QOS                          SET_BIT5

#define OPTQB_PROC_Q_TIMEOUT                    (0x0C)
#define OPTQB_PROC_Q_MTQ_TRIG_CNT               (0x0D)
#define OPTQB_PROC_Q_MTQ_TRIG_FULL              (0x0E)
#define OPTQB_PROC_CMD_Q_ELEMENT_CNT            (0x0F)
#define OPTQB_PROC_CMD_Q_ELEMENT_CNT_MASK       (0x3F)


#define OPTQL_Q_INFO_1                          (0x0C>>2)   //OPT_Q_INFO_1 (Offset: 00Fh~00Ch), RO.

#define OPTQ_PROC_Q_TIMEOUT                     (BIT0)  // Current processing Q timeout.
#define OPTQ_PROC_Q_MTQ_TRIG_FULL               (BIT16) // Current processing MTQ's trigger count > (MAX-2). ( -2 for reuse MT trigger.)

#define OPTQL_PROC_Q_HEAD_CMD_ATTR              (0x10>>2)

//CMD
#define     OPTQB_PROC_Q_HEAD_ELEMENT_CMD       (0x10)
#define     OPTQ_PROC_Q_HEAD_CMD                (OPTQL[OPTQL_PROC_Q_HEAD_CMD_ATTR] & (BITMSK(3, 0)))
#define     OPTQ_PROC_Q_HEAD_CMD_SLC            (OPTQL[OPTQL_PROC_Q_HEAD_CMD_ATTR] & (BITMSK(4, 0)))
#define     OPTQ_PROC_Q_HEAD_SLC_MODE           ((OPTQL[OPTQL_PROC_Q_HEAD_CMD_ATTR] & BIT3) >> 3)
#define 	  OPTQ_PROC_Q_HEAD_RMP_BPS               ((OPTQL[OPTQL_PROC_Q_HEAD_CMD_ATTR] & (BITMSK(1, 6))) >> 6)

//ATTR
#define     OPT_GET_PBNA                               (BIT27)
#define     OPT_ATTR_SGN_DIS                           (BIT26)
#define     OPT_ATTR_READ_TIEOUT_METHOD               (BIT25)
#define     OPT_ATTR_READ_BACKUP_P4K_WRITE_KEEP_P4K   (BIT24)
#define     OPT_ATTR_WRITE               (BIT23)
#define     OPT_ATTR_MT_CLK_SEL         (BIT22)
#define     OPT_ATTR_FORCE_EMPTY        (BIT21)                 // 1: MT table set force empty
#define     OPT_ATTR_NORMAL_CQ_RESPONSE        (BIT20)                 // 0: response only if error, 1: always response
#define     OPT_ATTR_Q_LINK_PRIORITY    (BIT18|BIT19)           // 0: low, 1: mid, 2: high
#define     OPT_ATTR_RND_SEED_MODE   (BIT16|BIT17)
#define     OPT_ATTR_BMU_ALCT_EN        (BIT15)
#define     OPT_ATTR_SERIAL               (BIT14)
#define     OPT_ATTR_ZIP_EN            (BIT13)
#define     OPT_ATTR_QOS               (BIT12)
#define     OPT_ATTR_MT_TEMPLATE_SEL    (BITMSK(4, 8))

//PAGE VALID
#define     OPTQ_PROC_Q_HEAD_FRAME_VLD          ((OPTQL[OPTQL_PROC_Q_HEAD_CMD_ATTR] & (BITMSK(4, 28))) >> 28)

//PCA
#define OPTQ_PROC_Q_HEAD_PCA                    (0x14>>2)

//USERDEF
/*
//#define OPTQ_PROC_Q_HEAD_USRDEF                 (0x18>>2)
#define OPTQW_PROC_Q_HEAD_USRDEF                (0x18>>1)
#define OPTQ_PROC_Q_HEAD_USRDEF_INFO            (OPTQW[OPTQW_PROC_Q_HEAD_USRDEF])
*/

#define OPTQ_PROC_Q_HEAD_USRDEF                 (0x18)
#define OPTQ_PROC_Q_HEAD_USRDEF_INFO            (OPTQW[(OPTQ_PROC_Q_HEAD_USRDEF >> 1)])
#define OPTQ_PROC_Q_HEAD_IOR_GRP()                (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 2)] & (BITMSK(3, 0)))
#define OPTQ_PROC_Q_HEAD_IOR_EN()                 (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 2)] & (BITMSK(1, 3)))
#define OPTQ_PROC_Q_HEAD_BAR_PRI()                (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 2)] & (BITMSK(2, 4)))
#define OPTQ_PROC_Q_HEAD_BAR_SEL()                (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 2)] & (BITMSK(1, 6)))
#define OPTQ_PROC_Q_HEAD_BAR_EN()                 (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 2)] & (BITMSK(1, 7)))
#define OPTQ_PROC_Q_HEAD_D1_SLC_MTHOD()           (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 0)))
#define OPTQ_PROC_Q_HEAD_D1_MDOE()                (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 1)))
#define OPTQ_PROC_Q_HEAD_BBRMP_BPS()              (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 2)))
#define OPTQ_PROC_Q_HEAD_OPTC_MARK()              (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 4)))
#define OPTQ_PROC_Q_HEAD_NO_WAIT()                (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 5)))
#define OPTQ_PROC_Q_HEAD_LB_NBIT()                (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 6)))
#define OPTQ_PROC_Q_HEAD_LB_CHK()                 (OPTQB[(OPTQ_PROC_Q_HEAD_USRDEF + 3)] & (BITMSK(1, 7)))

#define OPTQ_PROC_Q_HEAD_RMP_PCA                (0x1C >> 2)

#define OPTQ_PROC_Q_LOOKUP_CMD_ATTR             (0x20>>2)

//CMD
#define     OPTQB_PROC_Q_LOOKUP_ELEMENT_CMD       (0x20)
#define     OPTQ_PROC_Q_LOOKUP_CMD              (OPTQL[OPTQ_PROC_Q_LOOKUP_CMD_ATTR] & (BITMSK(3, 0)))
#define     OPTQ_PROC_Q_LOOKUP_CMD_SLC          (OPTQL[OPTQ_PROC_Q_LOOKUP_CMD_ATTR] & (BITMSK(4, 0)))
#define     OPTQ_PROC_Q_LOOKUP_SLC_MODE         ((OPTQL[OPTQ_PROC_Q_LOOKUP_CMD_ATTR] & BIT3) >> 3)
#define 	  OPTQ_PROC_Q_LOOKUP_RMP_BPS               ((OPTQL[OPTQ_PROC_Q_LOOKUP_CMD_ATTR] & (BITMSK(1, 6))) >> 6)

//PAGE VALID
#define     OPTQ_PROC_Q_LOOKUP_FRAME_VLD        ((OPTQL[OPTQ_PROC_Q_LOOKUP_CMD_ATTR] & (BITMSK(4, 28))) >> 28)

//PCA
#define OPTQ_PROC_Q_LOOKUP_PCA                  (0x24>>2)

//USERDEF

#define OPTQ_PROC_Q_LOOKUP_USRDEF               (0x28 >> 2)
#define OPTQB_PROC_Q_LOOKUP_USRDEF               (0x28)
#define OPTQ_PROC_Q_LOOKUP_USRDEF_INFO            (OPTQW[(OPTQB_PROC_Q_LOOKUP_USRDEF >> 1)])
#define OPTQ_PROC_Q_LOOKUP_IOR_GRP()              (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 2)] & (BITMSK(3, 0)))
#define OPTQ_PROC_Q_LOOKUP_IOR_EN()               (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 2)] & (BITMSK(1, 3)))
#define OPTQ_PROC_Q_LOOKUPD_BAR_PRI()             (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 2)] & (BITMSK(2, 4)))
#define OPTQ_PROC_Q_LOOKUP_BAR_SEL()              (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 2)] & (BITMSK(1, 6)))
#define OPTQ_PROC_Q_LOOKUP_BAR_EN()               (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 2)] & (BITMSK(1, 7)))
#define OPTQ_PROC_Q_LOOKUP_D1_SLC_MTHOD()         (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 0)))
#define OPTQ_PROC_Q_LOOKUP_D1()                   (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 1)))
//#define OPTQ_PROC_Q_LOOKUP_BBRMP_BPS()            (OPTQB[(OPTQ_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 2)))
#define OPTQ_PROC_Q_LOOKUP_BBRMP_BPS               ((OPTQL[OPTQ_PROC_Q_LOOKUP_USRDEF] & (BITMSK(1, 26))) >> 26)
#define OPTQ_PROC_Q_LOOKUP_OPTC_MARK()            (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 4)))
#define OPTQ_PROC_Q_LOOKUP_NO_WAIT()              (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 5)))
#define OPTQ_PROC_Q_LOOKUP_LB_NBIT()              (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 6)))
#define OPTQ_PROC_Q_LOOKUP_LB_CHK()               (OPTQB[(OPTQB_PROC_Q_LOOKUP_USRDEF + 3)] & (BITMSK(1, 7)))

#define OPTQ_PROC_Q_LOOKUP_RMP_PCA              (0x2C >> 2)

#define R32_OPT_PROC_Q_PAGE_VLD               (0x30 >> 2)
#define R8_OPT_PROC_Q_CUR_PAGE_VLD_0         (0x30)
#define R8_OPT_PROC_Q_CUR_PAGE_VLD_1         (0x31)
#define R8_OPT_PROC_Q_LOOKUP_PAGE_VLD_0      (0x32)
#define R8_OPT_PROC_Q_LOOKUP_PAGE_VLD_1      (0x33)

#define OPTQ_PROC_Q_TIMEOUT_CNT                 (0x34 >> 2)   //3.3.12    OPT_PROC_Q_TIMEOUT_CNT -> - Timeout counter of processing queue, any write value will clear timeout.
#define OPTQ_PROC_Q_TIMEER                      (0x34>>1)

#define READ_TIMER_COUNT                        (0)
#define WRITE_TIMER_COUNT                       (0)
#define ERASE_TIMER_COUNT                       (0)

#define OPTQB_SET_READ_TIMER_COUNT              {OPTQW[OPTQ_PROC_Q_TIMEER] = (READ_TIMER_COUNT);}
#define OPTQB_SET_WRITE_TIMER_COUNT             {OPTQW[OPTQ_PROC_Q_TIMEER] = (WRITE_TIMER_COUNT);}
#define OPTQB_SET_ERASE_TIMER_COUNT             {OPTQW[OPTQ_PROC_Q_TIMEER] = (ERASE_TIMER_COUNT);}

#define OPTQL_OPT_CMD_HEAD_PTR                       (0x38>>2)
#define OPTQL_OPT_CMD_LOOKUP_PTR                     (0x3A>>2)
#define OPTQL_OPT_CMD_PTR                       (0x38 >> 2)
#define OPTQW_HEAD_CMD_PTR                      (0x38 >> 1)
#define OPTQW_LOOKUP_CMD_PTR                    (0x3A >> 1)

#define OPTQ_OPT_HEAD_CMD_PTR                   ((OPTQL[OPTQL_OPT_CMD_HEAD_PTR]) & 0x3FF)
#define OPTQ_OPT_LOOKUP_CMD_PTR                 ((OPTQL[OPTQL_OPT_CMD_LOOKUP_PTR]) & 0x3FF)

#define OPTQL_PROC_Q_WRITABLE_COUNT            (0x3C>>2)  // OPT_PROC_Q_WRIABLE_COUNT (Offset: 03Fh~03Ch)
#define OPTQB_PROC_Q_WRITABLE_COUNT               (0x3C)
#define OPTQ_PROC_Q_WRITABLE_COUNT                *((volatile U8*) (OPT_Q_INFO_REG + OPTQB_PROC_Q_WRITABLE_COUNT))
#define SET_QOS_MUX_EN                          SET_BIT24
#define CLR_NORMAL_MUX_EN                       CLR_BIT24

#define OPTQB_WRITABLE_CNT                      (0x40)
#define OPTQ_WRITABLE_CNT(n)                    *((volatile U8*) (OPT_Q_INFO_REG + OPTQB_WRITABLE_CNT + (n)))

#define OPTQL_WRIABLE_COUNT_3_0                 (0x40>>2)  // OPT_ WRIABLE_COUNT_3_0 (Offset: 043h~040h)
#define OPTQB_WRITABLE_COUNT_0                  (0x40)
#define OPTQB_WRITABLE_COUNT_1                  (0x41)
#define OPTQB_WRITABLE_COUNT_2                  (0x42)
#define OPTQB_WRITABLE_COUNT_3                  (0x43)

#define OPTQL_WRIABLE_COUNT_7_4                 (0x44>>2)  // OPT_ WRIABLE_COUNT_7_4 (Offset: 047h~044h)
#define OPTQB_WRITABLE_COUNT_4                  (0x44)
#define OPTQB_WRITABLE_COUNT_5                  (0x45)
#define OPTQB_WRITABLE_COUNT_6                  (0x46)
#define OPTQB_WRITABLE_COUNT_7                  (0x47)

#define OPTQL_WRIABLE_COUNT_11_8                (0x48>>2)  // OPT_ WRIABLE_COUNT_11_8 (Offset: 04Bh~048h)
#define OPTQB_WRITABLE_COUNT_8                  (0x48)
#define OPTQB_WRITABLE_COUNT_9                  (0x49)
#define OPTQB_WRITABLE_COUNT_10                 (0x4A)
#define OPTQB_WRITABLE_COUNT_11                 (0x4B)

#define OPTQL_WRIABLE_COUNT_15_12               (0x4C>>2)  // OPT_ WRIABLE_COUNT_15_12 (Offset: 04Fh~04Ch)
#define OPTQB_WRITABLE_COUNT_12                 (0x4C)
#define OPTQB_WRITABLE_COUNT_13                 (0x4D)
#define OPTQB_WRITABLE_COUNT_14                 (0x4E)
#define OPTQB_WRITABLE_COUNT_15                 (0x4F)

#define OPTQL_WRIABLE_COUNT_19_16               (0x50>>2)  // OPT_ WRIABLE_COUNT_19_16 (Offset: 053h~050h)
#define OPTQB_WRITABLE_COUNT_16                 (0x50)
#define OPTQB_WRITABLE_COUNT_17                 (0x51)
#define OPTQB_WRITABLE_COUNT_18                 (0x52)
#define OPTQB_WRITABLE_COUNT_19                 (0x53)

#define OPTQL_WRIABLE_COUNT_23_20               (0x54>>2)  // OPT_ WRIABLE_COUNT_23_20 (Offset: 057h~054h)
#define OPTQB_WRITABLE_COUNT_20                 (0x54)
#define OPTQB_WRITABLE_COUNT_21                 (0x55)
#define OPTQB_WRITABLE_COUNT_22                 (0x56)
#define OPTQB_WRITABLE_COUNT_23                 (0x57)

#define OPTQL_WRIABLE_COUNT_27_24               (0x58>>2)  // OPT_ WRIABLE_COUNT_27_24 (Offset: 05Bh~058h)
#define OPTQB_WRITABLE_COUNT_24                 (0x58)
#define OPTQB_WRITABLE_COUNT_25                 (0x59)
#define OPTQB_WRITABLE_COUNT_26                 (0x5A)
#define OPTQB_WRITABLE_COUNT_27                 (0x5B)

#define OPTQL_WRIABLE_COUNT_31_28               (0x5C>>2)  // OPT_ WRIABLE_COUNT_31_28 (Offset: 05Fh~05Ch)
#define OPTQB_WRITABLE_COUNT_28                 (0x5C)
#define OPTQB_WRITABLE_COUNT_29                 (0x5D)
#define OPTQB_WRITABLE_COUNT_30                 (0x5E)
#define OPTQB_WRITABLE_COUNT_31                 (0x5F)

#define OPTQL_WRIABLE_COUNT_QOS_3_0               (0x60>>2)  // OPT_ WRIABLE_COUNT_35_32 (Offset: 063h~060h)
#define OPTQB_WRITABLE_COUNT_QOS_0                 (0x60)
#define OPTQB_WRITABLE_COUNT_QOS_1                 (0x61)
#define OPTQB_WRITABLE_COUNT_QOS_2                  (0x62)
#define OPTQB_WRITABLE_COUNT_QOS_3                  (0x63)

#define OPTQL_WRIABLE_COUNT_QOS_7_4               (0x64>>2)  // OPT_ WRIABLE_COUNT_39_36 (Offset: 067h~064h)
#define OPTQB_WRITABLE_COUNT_QOS_4                 (0x64)
#define OPTQB_WRITABLE_COUNT_QOS_5                 (0x65)
#define OPTQB_WRITABLE_COUNT_QOS_6                (0x66)
#define OPTQB_WRITABLE_COUNT_QOS_7                  (0x67)

#define OPTQL_WRIABLE_COUNT_QOS_11_8               (0x68>>2)  // OPT_ WRIABLE_COUNT_43_40 (Offset: 06Bh~068h)
#define OPTQB_WRITABLE_COUNT_QOS_8                  (0x68)
#define OPTQB_WRITABLE_COUNT_QOS_9                 (0x69)
#define OPTQB_WRITABLE_COUNT_QOS_10                  (0x6A)
#define OPTQB_WRITABLE_COUNT_QOS_11                 (0x6B)

#define OPTQL_WRIABLE_COUNT_QOS_15_12               (0x6C>>2)  // OPT_ WRIABLE_COUNT_47_44 (Offset: 06Fh~06Ch)
#define OPTQB_WRITABLE_COUNT_QOS_12                  (0x6C)
#define OPTQB_WRITABLE_COUNT_QOS_13                  (0x6D)
#define OPTQB_WRITABLE_COUNT_QOS_14                  (0x6E)
#define OPTQB_WRITABLE_COUNT_QOS_15                  (0x6F)

#define OPTQL_WRIABLE_COUNT_QOS_19_16             (0x70>>2)  // OPT_ WRIABLE_COUNT_51_48 (Offset: 073h~070h)
#define OPTQB_WRITABLE_COUNT_QOS_16                 (0x70)
#define OPTQB_WRITABLE_COUNT_QOS_17                  (0x71)
#define OPTQB_WRITABLE_COUNT_QOS_18                  (0x72)
#define OPTQB_WRITABLE_COUNT_QOS_19                  (0x73)

#define OPTQL_WRIABLE_COUNT_QOS_23_20              (0x74>>2)  // OPT_ WRIABLE_COUNT_55_52 (Offset: 077h~074h)
#define OPTQB_WRITABLE_COUNT_QOS_20                  (0x74)
#define OPTQB_WRITABLE_COUNT_QOS_21                  (0x75)
#define OPTQB_WRITABLE_COUNT_QOS_22                  (0x76)
#define OPTQB_WRITABLE_COUNT_QOS_23                  (0x77)

#define OPTQL_WRIABLE_COUNT_QOS_27_24               (0x78>>2)  // OPT_ WRIABLE_COUNT_59_56 (Offset: 07Bh~078h)
#define OPTQB_WRITABLE_COUNT_QOS_24                  (0x78)
#define OPTQB_WRITABLE_COUNT_QOS_25                 (0x79)
#define OPTQB_WRITABLE_COUNT_QOS_26                 (0x7A)
#define OPTQB_WRITABLE_COUNT_QOS_27                  (0x7B)

#define OPTQL_WRIABLE_COUNT_QOS_31_28               (0x7C>>2)  // OPT_ WRIABLE_COUNT_63_60 (Offset: 07Fh~07Ch)
#define OPTQB_WRITABLE_COUNT_QOS_28                  (0x7C)
#define OPTQB_WRITABLE_COUNT_QOS_29                  (0x7D)
#define OPTQB_WRITABLE_COUNT_QOS_30                  (0x7E)
#define OPTQB_WRITABLE_COUNT_QOS_31                  (0x7F)

#define R32_OPT_LOOKUP_PTR_7_0                    (0x80 >> 2)
#define R32_OPT_LOOKUP_PTR_15_8                   (0x84 >> 2)
#define R32_OPT_LOOKUP_PTR_23_16                  (0x88 >> 2)
#define R32_OPT_LOOKUP_PTR_31_24                  (0x8C >> 2)

#define R32_OPT_SWITCHQ_VALID                      (0xA0>>2)
#define R32_OPT_SWITCHQ_VALID_QOS               (0xA8>>2)

#define R32_OPT_TIMEOUT_0                            (0xB0>>2)
#define R32_OPT_TIMEOUT_QOS_0                       (0xB8>>2)

#define OPTQB_PROC_Q_FORCE_INVALID              (0xCC)
#define PROC_Q_NORMAL_FOECE_INVALID     (BIT0)
#define PROC_Q_QOS_FOECE_INVALID        (BIT1)

#define SET_PROC_Q_NORMAL_FOECE_INVALID {OPTQB[OPTQB_PROC_Q_FORCE_INVALID] = (PROC_Q_NORMAL_FOECE_INVALID);}
#define SET_PROC_Q_QOS_FOECE_INVALID        {OPTQB[OPTQB_PROC_Q_FORCE_INVALID] = (PROC_Q_QOS_FOECE_INVALID);}

#define OPTQL_FORCE_INVALID_0                   (0xD0>>2)
#define OPTQL_FORCE_INVALID_1                   (0xD4>>2)

#define OPTQL_FORCE_INVALID_QOS_0               (0xD8>>2)
#define OPTQL_FORCE_INVALID_QOS_1               (0xDC>>2)

#define R32_OPT_PROC_Q_HEAD_CONVPAGE                (0xF0 >> 2)
#define HEAD_RS_PAGE_NUMBER_W                   (OPTQL[R32_OPT_PROC_Q_HEAD_CONVPAGE] & (BITMSK(8, 0)))      // bit 7:0
#define HEAD_RS_TAG_W                               ((OPTQL[R32_OPT_PROC_Q_HEAD_CONVPAGE] & (BITMSK(9, 8))) >> 8)   // bit 16:8 
#define HEAD_RS_LAST_PAGE_W                     ((OPTQL[R32_OPT_PROC_Q_HEAD_CONVPAGE] & (BITMSK(1, 17))) >> 17) // bit 17
#define HEAD_RS_OTFENC_W                            ((OPTQL[R32_OPT_PROC_Q_HEAD_CONVPAGE] & (BITMSK(1, 18))) >> 18) // bit 18
#define HEAD_RS_PROG_PARITY_W                       ((OPTQL[R32_OPT_PROC_Q_HEAD_CONVPAGE] & (BITMSK(1, 20))) >> 20) // bit 20

#define R32_OPT_PROC_Q_LOOKUP_CONVPAGE          (0xF4 >> 2)
#define LOOKUP_RS_PAGE_NUMBER_W                 (OPTQL[R32_OPT_PROC_Q_LOOKUP_CONVPAGE] & (BITMSK(8, 0)))            // bit 7:0
#define LOOKUP_RS_TAG_W                         ((OPTQL[R32_OPT_PROC_Q_LOOKUP_CONVPAGE] & (BITMSK(9, 8))) >> 8) // bit 16:8
#define LOOKUP_RS_LAST_PAGE_W                       ((OPTQL[R32_OPT_PROC_Q_LOOKUP_CONVPAGE] & (BITMSK(1, 17))) >> 17)   // bit 17
#define LOOKUP_RS_OTFENC_W                      ((OPTQL[R32_OPT_PROC_Q_LOOKUP_CONVPAGE] & (BITMSK(1, 18))) >> 18)   // bit 18
#define LOOKUP_RS_PROG_PARITY_W                 ((OPTQL[R32_OPT_PROC_Q_LOOKUP_CONVPAGE] & (BITMSK(1, 20))) >> 20)   // bit 20

//#define OPTQB_REG_1                             (0x104)
//#define OPTQB_TRIGER_Q_EMPTY                    (0x107)

//////////////////////////////////////////////////////////////// //0x100 ////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////// //0x100 ////////////////////////////////////////////////////////////////
//#define //3.3.23  OPT_REG_0 (Offset: 103h~100h)

/*
 * OPT MT registers
 */
#define R32_OPT_REG_0                   (0x100 >> 2)
#define OPTMTW_FORMMT                           (0x100 >> 1)
#define OPTMTW_FORMMT_HW_AUTOGEN_FRAME          (BIT12)
#define OPTMTW_FORMMT_NO_HW_AUTOGEN_FRAME       (BIT12 & 0x0000)
#define OPTMTW_FORMMT_MARK_OPT_CMD          (BIT11)
#define OPTMTW_FORMMT_NOT_MARK              (BIT11 & 0x0000)
#define OPTMTW_FORMMT_MULTIPLE_PCA_FROM_1ST_VALID_GMP           (BIT10)
#define OPTMTW_FORMMT_MULTIPLE_PCA_FROM_HEAD_LOOKUP     (BIT10 & 0x0000)
#define OPTMTW_FORMMT_FORM_PLANE4_7         (BIT9)
#define OPTMTW_FORMMT_FORM_PLANE0_3         (BIT9 & 0x0000)
#define OPTMTW_FORMMT_MULTIPLE_PCA_USE_GMP_LOOKUP_PTR   (BIT8)
#define OPTMTW_FORMMT_MULTIPLE_PCA_USE_BIT2_BIT3        (BIT8 & 0x0000)
#define OPTMTW_FORMMT_PAGE_VLD_SEL_1            (BIT7)
#define OPTMTW_FORMMT_PAGE_VLD_SEL_0            (BIT7 & 0x00)
#define OPTMTW_FORMMT_PCA_MASK_BYPASS           (BIT6)
#define OPTMTW_FORMMT_PCA_MASK                  (BIT6 & 0x00)
#define OPTMTW_FORMMT_MT_COPY                   (BIT5)
#define OPTMTW_FORMMT_MT_NEW                    (BIT5 & 0x00)
#define OPTMTW_FORMMT_DMA_MT_TRIG               (BIT4)           // It will update to trigger data bit[59]
#define OPTMTW_FORMMT_CMD_MT_TRIG               (BIT4 & 0x00)
#define OPTMTW_FORMMT_FSA0_FROM_LOOKUP          (BIT3)
#define OPTMTW_FORMMT_FSA0_FROM_HEAD            (BIT3 & 0x00)
#define OPTMTW_FORMMT_FORM_ALL_PLANE_FSA        (BIT2)
#define OPTMTW_FORMMT_COPY_ONE_PLANE_FSA        (BIT2 & 0x00)
#define OPTMTW_FORMMT_USR_SEL_TMP               (BIT1)
#define OPTMTW_FORMMT_ATTR_SEL_TMP              (BIT1 & 0x00)
#define OPTMTW_FORMMT_FROM_LOOKUP               (BIT0)
#define OPTMTW_FORMMT_FROM_HEAD                 (BIT0 & 0x00)
#define OPTMTB_TRIGGER                          (0x102)
#define OPTMTB_FORMMT_WAIT                 (0x103)

#define R32_OPT_REG_1                            (0x104 >> 2)
#define OPTMTB_CUR_MT_PTR                           (0x104)
#define OPTMTB_FORMMT_TEMPLATE_USR_SEL          (0x105)  //- User select CREG_ATTR0~15 template for FormMT, use by writing FormMTbit[1] as 1.
#define OPTQB_TRIG_Q_EMPTY                           (0x107)
#define TRIGGER_Q_QOS_EMPTY                 (BIT25)
#define TRIGGER_Q_EMPTY                         (BIT24)
#define TIMER_EN                                (BIT16)

#define R32_OPT_COP0_ATTR                         (0x108 >> 2)

#define R32_OPT_RSC_RELEASE                       (0x10C >> 2)
#define RLS_QOS                                 (BIT17)
#define RLS_REQ                                 (BIT16)

#define R32_OPT_IN_ORDER_READ_EXE_GRP_REF                  (0x120 >> 2)
#define CHK_EXE_GRP_REF_OFFSET                  (0)
#define CHK_EXE_GRP_REF_MASK                    (0x0F << CHK_EXE_GRP_REF_OFFSET)

#define R32_OPT_IN_ORDER_READ_EXE_CNT_GRP_3_0         (0x124 >> 2)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_0                  (0x124)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_1                  (0x125)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_2                  (0x126)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_3                  (0x127)
#define R32_OPT_IN_ORDER_READ_EXE_CNT_GRP_7_4         (0x128 >> 2)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_4                  (0x128)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_5                  (0x129)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_6                  (0x12A)
#define R8_IN_ORDER_READ_EXE_CNT_GRP_7                  (0x12B)

#define R32_OPT_FORMMT_STS  (0x12C >> 2)
#define FORMMT_GMP_BSY      CHK_BIT8
#define FORMMT_MARK_BSY     CHK_BIT0

#define R32_OPT_MTQ_FULL                          (0x130 >> 2) //[31:0]: OPT_MTQ_FULL_0 -  MTQ's trigger full status of all 31-0 queues. (Trigger count>MAX-2).
#define R32_OPT_MTQ_FULL_1                        (0x134 >> 2) //[31:0]: OPT_MTQ_FULL_1 -  MTQ's trigger full status of all 63-32 queues. (Trigger count>MAX-2).    (CY21), removed reg

#define R32_OPT_MTQ_FULL_QOS                      (0x138 >> 2) //[31:0]: OPT_MTQ_FULL_QOS_0 -  MTQ's trigger full status of all 31-0 queues. (Trigger count>MAX-2).
#define R32_OPT_MTQ_FULL_QOS_1                    (0x13C >> 2) //[31:0]: OPT_MTQ_FULL_QOS_1 -  MTQ's trigger full status of all 63-32 queues. (Trigger count>MAX-2). (CY21), removed reg

/*
 * STALL Function
 */
#define OPTMTL_Q_STALL_INFO             (0x140 >> 2)
#define OPTMTL_Q_STALL_INFO_1           (0x144 >> 2)    // (CY21), removed reg
#define OPTMTL_Q_STALL_QOS_INFO         (0x148 >> 2)
#define OPTMTL_Q_STALL_QOS_INFO_1       (0x14C >> 2)    // (CY21), removed reg

#define OPTMTL_Q_STALL_RDY_Q            (0x150 >> 2)
#define OPTMTL_Q_STALL_RDY_Q_1          (0x154 >> 2)    // (CY21), removed reg
#define OPTMTL_Q_STALL_QOS_RDY_Q        (0x158 >> 2)
#define OPTMTL_Q_STALL_QOS_RDY_Q_1      (0x15C >> 2)    // (CY21), removed reg

#define R32_OPT_COP0_CLKSW_ATTR_0                (0x170 >> 2)
#define R32_OPT_COP0_CLKSW_ATTR_1                (0x174 >> 2)

#define R32_OPT_COP0_MT_OPTION                   (0x178 >> 2) //3.3.433.3.52   OPT_CREG_MT_OPTION
#define RO_NO_QO_QOS_1_MUX                      (BIT24)
#define SET_RO_NOR0_QOS1_MUX                    SET_BIT24
#define CLR_RO_NOR0_QOS1_MUX                    CLR_BIT24
#define CE_SEL_MODE_BIT                                 (BIT1)   /* Bit[1]: ce_select_mode */
#define PFA_INT_EN_BIT                                  (BIT0)   /* Bit[0]: pfa_int_en */

#define R32_OPT_COP0_USERDEFINE                  (0x17C >> 2) //3.3.443.3.53  OPT_CREG_USERDEFINE

#define R16_OPT_ERR_MT_BASE             (0x180 >> 1)
#define R16_OPT_ERR_TRIGGERDATA_BASE        (0x182 >> 1)

#define R32_OPT_PROC_Q_STALL                     (0x184 >> 2)    //3.3.55    OPT_PROC_Q_STALL
#define OPTMTB_PROC_Q_STALL                       (0x184)
#define OPTMTB_PROC_Q_STALL_RDY               (0x185)
#define SET_PROC_Q_STALL_RDY                    SET_BIT8
#define CLR_PROC_Q_STALL_RDY                    CLR_BIT8
#define CHK_PROC_Q_STALL_RDY                    CHK_BIT8
#define PROC_Q_CREG_STALL_BIT                   (BIT0)

#define R16_OPT_MT_BASE                  (0x188 >> 1)
#define CHK_MT_POOL_NOR_MTP_BASE_OFFSET         (0)
#define CHK_MT_POOL_NOR_MTP_BASE_MASK           (0xFFFF << CHK_MT_POOL_NOR_MTP_BASE_OFFSET)

#define R16_OPT_TRIGGERDATA_BASE            (0x18A >> 1)
#define CHK_MT_POOL_NOR_MTD_BASE_OFFSET         (0)
#define CHK_MT_POOL_NOR_MTD_BASE_MASK           (0xFFFF << CHK_MT_POOL_NOR_MTD_BASE_OFFSET)

#define R16_OPT_QOS_MT_BASE              (0x18C >> 1)
#define CHK_MT_POOL_QOS_MTP_BASE_OFFSET         (0)
#define CHK_MT_POOL_QOS_MTP_BASE_MASK           (0xFFFF << CHK_MT_POOL_QOS_MTP_BASE_OFFSET)

#define R16_OPT_QOS_TRIGGERDATA_BASE       (0x18E >> 1)
#define CHK_MT_POOL_QOS_MTD_BASE_OFFSET         (0)
#define CHK_MT_POOL_QOS_MTD_BASE_MASK           (0xFFFF << CHK_MT_POOL_QOS_MTD_BASE_OFFSET)

/*
 * OPT AXIM Write Read data
 */
#define OPTQ_AXIM_LL8B_WR                            (0x190 >> 3)
#define OPTQ_AXIM_L4B_WR                             (0x190 >> 2)
#define OPTQ_AXIM_H4B_WR                             (0x194 >> 2)

#define OPTQ_AXIM_LL8B_RD                            (0x198 >> 3)
#define OPTQ_AXIM_L4B_RD                             (0x198 >> 2)
#define OPTQ_AXIM_H4B_RD                             (0x19C >> 2)
#define OPTQ_AXIM_ADDR                               (0x1A0 >> 2)
#define OPTQ_AXIM_CTRL                               (0x1A4 >> 2)
#define OPTB_AXIM_WSTRB                              (0x1A4)
#define     OPT_AXIM_BY_TRIG                        BIT16
#define     OPT_AXIM_BY_DIR                         BIT8
#define OPTB_AXIM_DIR                               (0x1A5)
#define SET_DIR_WRITE                           SET_BIT0
#define CLR_DIR_READ                            CLR_BIT0
#define OPTB_AXIM_TRIG                          (0x1A6)
#define SET_AXIM_TRIG                           SET_BIT0
#define CHK_AXIM_BUSY                           CHK_BIT0
#define OPTB_AXIM_ADDR_H                        (0x1A7)

/*
 *  OPT CLEAR CTRL
 */
#if 1

#define OPTB_CLEAR_WAIT          (0x1A8)
#define OPTB_CLEAR_SIZE          (0x1AA)
#define OPTW_CLEAR_ADDR          (0x1AC >> 1)

#define OPT_CLEAR_DCCM_SETUP_FOR_JOB(_8B_cnt)       {OPTQB[OPTB_CLEAR_SIZE] = _8B_cnt;}
#if PS5021_EN
#define OPT_CLEAR_DCCM_FOR_JOB(addr)                do {\
        memset((U32 *)addr, 0, sizeof(OPT_JOB_STRUCT));\
    } while (0)
#else
#define OPT_CLEAR_DCCM_FOR_JOB(addr)                do { OPTQW[OPTW_CLEAR_ADDR] = addr;    \
        while (OPTQB[OPTB_CLEAR_WAIT]);   \
    } while (0)
#endif
#define OPT_CLEAR_DCCM(addr, _8B_cnt)           do {                                                \
                                                    OPTQB[OPTB_CLEAR_SIZE] = _8B_cnt;     \
                                                    OPTQW[OPTW_CLEAR_ADDR] = addr;        \
                                                    while (OPTQB[OPTB_CLEAR_WAIT]);       \
    } while (0)
#else

#define OPT_CLEAR_CTRL                              (0xA8 >> 2)
#define CLEAR_WAIT                              (BIT0)
#define OPTB_CLEAR_SIZE                         (0xAA)   //[23:16]: CLEAR_SIZE - Size of structure to clear, unit: 8-byte.

#define OPT_CLEAR_ADDR                              (0xAC >> 2)

#define OPT_CLEAR_DCCM_FOR_JOB(addr, _8B_cnt)               do { OPTQL [OPT_CLEAR_CTRL] = ((_8B_cnt & BITMSK(8, 0)) << 16); \
        OPTQL [OPT_CLEAR_ADDR] = (addr & BITMSK(10, 3)); \
        OPTQL [OPT_CLEAR_CTRL] |= BIT0; \
        while (OPTQL [OPT_CLEAR_CTRL] & (BIT0)); \
    } while(0)
#endif

#define R32_OPT_MTQ_TRIG_CNT_3_0                (0x340 >> 2)
#define R8_OPT_MTQ_TRIG_CNT_0                   	(0x340)
#define 	M_GET_MTQ_TRIG_CNT(queue)		(OPTQB[R8_OPT_MTQ_TRIG_CNT_0 + queue])

/*
 * USER_DEF Field Definition
 */

/* For dummy cmd */
#define OPT_CHK_HEAD_DUMMY_CMD                   (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_DUMMY_CMD)

/* For get temperature cmd */
#define OPT_CHK_HEAD_GET_TEMPERATURE_CMD 	(OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_GET_TEMPERATURE_CMD)

/* For Sync MICRON Open Unit*/
#define OPT_CHECK_HEAD_SYNC_OPEN_UNIT_CMD (OPTQ_PROC_Q_HEAD_USRDEF_INFO & (COP0_USERDEFINE_MICRON_SYNC_OPEN_UNIT | COP0_USERDEFINE_MICRON_SYNC_OPEN_GCGRUNIT | COP0_USERDEFINE_MICRON_SYNC_OPEN_SPOR_OLDGRUNIT))

#define OPT_CHECK_LOOKUP_DECODER_VALUE ((OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_DECODER_MASK)>>COP0_USERDEFINE_DECODER_OFFSET)

#if (MicronFlashID4 == IM_N28A_ID4)
/* For Micron Valley Check Use in Vender CMD */
#define OPT_CHECK_HEAD_VALLEY_CHECK_CMD (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_MICRON_VALLEY_CHECK)
/* For Micron Valley Check Use in Vender CMD */
#define OPT_CHECK_LOOKUP_VALLEY_CHECK_CMD (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_MICRON_VALLEY_CHECK)
/* For Micron Disable pre-read */
#define OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD (OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICRON_DISABLE_PREREAD)
/* For Micron Enable pre-read */
#define OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD (OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICRON_ENABLE_PREREAD)
/* For Micron B47R Bin Select*/
#define OPT_GET_LOOKUP_BIN_VALUE (FALSE)
#define M_OPT_GET_LOOKUP_BIN_VALUE(USERDEFINE) (FALSE)
/* For Micron B47R Disable IWL*/
#define OPT_GET_LOOKUP_DISABLE_IWL (FALSE)
/* For Micron Dummy Read */
#define OPT_CHECK_LOOKUP_NEED_DUMMY_READ	(OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICTON_DUMMY_READ)
/* For QLC 2nd Pass*/
#define OPT_GET_LOOKUP_IS_QLC_CELL_1ST_PASS  (FALSE)
#define OPT_CHECK_LOOKUP_DECODER_VALUE (FALSE)
#elif (MicronFlashID4 == IM_140S_ID4)

#if ((PS5021_EN) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
/* For Micron Valley Check Use in Vender CMD */
#define OPT_CHECK_HEAD_VALLEY_CHECK_CMD (FALSE)
/* For Micron Valley Check Use in Vender CMD */
#define OPT_CHECK_LOOKUP_VALLEY_CHECK_CMD (FALSE)
/* For Micron B47R Disable IWL*/
#define OPT_GET_LOOKUP_DISABLE_IWL  (FALSE)
/* For Micron Disable pre-read */
#define OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD (FALSE)
/* For Micron Enable pre-read */
#define OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD (FALSE)
#else /* ((PS5021_EN) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)) */
/* For Micron Valley Check Use in Vender CMD */
#define OPT_CHECK_HEAD_VALLEY_CHECK_CMD (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_MICRON_VALLEY_CHECK)
/* For Micron Valley Check Use in Vender CMD */
#define OPT_CHECK_LOOKUP_VALLEY_CHECK_CMD (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_MICRON_VALLEY_CHECK)
/* For Micron B47R Disable IWL*/
#define OPT_GET_LOOKUP_DISABLE_IWL  (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_MICRON_DISABLE_IWL_READ)
/* For Micron Disable pre-read */
#define OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD (OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICRON_DISABLE_PREREAD)
/* For Micron Enable pre-read */
#define OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD (OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICRON_ENABLE_PREREAD)
#endif /* ((PS5021_EN) && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)) */
/* For QLC 2nd Pass*/
#define OPT_GET_LOOKUP_IS_QLC_CELL_1ST_PASS	(OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_IS_QLC_CELL_1ST_PASS)? (TRUE):(FALSE)
/* For Micron B47R Bin Select*/
#define OPT_GET_LOOKUP_BIN_VALUE  (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_MICRON_SPECIALMODE_CHECK) ? (0) :((OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_READ_BIN_MASK) >> COP0_USERDEFINE_DECODER_OFFSET)
#define M_OPT_GET_LOOKUP_BIN_VALUE(USERDEFINE) (((USERDEFINE) & COP0_USERDEFINE_MICRON_SPECIALMODE_CHECK) ? (0) :(((USERDEFINE) & COP0_USERDEFINE_READ_BIN_MASK)>>COP0_USERDEFINE_DECODER_OFFSET))
/* For Micron Dummy Read */
#define OPT_CHECK_LOOKUP_NEED_DUMMY_READ	(OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICTON_DUMMY_READ)
/* For LDPC no correct (MT Erase No Stop Request)*/
#define OPT_CHECK_LOOKUP_DISABLE_LDPC_COR	(OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICRON_DISABLE_LDPC_COR)
/* For Micron NDEP Read */
#if (NDEP_READ_EN)
#define M_OPT_CHECK_LOOKUP_NDEP_READ(USERDEFINE)	((((USERDEFINE) & COP0_USERDEFINE_DECODER_MASK)>>COP0_USERDEFINE_DECODER_OFFSET) == COP0_USERDEFINE_MICRON_NDEP_READ)
#define NDEP_FPU_OFFSET(n)		((0 == n)? (FPU_OFFSET(gFpuEntryList.fpu_entry_slc_ndep_30_read_mod_DAC)):(FPU_OFFSET(gFpuEntryList.fpu_entry_slc_ndep_30_read_clear)))
#else /* (NDEP_READ_EN) */
#define M_OPT_CHECK_LOOKUP_NDEP_READ(USERDEFINE)	(FALSE)
#define NDEP_FPU_OFFSET(n)							(FALSE)
#endif /* (NDEP_READ_EN) */

#if (READ_DISTURB_PRDH_EN)
/* For Read Disturb PRDH read cnt handle */
#define M_OPT_GET_LOOKUP_PRDH_BLK_TYPE(USERDEFINE)	(((USERDEFINE) & COP0_USERDEFINE_PRDH_BLK_TYPE_MASK)>>COP0_USERDEFINE_PRDH_BLK_TYPE_OFFSET)
#define M_OPT_CHECK_LOOKUP_PRDH_BYPASS_READ_CNT_HANDLE(USERDEFINE) (((USERDEFINE) & COP0_USERDEFINE_PRDH_BYPASS_READ_CNT_HANDLE)>>COP0_USERDEFINE_PRDH_BYPASS_READ_CNT_HANDLE_OFFSET)
#endif /* (READ_DISTURB_PRDH_EN) */

#else /* (MicronFlashID4 == IM_N28A_ID4) */
#define OPT_CHECK_HEAD_VALLEY_CHECK_CMD (FALSE)
#define OPT_CHECK_LOOKUP_VALLEY_CHECK_CMD (FALSE)
/* For Micron Disable pre-read */
#define OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD (FALSE)
/* For Micron Enable pre-read */
#define OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD (FALSE)
/* For Micron B47R Bin Select*/
#define OPT_GET_LOOKUP_BIN_VALUE (FALSE)
#define M_OPT_GET_LOOKUP_BIN_VALUE(USERDEFINE) (FALSE)
/* For Micron B47R Disable IWL*/
#define OPT_GET_LOOKUP_DISABLE_IWL (FALSE)
/* For Micron Dummy Read */
#define OPT_CHECK_LOOKUP_NEED_DUMMY_READ	(OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_MICTON_DUMMY_READ)
#endif /* (MicronFlashID4 == IM_N28A_ID4) */

/* For QLC 2nd Pass*/	//Reip Porting 3D-V7 QLC Add
#define OPT_GET_LOOKUP_IS_QLC_CELL_1ST_PASS	(OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_IS_QLC_CELL_1ST_PASS)? (TRUE):(FALSE)



/* For prog RAND by P4K_BackUp flow */
#define OPT_CHECK_LOOKUP_PROGRAM_RANDOM_USERDEFINE		(OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_PROGRAM_RANDOM)

#define OPT_CHECK_LOOKUP_PROGRAM_TABLE_USERDEFINE        (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_PROGRAM_TABLE)
#define OPT_CHK_LOOKUP_PROGRAM_PARITY_USERDEFINE                (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_PROGRAM_PARITY)
#define OPT_CHK_HEAD_PROGRAM_PARITY_USERDEFINE                (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_PROGRAM_PARITY)
#define OPT_CHK_LOOKUP_END_PROGRAM_USERDEFINE                   (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_END_PROGRAM)

/* For one plane read */
#define OPT_CHK_FORCE_ONE_PLANE_READ_USERDEFINE       ((OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_FORCE_ONE_PLANE_READ_USERDEFINE))

/* For one plane rd op with fast read 0x36 */
#define OPT_CHECK_TOSHIBA_FAST_READ_REQUEST_USERDEFINE		(OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_TOSHIBA_FAST_READ_REQUEST)

/* For table read no wait */
#define OPT_CHECK_TABLE_READ_NO_WAIT								(OPT_CHECK_LOOKUP_DECODER_VALUE == COP0_USERDEFINE_TABLE_READ_NO_WAIT)

/* For disable cache read / write */
#define OPT_CHK_DIS_CACHE_USERDEFINE                  (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_DIS_CACHE)

#define OPT_CHK_LOOKUP_GEN_FAIL          (OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_GEN_FAIL)
#define OPT_CHK_HEAD_GEN_FAIL          (OPTQ_PROC_Q_HEAD_USRDEF_INFO & COP0_USERDEFINE_GEN_FAIL)

/* For Bypass Program DMA Load Parity */
#define OPT_CHECK_BYPASS_WDMA_USERDEFINE	(OPTQ_PROC_Q_LOOKUP_USRDEF_INFO & COP0_USERDEFINE_BYPASS_WDMA)

typedef union copy_back_user_def_struct   COPY_BACK_USER_DEF_STRUCT, *COPY_BACK_USER_DEF_STRUCT_PTR;

union copy_back_user_def_struct {
	U32 all;
	struct {
		U32  sub_cmd    : 8;
		U32  length     : 4;
		U32  plane_vld  : 4;
		U32  dccm_ofs   : 16;
	} cpbk;
};

#define R32_OPT_PROC_Q_MTQ_TRIG_CNT                (0x1B0 >> 2)
#define PROC_Q_MTQ_TRIG_CNT_ADD              (0x1B0)

#define R32_OPT_MT_CNT                           (0x1B8 >> 2)
#define R16_MT_CNT                              (0x1B8 >> 1)
#define R32_OPT_MT_CNT_QOS                    (0x1BC >> 2)
#define R16_MT_CNT_QOS                           (0x1BC >> 1)

/*
 *  OPT CREG PCA RULE (all register read only)
 */

#define R8_OPT_COP0_PCA_RULE0                       (0x1C0) //follow spec. naming, actually means R8_OPT_COP0_PCA_RULE0_0
#define R8_OPT_COP0_PCA_RULE1_0                     (0x310)
#define R8_OPT_COP0_PCA_RULE2_0                     (0x320)
#define R8_OPT_COP0_PCA_RULE3_0                     (0x330)

#define R16_OPT_COP0_PCA_RULE1                      (0x1C4 >> 1) // follow spec. naming, actually means R8_OPT_COP0_PCA_RULE0_1
#define R16_OPT_COP0_PCA_RULE1_1                    (0x314 >> 1)
#define R16_OPT_COP0_PCA_RULE2_1                    (0x324 >> 1)
#define R16_OPT_COP0_PCA_RULE3_1                    (0x334 >> 1)

#define R8_OPT_COP0_PCA_RULE2                       (0x1C8) // follow spec. naming, actually means R8_OPT_COP0_PCA_RULE0_2
#define R8_OPT_COP0_PCA_RULE1_2                    (0x318)
#define R8_OPT_COP0_PCA_RULE2_2                    (0x328)
#define R8_OPT_COP0_PCA_RULE3_2                    (0x338)

#define R8_PCA_RULE_OFFSET_ADJUST(n)                (((n) > 0) * (0x140 >> 0) + (n) * (0x10 >> 0)) // rule 0 & 1 offset gap is 0x150, other 0x10
#define R16_PCA_RULE_OFFSET_ADJUST(n)               (((n) > 0) * (0x140 >> 1) + (n) * (0x10 >> 1))

#define COP0_PCA_RULE_NUM                   (4)
#define COP0_PCA_RULE_0                     (0) // SLC=0, D1=0
#define COP0_PCA_RULE_1                     (1) // SLC=0, D1=1
#define COP0_PCA_RULE_2                     (2) // SLC=1, D1=0
#define COP0_PCA_RULE_3                     (3) // SLC=1, D1=1

#define     COP0_BANK_RULE(n)                   ( OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x03])
#define         COP0_BANK_START_POINT(n)        ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x03] & (BITMSK(5, 0))))
#define         COP0_BANK_LENS(n)               ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x03] & (BITMSK(3, 5))) >> 5)

#define     COP0_LMU_RULE(n)                       ( OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x02])
#define         COP0_LMU_START_POINT(n)            ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x02] & (BITMSK(6, 0))))
#define         COP0_LMU_LENS(n)                   ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x02] & (BITMSK(2, 6))) >> 6)

#define     COP0_PLANE_RULE(n)                     ( OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x01])
#define         COP0_PLANE_START_POINT(n)          ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x01] & (BITMSK(6, 0))))
#define         COP0_PLANE_LENS(n)                 ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x01] & (BITMSK(2, 6))) >> 6)

#define     COP0_CHANNEL_RULE(n)                   ( OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x00])
#define         COP0_CHANNEL_START_POINT(n)        ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x00] & (BITMSK(5, 0))))
#define         COP0_CHANNEL_LENS(n)               ((OPTQB [R8_OPT_COP0_PCA_RULE0 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x00] & (BITMSK(3, 5))) >> 5)

#define     COP0_BLOCK_RULE(n)                     ( OPTQW [R16_OPT_COP0_PCA_RULE1 + R16_PCA_RULE_OFFSET_ADJUST(n) + 0x01])
#define         COP0_BLOCK_START_POINT(n)          ((OPTQW [R16_OPT_COP0_PCA_RULE1 + R16_PCA_RULE_OFFSET_ADJUST(n) + 0x01] & (BITMSK(6, 0))))
#define         COP0_BLOCK_LENS(n)                 ((OPTQW [R16_OPT_COP0_PCA_RULE1 + R16_PCA_RULE_OFFSET_ADJUST(n) + 0x01] & (BITMSK(4, 6))) >> 6)

#define     COP0_PAGE_RULE(n)                      ( OPTQW [R16_OPT_COP0_PCA_RULE1 + R16_PCA_RULE_OFFSET_ADJUST(n) + 0x00])
#define         COP0_PAGE_START_POINT(n)           ((OPTQW [R16_OPT_COP0_PCA_RULE1 + R16_PCA_RULE_OFFSET_ADJUST(n) + 0x00] & (BITMSK(6, 0))))
#define         COP0_PAGE_LENS(n)                  ((OPTQW [R16_OPT_COP0_PCA_RULE1 + R16_PCA_RULE_OFFSET_ADJUST(n) + 0x00] & (BITMSK(4, 6))) >> 6)

#define     COP0_DIE_IL_RULE(n)                      ( OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x03])
#define         COP0_DIE_IL_START_POINT(n)           ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x03] & (BITMSK(6, 0))))
#define         COP0_DIE_IL_LENS(n)                  ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x03] & (BITMSK(2, 6))) >> 6)
#define     COP0_TRIM_RULE(n)                      ( OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x02])
#define         COP0_TRIM_START_POINT(n)           ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x02] & (BITMSK(6, 0))))
#define         COP0_TRIM_LENS(n)                  ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x02] & (BITMSK(2, 6))) >> 6)

#define     COP0_EXDIE_RULE(n)                      ( OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x01])
#define         COP0_EXDIE_START_POINT(n)           ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x01] & (BITMSK(6, 0))))
#define         COP0_EXDIE_LENS(n)                  ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x01] & (BITMSK(2, 6))) >> 6)

#define    COP0_DIE_RULE(n)                       ( OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x00])
#define         COP0_DIE_START_POINT(n)            ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x00] & (BITMSK(6, 0))))
#define         COP0_DIE_LENS(n)                   ((OPTQB [R8_OPT_COP0_PCA_RULE2 + R8_PCA_RULE_OFFSET_ADJUST(n) + 0x00] & (BITMSK(2, 6))) >> 6)

/*
 *  PCA ADDR (all register read only)
 */
#define R32_OPT_COP0_PAGE_ADDR                        (0x1D0 >> 2)
#define R16_OPT_COP0_PAGE_ADDR                         (0x1D0 >> 1) //3.3.71 OPT_CREG_PAGE_ADDR (Offset: 1D3h~1D0h)
#define     OPT_LOOKUP_PAGE_ADDR                    (OPTQW [R16_OPT_COP0_PAGE_ADDR + 0x01]) // [31:16]: LOOKUP_PAGE_ADDR
#define     OPT_HEAD_PAGE_ADDR                      (OPTQW [R16_OPT_COP0_PAGE_ADDR + 0x00]) // [15:0]: HEAD_PAGE_ADDR

#define R16_OPT_COP0_BLOCK_ADDR                        (0x1D4 >> 1) //3.3.72    OPT_CREG_BLOCK_ADDR (Offset: 1D7h~1D4h)
#define     OPT_LOOKUP_BLOCK_ADDR                   (OPTQW [R16_OPT_COP0_BLOCK_ADDR + 0x01]) // [31:16]: LOOKUP_BLOCK_ADDR
#define     OPT_HEAD_BLOCK_ADDR                     (OPTQW [R16_OPT_COP0_BLOCK_ADDR + 0x00]) // [15:0]: HEAD_BLOCK_ADDR

#define R32_OPT_COP0_PLANE_ADDR                       (0x1D8 >> 2)
#define R8_OPT_COP0_PLANE_ADDR                        (0x1D8) //3.3.73 OPT_CREG_PLANE_ADDR (Offset: 1DBh~1D8h)
#define     OPT_LOOKUP_PLANE_BIT                    (OPTQB [R8_OPT_COP0_PLANE_ADDR + 0x03])   //[27:24]: LOOKUP_PLANE_BIT_MAP
#define     OPT_HEAD_PLANE_BIT                      (OPTQB [R8_OPT_COP0_PLANE_ADDR + 0x02])   //[19:16]: HEAD_PLANE_BIT_MAP
#define     OPT_LOOKUP_PLANE_ADDR                   (OPTQB [R8_OPT_COP0_PLANE_ADDR + 0x01])   //[11:8]:  LOOKUP_PLANE_ADDR
#define     OPT_HEAD_PLANE_ADDR                     (OPTQB [R8_OPT_COP0_PLANE_ADDR + 0x00])   //[3:0]:   HEAD_PLANE_ADDR

#define R8_OPT_COP0_LMU_DIE_ADDR                      (0x1DC) //3.3.74 OPT_CREG_LMU_DIE_ADDR (Offset: 1DFh~1DCh)
#define     OPT_LOOKUP_LMU_ADDR                     (OPTQB [R8_OPT_COP0_LMU_DIE_ADDR + 0x03])  //[27:24]: LOOKUP_LMU_ADDR
#define     OPT_HEAD_LMU_ADDR                       (OPTQB [R8_OPT_COP0_LMU_DIE_ADDR + 0x02])  //[19:16]: HEAD_LMU_ADDR
#define     OPT_LOOKUP_DIE_ADDR                     (OPTQB [R8_OPT_COP0_LMU_DIE_ADDR + 0x01])  //[11:8]:  LOOKUP_DIE_ADDR
#define     OPT_HEAD_DIE_ADDR                       (OPTQB [R8_OPT_COP0_LMU_DIE_ADDR + 0x00])  //[3:0]:   HEAD_DIE_ADDR

#define R32_OPT_COP0_REMAP_EN                        (0x1E0>>2)
#define R8_OPT_COP0_REMAP_EN                          (0x1E0) //4.3.80 OPT_CREG_REMAP_EN (Offset: 1E0h)
#define     COP0_VBRMP_EN                               (BIT1)
#define     COP0_CR_BBMP_EN                             (BIT0)

#define R8_OPT_ENTRY_MASK_EN                    (0x1E1)
#define     ENTRY_MASK_EN                           (BIT0)
#define     ENTRY_MASK_DIS                          (CLR_BIT0)

#define R8_OPT_COP0_DIE_IL_ADDR                      (0x1E2)
#define     OPT_LOOKUP_DIE_IL_ADDR                     (OPTQB [R8_OPT_COP0_DIE_IL_ADDR + 0x01])  //[11:8]:  LOOKUP_DIE_IL_ADDR
#define     OPT_HEAD_DIE_IL_ADDR                       (OPTQB [R8_OPT_COP0_DIE_IL_ADDR + 0x00])  //[3:0]:   HEAD_DIE_IL_ADDR

#define R32_OPT_TIMER                            (0x1E4 >> 2)

#define R32_OPT_MTQ_TRIG_CNT_ADD_0                       (0x1F0 >> 2)
#define R32_OPT_MTQ_TRIG_CNT_ADD_1                     (0x1F4 >> 2)
#define R32_OPT_MTQ_TRIG_CNT_ADD_QOS_0                   (0x1F8 >> 2)
#define R32_OPT_MTQ_TRIG_CNT_ADD_QOS_1                 (0x1FC >> 2)

/*
 *  Plane Search Reg (all register read only)
 */
#define R64_OPT_PS_RESULT_IDX_0              (0x200 >> 3)
#define R32_OPT_PS_RESULT_IDX_0_P03              (0x200 >> 2)
#define R32_OPT_PS_RESULT_IDX_0_P47              (0x204 >> 2)
#define R64_OPT_PS_RESULT_IDX_1              (0x208 >> 3)
#define R32_OPT_PS_RESULT_IDX_1_P03              (0x208 >> 2)
#define R32_OPT_PS_RESULT_IDX_1_P47              (0x20C >> 2)
#define R64_OPT_PS_RESULT_IDX_2              (0x210 >> 3)
#define R32_OPT_PS_RESULT_IDX_2_P03              (0x210 >> 2)
#define R32_OPT_PS_RESULT_IDX_2_P47              (0x214 >> 2)
#define R64_OPT_PS_RESULT_IDX_3              (0x218 >> 3)
#define R32_OPT_PS_RESULT_IDX_3_P03              (0x218 >> 2)
#define R32_OPT_PS_RESULT_IDX_3_P47              (0x21C >> 2)
#define R64_OPT_PS_RESULT_IDX_4              (0x220 >> 3)
#define R32_OPT_PS_RESULT_IDX_4_P03              (0x220 >> 2)
#define R32_OPT_PS_RESULT_IDX_4_P47              (0x224 >> 2)
#define R64_OPT_PS_RESULT_IDX_5              (0x228 >> 3)
#define R32_OPT_PS_RESULT_IDX_5_P03              (0x228 >> 2)
#define R32_OPT_PS_RESULT_IDX_5_P47              (0x22C >> 2)

#define R32_OPT_PS_RESULT_NW_0_3                    (0x260 >> 2)
#define R8_OPT_PS_RESULT_NW_0                      (0x260)
#define R8_OPT_PS_RESULT_NW_1                      (0x261)
#define R8_OPT_PS_RESULT_NW_2                      (0x262)
#define R8_OPT_PS_RESULT_NW_3                      (0x263)

#define R32_OPT_PS_RESULT_NW_4_7                    (0x264 >> 2)
#define R8_OPT_PS_RESULT_NW_4                      (0x264)
#define R8_OPT_PS_RESULT_NW_5                      (0x265)
#define R8_OPT_PS_RESULT_NW_6                      (0x266)
#define R8_OPT_PS_RESULT_NW_7                      (0x267)

#define R32_OPT_PS_RESULT_NW_8_11                   (0x268 >> 2)
#define R8_OPT_PS_RESULT_NW_8                      (0x268)
#define R8_OPT_PS_RESULT_NW_9                      (0x269)
#define R8_OPT_PS_RESULT_NW_10                     (0x26A)
#define R8_OPT_PS_RESULT_NW_11                     (0x26B)

#define R8_OPT_PS_HIT_NOT_READ                     (0x26C)
#define R8_OPT_PS_NOT_READ_PTR                     (0x26D)

#define R8_OPT_PS_NW_NONREAD_ROUND_IDX             (0x26E)

#define R8_OPT_PS_DMA_ROUND_IDX                    (0x26F)

#define R64_OPT_PS_RESULT_MLC_QUEUE         (0x270 >> 3)
#define R32_OPT_PS_RESULT_MLC_QUEUE                 (0x270 >> 2)
#define R8_OPT_PS_RESULT_MLC_QUEUE_0                 (0x270)
#define R8_OPT_PS_RESULT_MLC_QUEUE_1                 (0x271)
#define R8_OPT_PS_RESULT_MLC_QUEUE_2                 (0x272)
#define R8_OPT_PS_RESULT_MLC_QUEUE_3                 (0x273)
#define R32_OPT_PS_RESULT_SLC_QUEUE                 (0x274 >> 2)
#define R8_OPT_PS_RESULT_SLC_QUEUE_0                 (0x274)
#define R8_OPT_PS_RESULT_SLC_QUEUE_1                 (0x275)
#define R8_OPT_PS_RESULT_SLC_QUEUE_2                 (0x276)
#define R8_OPT_PS_RESULT_SLC_QUEUE_3                 (0x277)
#define PS_RESULT_SLC_QUEUE_OFFSET			  (4)

#define R8_OPT_PS_RESULT_MLC_QUEUE_CNT                 (0x278)
#define R8_OPT_PS_RESULT_SLC_QUEUE_CNT                 (0x279)

#define R8_OPT_PS_RESULT_VALID_CNT                 (0x27A)
#define R8_OPT_PS_READ_CMD_CNT                 (0x27B)

#define R8_OPT_PS_FILTER_EN                        (0x2C0)
#define PS_FILTER_BLOCK_BIT						BIT0
#define PS_FILTER_PAGE_BIT						BIT1
#define PS_FILTER_DIE_BIT						BIT2
#define PS_FILTER_USERDEFINE_BIT				BIT3
#define PS_FILTER_MARK_BIT						BIT4
#define PS_FILTER_SLC_BIT						BIT5
#define PS_FILTER_D1_BIT						BIT6
#define PS_FILTER_LMU_BIT						BIT7

#define R8_OPT_PS_SGL_MTL_DIE_MODE                 (0x2C1)
#define MULTI_DIE_MODE              (0)
#define SINGLE_DIE_MODE         (1)
#define DIRECT_PUSH_INTO_SLC_QUEUE_MODE (2)

#define R8_OPT_PS_READ_CMD                         (0x2C2)
#define R8_OPT_PS_INTERNAL_POP                     (0x2C4)
#define R8_OPT_PS_MULTI_POP                        (0x2C5)
#define R8_OPT_PS_SKIP_STALL_EN                   (0x2C6)
#define R16_OPT_PS_FILTER_UD_MASK		(0x2C8 >> 1)

#define R8_OPT_GMP_LOOKUP_PTR_3_0          (0x2CC)
#define R32_OPT_GMP_LOOKUP_PTR_3_0         (0x2CC >> 2)
#define R8_OPT_GMP_LOOKUP_PTR_7_4          (0x2D0)
#define R32_OPT_GMP_LOOKUP_PTR_7_4         (0x2D0 >> 2)

#define R8_OPT_GMP_RESULT_SEL          (0x2D4)
#define OPT_GMP_RESULT_EXTRACT           (BIT6)
#define OPT_GMP_RESULT_P47                            (BIT7)
#define OPT_GMP_RESULT_P03                            (0)

#define R8_OPT_GMP_RESULT_EXTRACT_ST   (0x2D5)

#define R32_OPT_MARK_CMD_PTR           (0x2D8 >> 2)
#define R16_OPT_MARK_CMD_PTR           (0x2D8 >> 1)
#define MARK_BSY					   (BIT24)

// ------------------------------------------------------------------------
// 0x0000A400, OPT MTP registers
// ------------------------------------------------------------------------
#define R32_OPT_MTP_LL_BASE                        (0x400 >> 2)
#define  MTP_LL_QOS_BASE_SHIFT              (16)
#define  MTP_LL_QOS_BASE_MASK                (0xFFFF << MTP_LL_QOS_BASE_SHIFT)
#define  MTP_LL_NOR_BASE_SHIFT              (0)
#define  MTP_LL_NOR_BASE_MASK                (0xFFFF << MTP_LL_NOR_BASE_SHIFT)
#define R16_OPT_MTP_LL_BASE		  	(0x400 >> 1)
#define R16_OPT_MTP_LL_QOS_BASE		  (0x402 >> 1)

#define R32_OPT_MTP_RSC_BASE                       (0x404 >> 2)
#define  MTP_RSC_QOS_BASE_SHIFT             (16)
#define  MTP_RSC_QOS_BASE_MASK               (0xFFFF << MTP_RSC_QOS_BASE_SHIFT)
#define  MTP_RSC_NOR_BASE_SHIFT            (0)
#define  MTP_RSC_NOR_BASE_MASK               (0xFFFF << MTP_RSC_NOR_BASE_SHIFT)
#define R16_OPT_MTP_RSC_NOR_BASE			(0x404 >> 1)
#define R16_OPT_MTP_RSC_QOS_BASE			(0x406 >> 1)

#define R32_OPT_MTP_NUM                            (0x408 >> 2)
#define R8_OPT_MTP_NOR_NUM                            (0x408)
#define R8_OPT_MTP_QOS_NUM                            (0x409)
#define R8_OPT_MTP_ERR_NUM                            (0x40A)

#define  MTP_ERR_NUM_SHIFT                  (16)
#define  MTP_ERR_NUM_MASK                    (0xFF << MTP_ERR_NUM_SHIFT)
#define  MTP_QOS_NUM_SHIFT                  (8)
#define  MTP_QOS_NUM_MASK                    (0xFF << MTP_QOS_NUM_SHIFT)
#define  MTP_NOR_NUM_SHIFT                  (0)
#define  MTP_NOR_NUM_MASK                    (0xFF << MTP_NOR_NUM_SHIFT)

#define R8_OPT_MRSC_INIT                          (0x40C)
#define  SET_MRSC_INIT                           (SET_BIT0)
#define  CLR_MRSC_INIT                           (CLR_BIT0)

#define R8_OPT_MRSC_ALCT_RUN                      (0x40D)
#define  SET_MRSC_ALCT_QOS_RUN                   (SET_BIT1)
#define  CLR_MRSC_ALCT_QOS_RUN                   (CLR_BIT1)
#define  SET_MRSC_ALCT_NOR_RUN                   (SET_BIT0)
#define  CLR_MRSC_ALCT_NOR_RUN                   (CLR_BIT0)

#define R32_OPT_MTP_STALL       (0x420 >> 2)

#define R32_OPT_MTP_STALL_QOS       (0x424 >> 2)

#define R8_OPT_AVL_MTP_SRC  (0x42E)

/* for read, 所需max MT個數為((MT_1_MT_TRIG+ (gubMaxPlane * 2) +  WLSB_MT_CNT)) = A (cmd + split DMA(*2) + wordline status bypass)
// for program, 所需max MT個數為 (gubMaxPlane * FSP_PAGE_CNT) = B
// 4p sample時 A(10 MT cnt) < B (12 MT cnt)
// 2p sample時 A(6 MT cnt) == B (6 MT cnt)
// 採用判斷條件B
*/
// Disable Pre-read flow : Dis Preread_1 + 4 * Top + Dis Preread_2 + 4 * eXtra + 4 * Upper + 4 * Lower + Enable Preread = 19
#define RESERVED_MT_POLL_CNT_FOR_N28_VALLEY_CHECK	(19)
//#define RESERVED_MT_POOL_CNT	((MT_1_MT_TRIG + (gubMaxPlane * 2) +  WLSB_MT_CNT))
#define RESERVED_MT_POLL_CNT_FOR_READ	((MT_1_MT_TRIG + (gubMaxPlane * 2) +  MT_1_MT_TRIG))
#define RESERVED_MT_POLL_CNT_FOR_PROG	(NAND_MAX_PLANE * FSP_PAGE_CNT)
#if (MicronFlashID4 == IM_140S_ID4)
#define RESERVED_MT_POOL_CNT			(RESERVED_MT_POLL_CNT_FOR_PROG)
#elif (MicronFlashID4 == IM_N28A_ID4)
#define RESERVED_MT_POOL_CNT			(RESERVED_MT_POLL_CNT_FOR_N28_VALLEY_CHECK)
#else /* (MicronFlashID4 == IM_N28A_ID4) */
#define RESERVED_MT_POOL_CNT			(RESERVED_MT_POLL_CNT_FOR_READ)
#endif /* (MicronFlashID4 == IM_N28A_ID4) */

#define _chk_read_mtq_free_cnt(q, mt)           (OPTQB[R8_OPT_AVL_MTP_SRC]<(mt + RESERVED_MT_POOL_CNT))
#define _chk_write_mtq_free_cnt(q, mt)          (OPTQB[R8_OPT_AVL_MTP_SRC]<(mt + RESERVED_MT_POOL_CNT))
#define _chk_reserved_mtq_free_cnt(q, mt)		 (OPTQB[R8_OPT_AVL_MTP_SRC]<(mt))
#define _chk_other_cmd_mtq_free_cnt(q, mt)	(OPTQB[R8_OPT_AVL_MTP_SRC]<(mt + RESERVED_MT_POOL_CNT))


#define R32_OPT_MTP_HEAD_VALID             (0x430 >> 2)

#define R32_OPT_MTP_HEAD_VALID_QOS     (0x434 >> 2)

#define R32_OPT_COP0_MTP_STALL  (0x450 >> 2)

#define R32_OPT_COP0_MTP_STALL_QOS  (0x454 >> 2)

#define R16_OPT_MTP_RSC_ERR_BASE    (0x460 >> 1)

//////////////////////////////////////////////////////////////// //0x800 ////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////// //0x800 ////////////////////////////////////////////////////////////////

/********************* OPT CURRENT MT **********************/
#define OPTCMB_MT_BYTE0                         (0x00)          //[4]:busy
#define     OPTCMB_MT_FPU_EN                    (BIT0)
#define     OPTCMB_PFA_INT_EN                   (BIT1)
#define     OPTCMB_MT_BUSY                      (BIT4)
#define OPTCMB_MT_BYTE5                         (0x05)          //[4:3]:alu_sel, [5]: ultra dma
#define     OPTCMB_MT_ALU_SEL(X)                ((X&0x03) << 3)
#define     OPTCMB_MT_ULTRA_DMA_DIS             (BIT5)
#define     OPTCMB_MT_INT_VCT_EN                (BIT6)
#define OPTCM_MT_DW0							(0x0>>2)
#define		OPTCM_MT_ULTRA_WRITE_DISABLE		(CLR_BIT24)
#define		OPTCM_MT_PFA_INT_DISABLE			(CLR_BIT1)
#define OPTCM_MT_DW1							(0x4>>2)
#define		OPTCM_MT_ULTRA_DMA_DISABLE			(SET_BIT13)
#define OPTCM_MT_DW5							(0x14>>2)
#define		OPTCM_POL_SEQ_SEL_SHIFT (27)
#define		OPTCM_POL_SEQ_SEL_MASK	(BIT_MASK(4))
#define		OPTCM_POL_SEQ_SEL_B4_B5_MASK	(BIT_MASK(2))
#define		M_SET_OPTCM_POLLING_SEQUENCE_SELECT(VALUE) do{\
	mtq->dw5.bits.pol_seq_sel = ((VALUE) & OPTCM_POL_SEQ_SEL_MASK);\
	mtd->dw0_dat.bits.pol_seq_sel_b4_b5 = (((VALUE) >> 4) & OPTCM_POL_SEQ_SEL_B4_B5_MASK);\
}while(0)
#define		M_SET_OPTCM_FPU_POL_SEQ(FPU, VALUE) do{\
    mtq->dw5.all |= FPU;\
    M_SET_OPTCM_POLLING_SEQUENCE_SELECT(VALUE);\
}while(0)

#define OPTCMB_MT_BYTE7                         (0x07)          //[6]: mtq_dly_en [7]: write_reg_mode
#define     OPTCMB_MT_DLY_EN                    (BIT6)
#define     OPTCMB_MT_WR_REG_MODE               (BIT7)

#define OPTCM_MT_DW7                            (0x1C>>2)       //[15:0] fpu_ptr, [30:28] polling seq
#define     OPTCMW_MT_FPU_PTR                   (0x1C>>1)

#define OPTCMB_MT_BYTE1F                        (0x1F)
#define     OPTCMB_POL_SEQ_BYTE_OFF             (4)

#define OPTCM_MT_DW8                            (0x20>>2)       //[15:0] fpu_ptr#2, [24]: busy#2, [25]:int_vct_en#2
#define     OPTCMW_MT_FPU_PTR_2                 (0x20>>1)
#define     OPTCM_MT_BUSY_2                     (BIT24)
#define     OPTCM_MT_INT_VCT_EN_2               (BIT25)

#define M_OPT_SET_OPTCM_WDMA_MSK_FLH_IO(VALUE)	(mtq->dw9.bits.wdma_msk_flh_io = (VALUE))

#define OPTCM_MT_DW10                           (0x28>>2)

#define OPTCM_MT_FSA0                           (0x28>>2)
#define OPTCM_MT_FSA1                           (0x30>>2)
#define OPTCM_MT_FSA2                           (0x34>>2)
#define OPTCM_MT_FSA3                           (0x38>>2)

enum {
	CQ_ATR_B1_CQ_FORMAT_NOT_RD = 0,
	CQ_ATR_B1_CQ_FORMAT_RD = 1
};

enum {
	PCA_SEL_iFSA0_ENA       = BIT0,
	PCA_SEL_iFSA1_ENA       = BIT1,
	PCA_SEL_iFSA2_ENA       = BIT2,
	PCA_SEL_iFSA3_ENA       = BIT3,
};

// -----------------------------------------------------------------
// mt parameters
// -----------------------------------------------------------------
typedef struct fpl_mtq_struct   FPL_MTQ_STRUCT, *FPL_MTQ_STRUCT_PTR;

struct fpl_mtq_struct {         // use union type to optimize.
	union {
		U32 all;
		struct {
			U32 first_op              : 1;    // LSB  , default: 0
			U32 pfa_int_en          : 1;  // default: COP0_MT_OPION[0]
			U32 force_r_fail        : 1;   // default: COP0_ATTR[0]
			U32 force_w_fail        : 1;  // default: COP0_ATTR[1]
			U32 busy                : 1    ; // default: 1
			U32 rsv1                : 1;
			U32 sta_no_stop         : 1;  // default: COP0_ATTR[2]
			U32 nxt_udma_dis        : 1;     // default: COP0_ATTR[16]
			U32 conv_page_h           : 4;    //default: 0
			U32 lp_crc_fail                : 1;
			U32 lp_crc_chk_en                : 1;   //default: COP0_ATTR[4]
			U32 pcacrc_chk_en                : 1;  //default: COP0_ATTR[7]
			U32 rsv2                : 1;
			U32 re_skip_cnt         : 8;              //default: 0
			U32 ultra_w_en          : 1;             //default: COP0_ATTR[18]
			U32 w_dma               : 1;           //default: 0
			U32 rsv3                : 2;              //default: 0
			U32 prog_buf_vld        : 4;          //default: 0
		} bits;
	} dw0;

	union {
		U32 all;
		struct {
			U32 conv_page           : 9;     // LSB,   default: 0
			U32 conv_bps            : 1;        //default: COP0_ATTR[3]
			U32 conv_page_en        : 1;     //default: 0
			U32 alu_sel             : 2;          //default: TIEIN.SLC_MODE
			U32 dis_udma            : 1;         //default: COP0_ATTR[7]
			U32 int_vct_en          : 1;       //default: 1
			U32 btMTWarmUpBypass    : 1;          //default: 1
			U32 rs_page             : 8;           //default: 0
			U32 ldpc_cor_en             : 1;            //default: 1
			U32 rs_last_page        : 1;         //default: 0
			U32 rs_parity_2nd_page  : 1;       //default: 0
			U32 rs_dis_par_cnt      : 1;            //default: 0
			U32 rs_one_parity_en    : 1;      //default: COP0_ATTR[4]
			U32 bch_bps             : 1;            //default: 0
			U32 mtq_dly_en          : 1;         //default: COP0_ATTR[16]
			U32 eot_chk_en          : 1;       //default: COP0_ATTR[27]
		} bits;
	} dw1;

	union {
		U32 all;
		struct {
			U32 nor_tar_cpu         : 3;     // LSB,    default: 4
			U32 err_tar_cpu         : 3;        //default: 4
			U32 rnd_seed_mode       : 2;      //default: COP0_ATTR[17:16]
			U32 int_vct             : 24;          //default: 0
		} bits;
	} dw2;

	union {
		U32 all;
		struct {
			U32 rs_parity_tlc_prog  : 1;        // LSB,   default: COP0_ATTR[5]
			U32 allow_switch        : 1;         //default: 0
			U32 erase               : 1;     //default: 0
			U32 spdrsm_seq_sel      : 3;  //default: 0
			U32 adg_sl_ptr          : 2;  //default: 0
			U32 io_type             : 1;  //default: COP0_CLKSW_ATTR[0]
			U32 fpu_pca_en          : 1;  //default: 0
			U32 fclk_div            : 3;  //default: COP0_CLKSW_ATTR[4:2]
			U32 raidecc_prog_keep_pec : 1;  //Tiein.PS_PROG_KEEP_PEC
			U32 raidecc_prog_dbuf   : 1;  //Tiein.PS_PROG_DBUF
			U32 rber_bps            : 1;  //default: 0
			U32 flh_type            : 2;  //default: COP0_CLKSW_ATTR[9:8]
			U32 time_cfg_sel        : 1; //default: COP0_CLKSW_ATTR[10]
			U32 upd_pol_seq         : 1;  //default: 1
			U32 no_read_dma                : 1;  //default: 0
			U32 pto_en              : 1;   //default: COP0_ATTR[13]
			U32 pto_len_sel         : 2;   //default: COP0_ATTR[15:14]
			U32 fta_h               : 8;   //default: 0, mt_ps5011.xlsx寫FTA[7:0]
		} bits;
	} dw3;

	union {
		U32 all;
		struct {
			U32 va0                 : 8;  //default: 0
			U32 va1                 : 8;  //default: 0
			U32 rsv1                : 8;      //default: 0
			U32 scale_mode          : 2;  //default: COP0_ATTR[21:20]
			U32 bmu_alct_en         : 1; //default: TIEIN.BMU_ALCT_EN
			U32 crc_chk_dis         : 1;  //default: COP0_ATTR[25]
			U32 sgn_dis0            : 1;  //default: 0
			U32 sgn_dis1            : 1;  //default: 0
			U32 sgn_dis2            : 1;  //default: 0
			U32 sgn_dis3            : 1;  //default: 0
		} bits;
	} dw4;

	union {
		U32 all;
		struct {
			U32 fpu_ptr             : 16;     // LSB, default: 0
			U32 ce_value            : 8;    //default: COP0_QUE_INDEX
			U32 decode_mode         : 3;  //default: COP0_ATTR[24:22]
			U32 pol_seq_sel         : 4;  //default: 0
			U32 ce_sel_mode         : 1; //default: COP0_MT_OPTION[1]
		} bits;
	} dw5;

	U32 dw6_rsv1;

	union {
		U32 all;
		struct {
			U32 seed_init           : 22;     // LSB, default: 0
			U32 cnv_wecnt              : 10; //default: 0
		} bits;
	} dw7;

	U32 dw8_userdefine;

	union {
		U32 all;
		struct {
			U32 l4k_spr_ptr         : 17;     // LSB, default: 0
			U32 gc                  : 1;  //default: TIEIN.GC
			U32 wdma_msk_flh_io     : 1;  //default: COP0_ATTR[28]
			U32 l4k_num             : 5;  //default: 0
			U32 frame_num           : 3;  //default: $countone(PAGE_VLD)
			U32 zip_en              : 1; //default: 0
			U32 cmp_en              : 1; //default: COP0_ATTR[8]
			U32 rsv1                : 2;
			U32 buf_mode            : 1; //default: (COP0_ATTR9] | (TIEIN.BMU_ALCT_EN) | (TIEIN.GETPBNA)
		} bits;
	} dw9;

	U32 dw10_iFSA0; // default: PCA_P0[31:0]

	union {
		U32 all;
		struct {
			U32 ifsa0_h             : 8;      // LSB,  default: PCA_P0[39:32]
			U32 ifsa1_h             : 8;      // default: PCA_P1[39:32]
			U32 ifsa2_h             : 8;     // default: PCA_P2[39:32]
			U32 ifsa3_h             : 8;    // default: PCA_P3[39:32]
		} bits;
	} dw11;

	U32 dw12_iFSA1;  // default: PCA_P1[31:0]
	U32 dw13_iFSA2;  // default: PCA_P2[31:0]
	U32 dw14_iFSA3;  // default: PCA_P3[31:0]
	U32 dw15_rsv2;
};

#define MTQ_DW0_SET_BSY             BIT(4)    /* Trigger MT status as internal busy */

/********************* E21 TRIGGER DATA **********************/
#define OPTCM_TRIG_DATA_DW0                     (0x40>>2)
#define     OPTCM_TRIG_DATA_OPT_STS_B0_B7_MASK	(BIT_MASK(8))
#define     OPTCM_TRIG_DATA_OPT_STS_B0_B7_SHIFT	(0)
#define     OPTCM_TRIG_DATA_PAR_RLS             (BIT8)
#define     OPTCM_TRIG_DATA_ERR_CNT_EN             (BIT9)
#define     OPTCM_TRIG_DATA_CQ_RD_FORMAT        (BIT10)    /* CQ_Attr: Bit1, CQ format: 1:Rd, 0:Others. */
#define     OPTCM_TRIG_DATA_OPT_STS_B8        (BIT11)
#define     OPTCM_TRIGGER_DATA_POL_SEQ_MASK        (BIT_MASK(3))
#define     OPTCM_TRIGGER_DATA_POL_SEQ_SHIFT        (12)
#define     OPTCM_TRIG_DATA_D1                  (BIT15)    // For Msg.Man.Record

#define     OPTCM_TRIG_DATA_SEED_INIT_SRC       (BIT16)    // 1 from DCCM, 0 from MRIN SEED_INIT
#define     OPTCM_TRIG_DATA_PCA_SEL_FSA0        (0) //E21 no use
#define     OPTCM_TRIGGER_DATA_RRTB_VA_VLD_MASK        (BIT_MASK(4))
#define     OPTCM_TRIGGER_DATA_RRTB_VA_VLD_SHIFT        (17)
#define     OPTCM_TRIG_DATA_RRTB_EN        (BIT21)
#define     OPTCM_TRIG_DATA_XPCA_EN             (BIT22)     // For write
#define     OPTCM_TRIG_DATA_MTP_HIGH_PRIO            (BIT23)     // For Mircon 3D
#define     OPTCM_TRIGGER_DATA_L4K_FW_2_MASK        (BIT_MASK(8))
#define     OPTCM_TRIGGER_DATA_L4K_FW_2_SHIFT        (8)

#define OPTCMB_TRIG_DATA_BYTE4                  (0x44)

#define OPTCM_TRIG_DATA_DW1                     (0x44>>2)
#define     OPTCM_TRIG_DATA_PAGE_VAL_MASK       (BIT_MASK(4))
#define     OPTCM_TRIG_DATA_PAGE_VAL_SHIFT       (0)

#define     OPTCM_TRIG_DATA_CMD_MASK       (BIT_MASK(4))
#define     OPTCM_TRIG_DATA_CMD_SHIFT       (4)

#define     OPTCM_TRIG_DATA_Q_IDX_MASK              (BIT_MASK(6))
#define     OPTCM_TRIG_DATA_Q_IDX_SHIFT              (10)

#define     OPTCM_TRIG_DATA_PRAM_RAM_IDX_MASK   (BIT_MASK(8))
#define     OPTCM_TRIG_DATA_PRAM_RAM_IDX_SHIFT    (16)

#define OPTCM_TRIG_DATA_DW2                     (0x48>>2)   // FCON_MT_CFG1
#define     OPTCM_TRIG_DATA_MT_IDX_MASK           (BIT_MASK(7))
#define     OPTCM_TRIG_DATA_MT_IDX_SHIFT           (0)

#define     OPTCM_TRIG_DATA_MTP_SMP_REQ           (BIT7)
#define     OPTCM_TRIG_DATA_LB_OFS_MASK           (BIT_MASK(9))
#define     OPTCM_TRIG_DATA_LB_OFS_SHIFT           (8)

#define     OPTCM_TRIG_DATA_RAIDECC_GPR_NUM_MASK	(BIT_MASK(3))
#define     OPTCM_TRIG_DATA_RAIDECC_GPR_NUM_SHIFT	(17)

#define     OPTCM_TRIG_DATA_FRC_EMP             (BIT20)     // force empty.
#define     OPTCM_TRIG_DATA_MT_SOFT_LOCK             (BIT21)
#define     OPTCM_TRIG_DATA_RAIDECC_OTFENC_EN   (BIT22)
#define     OPTCM_TRIG_DATA_QOS   (BIT23)
#define     OPTCM_TRIG_DATA_QUE_IDX_MASK        (BIT_MASK(5))
#define     OPTCM_TRIG_DATA_QUE_IDX_SHIFT         (24)

#define     OPTCM_TRIG_DATA_MT_HARD_LOCK        (BIT29)
#define     OPTCM_TRIG_DATA_MT_HARD_LOCK_CLEAR  (CLR_BIT29)
#define     OPTCM_TRIG_DATA_RAIDECC_PROG_PAR         (BIT30)
#define     OPTCM_TRIG_DATA_GRO_PRI_DEF         (BIT31)     // 1: high priority

#define OPTCM_TRIG_DATA_DW3                     (0x4C>>2)   // FCON_MT_CFG2

#define M_SET_OPTCMB_TRIG_DATA_PCA_SEL(VALUE)   // E21 remove pca_sel in mtd
// -----------------------------------------------------------------
// mt data
// -----------------------------------------------------------------
typedef struct cop0_mt_data_struct   COP0_MTD_STRUCT, *COP0_MTD_STRUCT_PTR;

struct cop0_mt_data_struct {         // use union type to optimize.
	union {
		U32 all;
		struct {
			U32 opt_status              :  8;    // LSB
			U32 par_rls                 :  1;    // release parameter.
			U32 err_cnt_en                : 1;//E21 new
			U32 cq_atr_b1_cq_format    : 1;    // CQ_Attr:Bit1. rd=1, others=0. bit10.	affect the format of COP0 CQ
			U32 opt_status_b8             : 1;    // E21 new, OPT_STS[8]
			U32 pol_seq_sel_b4_b5             : 2;    // E21 new, POL_SEQ_SEL[5:4]
			U32 rsv1					: 1;
			U32 D1			: 1;
			U32 seed_init_src           : 1;    // 1 from DCCM, 0 from MRIN SEED_INIT
			U32 rrtb_va_vld             : 4;    // rrtb_va_vld.
			U32 rrtb_en                : 1;
			U32 MTP_stall		   : 1;
			U32 MTP_high_prio 	   : 1;//E21 new
			U32 L4K_FW_2		: 8; //E21 new
		} bits;
	} dw0_dat;

	union {
		U32 all;
		struct {
			U32 pg_vld                  :  4;     // LSB
			U32 cmd                     :  4;
			U32 seq_wr              : 1;
			U32 nor_cq_rsp          : 1;
			U32 q_idx                   :  6;
			U32 pram_idx            : 9;
			U32 barrier_cmd_low2bits                : 2;
			U32 mt_format_sel           :  1;
			U32 IOR_en      : 1;
			U32 zip_en                : 1;
			U32 barrier_cmd_high2bits              : 2;
		} bits;
	} dw1_dat;

	union {
		U32 all;
		struct {
			U32 mt_idx              : 7;
			U32 mtp_smp_req                : 1;
			U32 lb_ofs              : 9;
			U32 rs_grp_num          : 3;
			U32 frc_emp             : 1;    // force empty
			U32 mt_soft_lock        : 1;
			U32 rs_otf_enc_en           :  1;     // RS_OTFENC_EN.
			U32 qos                     :  1;
			U32 que_idx             : 5;
			U32 mt_hard_lock        : 1;
			U32 rs_pg_par               :  1;     // RS_PROG_PARITY.
			U32 mtp_gro_pri_def         :  1;     // MTP_GRO_PRI_DEF.
		} bits;
	} dw2_mt_cfg1;

	union {
		U32 all;
		struct {
			U32 fw_rls_idx         :  9;
			U32 fw_rls_qos        :  1;
			U32 fw_rls_sel          :  1;
			U32 rsv0                  :  1;
			U32 rrtb_fsa_vld      :  4;//E21 new
			U32 chk_sts           : 1;
			U32 rber_bps          : 1;
			U32 mtq_dly           : 4;
			U32 die_num           : 2; // HW Gen in to MT_CFG_1
			U32 ce_delay_mode     : 2;
			U32 va_num            : 2;
			U32 fpu_get_feature   : 1;
			U32 rev1              : 3;
		} bits;
	} dw3_dat;
};
// -----------------------------------------------------------------
// 5011 banking data
// -----------------------------------------------------------------

//////////////////////////////////////////////////////////////// Others ////////////////////////////////////////////////////////////////
/* RAIDECC register */
#define RS_REG_BASE                               (0x80160000)
#define R8_RS                           ((_V U8  *)RS_REG_BASE)
#define R16_RS                                    ((_V U16 *)RS_REG_BASE)
#define R32_RS                                    ((_V U32 *)RS_REG_BASE)
#define R64_RS                                   ((_V U64 *)RS_REG_BASE)

#define R32_RAIDECC_TAG_CONFIG_0             	(0x0060 >> 2)
#define	RAIDECC_TAG_PEC_SHIFT							(24)
#define	RAIDECC_TAG_PEC_MASK							(BIT_MASK(8))
#define	RAIDECC_TAG_PRC_SHIFT							(16)
#define	RAIDECC_TAG_PRC_MASK							(BIT_MASK(8))
#define	RAIDECC_TAG_SEL_SHIFT				(0)
#define	RAIDECC_TAG_SEL_MASK				(BIT_MASK(9))
#define INVALID_PRC_0x00								(0x00)
#define INVALID_PRC_0xFF								(0xFF)

#define R8_RAIDECC_TAG_VLD_CTL                  (0x0070)
#define R32_RAIDECC_TAG_VLD_CTL                 (0x0070 >> 2)
#define 	RAIDECC_TAG_RD                              (BIT0)
#define	RAIDECC_TAG_RD_SHIFT				(0)
#define	RAIDECC_TAG_RD_MASK				(BIT_MASK(1))
#define RAIDECC_TAG_RD_1                            (BIT16)
#define	RAIDECC_TAG_RD_1_SHIFT						(16)
#define	RAIDECC_TAG_RD_1_MASK						(BIT_MASK(1))

#define R8_RAIDECC_TAG_SEL                      (0x0078)
#define R8_RAIDECC_TAG_SEL_1                    (0x00DC)

#define R32_RAIDECC_TAG_CONFIG_1             	(0x00E0 >> 2)
#define	RAIDECC_TAG_PEC_1_SHIFT						(24)
#define	RAIDECC_TAG_PEC_1_MASK						(BIT_MASK(8))
#define	RAIDECC_TAG_PRC_1_SHIFT						(16)
#define	RAIDECC_TAG_PRC_1_MASK						(BIT_MASK(8))
#define	RAIDECC_TAG_SEL_1_SHIFT						(0)
#define	RAIDECC_TAG_SEL_1_MASK						(BIT_MASK(9))
/* For UART Module */
#define UART_REG_BASE                               (0x00804C00)
#define UARTREGB                                    ((_V U8  *)UART_REG_BASE)
#define UARTREGW                                    ((_V U16 *)UART_REG_BASE)
#define UARTREGL                                    ((_V U32 *)UART_REG_BASE)
#define UARTREGLL                                   ((_V U64 *)UART_REG_BASE)

#define UARTL_TX_FIFO3_CTLST_0x94                   (0x94 >> 2)
#define UARTB_TX_FIFO3_CTLST_0x94                   (0x94)
#define TX_FIFO3_FULL                               (BIT0)
#define TX_FIFO3_EMPTY                              (BIT1)
#define TX_FIFO3_EN                                 (BIT8)
#define UARTB_TX_FIFO3_DATA_0x90                    (0x90)

#define UARTL_UART_CTRL             (0x00)
#define RX_RESET                    BIT31
#define CLR_RXLAT_POSI              0x70000000
#define CLR_BAUD_RATE               0xFF000000
#define BAUD_RATE(x)                ((U32)(x) & 0x00FFFFFF)
#define RX_LAT_POSI(x)              (((U32)(x) & 0x07) << 28)
#define TX_BAUD_EN                  BIT27
#define RX_BAUD_EN                  BIT26
#define INT_MASK_EN                 BIT25
#define OOB_EN                      BIT24

#define TX_TRIG                     (0x04)
#define SET_EMPTY_COMPLETE_EN       SET_BIT4
#define CLR_EMPTY_COMPLETE_DIS      CLR_BIT4

#define UARTL_TX_FIFO1_DATA         (0x74)
#define TX_FIFO1_DATA(x)            ((U32)(x) & 0xFF)

#define UARTL_TX_FIFO1_CTLST        (0x78)
#define TX_FIFO1_EN                 BIT8
#define TX_FIFO1_EMPTY              BIT1
#define TX_FIFO1_FULL               BIT0

#define UARTL_TX_FIFO2_DATA         (0x80)
#define TX_FIFO2_DATA(x)            ((U32)(x) & 0xFF)

#define UARTL_TX_FIFO2_CTLST        (0x84)
#define TX_FIFO2_EN                 BIT8
#define TX_FIFO2_EMPTY              BIT1
#define TX_FIFO2_FULL               BIT0

//!!!todo
/* For BANKING Module */
/* all */
#define OPT_FCON_BASE                               (0x80161000)
#define R32_OPT_FCON                                    ((_V U32 *)OPT_FCON_BASE)
#define R8_OPT_FCON									((_V U8 *)OPT_FCON_BASE)
#define OPT_FCON_MTQ0_EVT_NUM_OFFSET                (0x12C)
#define OPT_FCON_MTQ16_EVT_NUM_OFFSET               (0x2D4)
/* channel  */
#define OPT_FCTL_BASE                               (OPT_FCON_BASE + 0x800)
#define OPT_FCTL_MT_TRIG(x)                         (OPT_FCTL_BASE + (0x200 * x) + 0x24)
#define R32_OPT_FCON_IOR_CFG                        (0x3C8 >> 2)
#define		IDX_VAL_SHIFT							(0)
#define		IDX_VAL_MASK							BIT_MASK(3)

#define R8_FCON_RAIDECC_PB_SEL						(0x1F0)
#define FCON_RAIDECC_PB_SEL_NUM						(34)
#define R32_FCON_RAIDECC_PB_STS						(0x1F4 >> 2)
#define FCON_RAIDECC_PB_STS_BIT					(BIT16)
#define M_OPT_CHECK_FCON_RAIDECC_PB_STS_VALID(x)		((x & FCON_RAIDECC_PB_STS_BIT) != 0)
#define M_OPT_GET_FCON_RAIDECC_PB_TAG_NUM(x)	(x & BIT_MASK(8))

/* FIP mtq global trigger register */
#define OPT_FCON_MT_CFG                             (OPT_FCON_BASE + 0xEC)

#undef EXTERN
#endif /* _E21_OPT_ARCH_H_ */

