/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  opt_micron_read_disturb.c
 *
 *
 *
 ****************************************************************************/

#define _OPT_MICRON_READ_DISTURB_C_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "opt_micron_read_disturb.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */
#if (READ_DISTURB_PRDH_EN)

void OPTMicronClearOpenBlockReadCnt(COP0_PRDH_READ_CNT_STRUCT_PTR pReadCnt)
{
	U8 ubPlaneBankIdx;
	for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx++) {
		pReadCnt[ubPlaneBankIdx].ulReadCnt = 0;
	}
}

void OPTMicronReadCntHandle(OPT_JOB_STRUCT_PTR pJob, U8 ubCmdCnt)
{
	U8 ubPlaneIdx, ubScanQueueIdx, ubRandomPlane;
	U16 uwUserDefine = pJob->user_define;
	U8 ubBlkType = M_OPT_GET_LOOKUP_PRDH_BLK_TYPE(uwUserDefine);
	U8 ubHeadPlaneBank;
	U8 ubPCARule = ((pJob->slc_mode << 1) | pJob->ubD1);
	U8 ubGlobalDie = gOptStruct.cur_que * gubMaxDie + pJob->ubDie;
	U8 ubPlaneValid = pJob->plane_vld;
	U8 ubMaxPlane = gubMaxPlane;
	U32 ulFWPCA = pJob->pca;
	U16 uwUnit = (gOptStruct.ulPCAMaskFWUnit & ulFWPCA) >> gOptStruct.ubPCAMaskFWUnitShift;
	U16 uwURN = INVALID_URN_VALUE;
	U16 uwWindowSize;
	U32 ulReadCnt, ulOpenBlkReadThreshold;
	COP0_PRDH_READ_CNT_STRUCT_PTR pReadCnt = NULL;
	COP0_PRDH_SCAN_INFO_STRUCT_PTR pScanInfo = NULL;

	cpu_comm->uoTotalNandRead += ubCmdCnt;

	if (FALSE == M_OPT_CHECK_LOOKUP_PRDH_BYPASS_READ_CNT_HANDLE(uwUserDefine)) {

		if (ubBlkType) {
			// Skip BLK_TYPE_NONE

			cpu_comm->ubAndesModifyOPTDRequest = TRUE;
			while (TRUE != cpu_comm->ubAndesModifyOPTDRequest);
			__ASM_VOLATILE_MEMORY__();
			if (TRUE == cpu_comm->ubARMModifyOPTDRequest) {
				cpu_comm->ubAndesModifyOPTDRequest = FALSE;
				__ASM_VOLATILE_MEMORY__();
			}
			else {

				if (BLK_TYPE_OPEN_TABLE == ubBlkType) { // COP01 always tie in BLK_TYPE_OPEN_TABLE
					if (uwUnit != cpu_comm->uwOpenTableUnit) {
						ubBlkType = BLK_TYPE_FULL;
					}
				}

				if (BLK_TYPE_FULL == ubBlkType) {

					if (COP0_PCA_RULE_2 <= ubPCARule) { //Static SLC , Dynamic SLC
						uwWindowSize = gpReadDisturbThreshold->uwSLCReadVerifyReadCntThreshold[cpu_comm->ubPECG[ubPCARule]];
						--ubPCARule;
					}
					else { // QLC
						uwWindowSize = gpReadDisturbThreshold->uwTLCReadVerifyReadCntThreshold[cpu_comm->ubPECG[COP0_PCA_RULE_0]];
					}

					pReadCnt 	= (COP0_PRDH_READ_CNT_STRUCT_PTR)&gpMicronReadCnt->Full[ubPCARule][ubGlobalDie];
					pScanInfo 	= (COP0_PRDH_SCAN_INFO_STRUCT_PTR)&gpMicronScanInfo->Full[ubPCARule][ubGlobalDie];

					ulReadCnt 	= pReadCnt->prdh.uwReadCnt;
					uwURN 		= pReadCnt->prdh.uwURN;

					ulReadCnt += ubCmdCnt;

					if (uwURN && uwWindowSize) { // should not be zero

						if (ulReadCnt >= uwURN) { // Trigger ReadDisturb

							if (1 < ubCmdCnt) { // Random select plane if trigger by multiplane read
								ubRandomPlane = (OPTQL[R32_OPT_TIMER] % ubCmdCnt);
								for (ubPlaneIdx = 0; ubPlaneIdx < ubMaxPlane; ubPlaneIdx++) {
									if (ubPlaneValid & BIT(ubPlaneIdx)) {
										if (0 == ubRandomPlane) { //Change FWPCA to this plane
											ulFWPCA = ((ulFWPCA & ~(gubPlaneMask << 2)) | (ubPlaneIdx << 2));
											break;
										}
										--ubRandomPlane;
									}
								}
							}

							for (ubScanQueueIdx = 0; ubScanQueueIdx < SCAN_INFO_QUEUE_DEPTH; ubScanQueueIdx++) {
								if (INVALID_PCA_VALUE == pScanInfo[ubScanQueueIdx].ulFWPCA) {
									pScanInfo[ubScanQueueIdx].ulFWPCA = ulFWPCA;
									pScanInfo[ubScanQueueIdx].Record.prdh.uwReadCnt = ulReadCnt;
									pScanInfo[ubScanQueueIdx].Record.prdh.uwURN = uwURN;
									++cpu_comm->uwOverReadCntThresholdUnitNum;
									break;
								}
							}
							if (SCAN_INFO_QUEUE_DEPTH == ubScanQueueIdx) {
								//Scan queue full ,Without handling RC
								cpu_comm->ubAndesModifyOPTDRequest = FALSE;
								__ASM_VOLATILE_MEMORY__();
								return;
							}

							gpMicronScanInfo->ulScanBMP[ubPCARule] |= BIT(ubGlobalDie);
							pReadCnt->prdh.uwURN = INVALID_URN_VALUE;
						}

						if (ulReadCnt >= uwWindowSize) { // Reset ReadCnt & Re-gen URN
							ulReadCnt = ((ulReadCnt - uwWindowSize) % ubCmdCnt);
							// Make URN > ReadCnt , URN will result in [ReadCnt+1 , WindowSize]
							uwURN = ((OPTQL[R32_OPT_TIMER]) % uwWindowSize) + 1; // [1 , WindowSize]
							pReadCnt->prdh.uwURN = ((ulReadCnt >= uwURN) ? (ulReadCnt + 1) : uwURN);
						}

						pReadCnt->prdh.uwReadCnt = ulReadCnt;
					}
				}
				else { // Open block handle

					pScanInfo = (COP0_PRDH_SCAN_INFO_STRUCT_PTR)&gpMicronScanInfo->OpenSLCBlk[ubBlkType];
					pReadCnt = (COP0_PRDH_READ_CNT_STRUCT_PTR)&gpMicronReadCnt->OpenSLCBlk[ubBlkType];

					if (BLK_TYPE_OPEN_VT == ubBlkType) {
						ubPCARule = COP0_PCA_RULE_1; // record VT PECG
					}

					ulOpenBlkReadThreshold = M_READ_DISTURB_SLC_FORCE_COPY_THRESHOLD_ADJUSTMENT(gpReadDisturbThreshold->uwSLCForceCopyReadCntThreshold[cpu_comm->ubPECG[ubPCARule]]);
					ubHeadPlaneBank = gOptStruct.cur_que * ubMaxPlane;

					if (ulOpenBlkReadThreshold) { // should not be zero

						if (gpMicronReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType] != uwUnit) {
							//Unit changed , update & clear ReadCnt
							gpMicronReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType] = uwUnit;

							OPTMicronClearOpenBlockReadCnt(pReadCnt);

							//Update ReadCnt for current read
							for (ubPlaneIdx = 0; ubPlaneIdx < ubMaxPlane; ubPlaneIdx++) {
								if (ubPlaneValid & BIT(ubPlaneIdx)) {
									++pReadCnt[ubHeadPlaneBank + ubPlaneIdx].ulReadCnt;
								}
							}
						}
						else {
							// By PlaneBank handle ReadCnt
							for (ubPlaneIdx = 0; ubPlaneIdx < ubMaxPlane; ubPlaneIdx++) {

								if (ubPlaneValid & BIT(ubPlaneIdx)) {

									ulReadCnt = (pReadCnt[ubHeadPlaneBank + ubPlaneIdx].ulReadCnt + 1);

									if (ulReadCnt >= ulOpenBlkReadThreshold) {

										if (INVALID_PCA_VALUE == pScanInfo->ulFWPCA) {
											pScanInfo->ulFWPCA = ulFWPCA;
											pScanInfo->Record.ulReadCnt = ulReadCnt;
											++cpu_comm->uwOverReadCntThresholdUnitNum;
										}
										else {
											//Without handling ReadCnt
											cpu_comm->ubAndesModifyOPTDRequest = FALSE;
											__ASM_VOLATILE_MEMORY__();
											return;
										}

										OPTMicronClearOpenBlockReadCnt(pReadCnt);

										gpMicronScanInfo->ulScanBMP[EVENT_TYPE_OPEN_BLK] |= BIT(ubBlkType);

										break; //Stop handling other plane ReadCnt

									}
									else {
										++pReadCnt[ubHeadPlaneBank + ubPlaneIdx].ulReadCnt;
									}
								}
							}
						}
					}
				}
				cpu_comm->ubAndesModifyOPTDRequest = FALSE;
				__ASM_VOLATILE_MEMORY__();
			}
		}
	}
}
#endif /* (READ_DISTURB_PRDH_EN) */

