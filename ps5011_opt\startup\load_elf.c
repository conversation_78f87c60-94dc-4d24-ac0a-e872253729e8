/**** PHISON HIGHLY CONFIDENTIAL INFORMATION ****/
//#include "config.h"

extern char __data_lmastart;
extern char __data_start;
extern char _edata;
extern char __bss_start;
extern char _end;

void load_elf(void)
{
	int size;

#if 1
	/* load data section from lma to vma */
	size = &_edata - &__data_start;
	__builtin_memcpy (&__data_start, &__data_lmastart, size);

#endif


	/* Initialize bss section */
	size = &_end - &__bss_start;
	__builtin_memset (&__bss_start, 0, size);
}
