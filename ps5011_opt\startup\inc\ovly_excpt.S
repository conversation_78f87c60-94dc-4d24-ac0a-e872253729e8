/*
	This is an general exception handler.  An overlay exception can trigger this
	exception.

	Save the following registers before doing anything
	r0~r5, r15 and lp for 16 registers CPU
	r0~r5, r15~r27 and lp for 32 registers CPU

	NOTES:
	The following variables and functions are used here.  They are defined
	outside this file.
	_dma_busy: a flag/semaphore to signify whether DMA channel is busy or not
	_ovly_done: a flag/semaphore to signify whether overlay is done or not
	_ovly_table:
	_novlys:

	The following functions are provided by users.
	_abort: abort the program.  Its prototype is define as
		void	_abort(void)
		This function never return
	_ovly_load: load the specified overlay segment.  Its prototype is defined as
		int	_ovly_load(int seg_no)
		The implementation of this function can be synchronous or asynchronous I/O.


	STACK CONTENTS
	|	???		|	<-- sp upon entry
	|	lp		|
	|	r15		|
	|	r5		|
	|	r4		|
	|	r3		|
	|	r2		|
	|	r1		|
	|	r0		|	<-- sp before align
	|	???		|	<-- entry may not exist
	| sp before align i	|
	|	IPSW		|
	|	IPC		|	<-- sp

	This implementation is based on ABI2
*/

	.extern general_exception_dispatcher

	.section .text
	.global	GENERAL_EXCEPTION_HANDLER

GENERAL_EXCEPTION_HANDLER:
	! save registers
#ifdef NDS32_16REG
	smw.adm	$r15, [$sp], $r15, 0x2
#else
	smw.adm	$r15, [$sp], $r27, 0x2
#endif
	smw.adm	$r0, [$sp], $r5

	! align stack to 8-byte
	move	$r4, $sp
	andi	$r3, $sp, 4
	subri	$r3, $r3, 4
	sub	$sp, $sp, $r3
	push	$r4

	! save MAC regs here if necessary

	! save IPC and IPSW
	mfsr	$r1, $IPC
	mfsr	$r2, $IPSW
	smw.adm	$r1, [$sp], $r2
	move	$r0, $sp

	! adjust interrupt level
	mfsr	$r3, $PSW
	addi	$r3, $r3, #-0x2
	mtsr	$r3, $PSW

	! can call C function now; $r0 points to INT_CTX which contains IPC and IPSW
	! void general_exception_dispacther(INTR_CTX *pctx)
	! modify the contents of IPC and IPSW if necessary
	bal general_exception_dispatcher

	! restore registers and return
	setgie.d
	dsb
	lmw.bim	$r1, [$sp], $r2
	mtsr	$r1, $IPC
	mtsr	$r2, $IPSW

	! restore MAC regs if necessary

	! restore stack
	lwi	$sp, [$sp]

	! restore regs
	lmw.bim	$r0, [$sp], $r5
#ifdef NDS32_16REG
	lmw.bim	$r15, [$sp], $r15, 0x2
#else
	lmw.bim	$r15, [$sp], $r27, 0x2
#endif

	iret
	.size	GENERAL_EXCEPTION_HANDLER, .-GENERAL_EXCEPTION_HANDLER
