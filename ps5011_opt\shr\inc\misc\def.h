/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  def.h                                                                 */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _DEF_H_
#define _DEF_H_


/*------------------------------------------------------------------------------------
       Common Define
 ------------------------------------------------------------------------------------*/
#define MAX_U32_VALUE                   (0xFFFFFFFF)
#define MAX_U16_VALUE                   (0xFFFF)
//#define MAX_U8_VALUE                    (0xFF)

#define INVALID_LCA_VALUE               (0xFFFFFFFF)
#define INVALID_PCA_VALUE               (0xFFFFFFFF)

#define LCA_BYTE_SIZE                   (4)
#define PCA_BYTE_SIZE                   (4)

/*------------------------------------------------------------------------------------
       Device define
 ------------------------------------------------------------------------------------*/
#define PAGE_SIZE                       (4) /* 4 LC */
#define PAGE_SIZE_SHIFT                 (2)
#define PAGE_SIZE_MASK                  (PAGE_SIZE - 1)
#define PAGE_BYTE_SIZE                  (16384)

#define LC_SIZE                         (8) /* 8 Sectors */
#define LC_SIZE_SHIFT                   (3)
#define LC_SIZE_MASK                    (LC_SIZE - 1)
#define LC_BYTE_SIZE                    (4096)

#define SECTOR_SIZE                     (512)
#define SECTOR_SIZE_SHIFT               (9)
#define SECTOR_SIZE_MASK                (SECTOR_SIZE - 1)

/*
 * Capacity in sector
 * [IDEMA] LBA_counts = ( 97,696,368 ) + ( 1,953,504 * ( Advertised_Capacity_in_GBytes - 50GB))
 * [JEDEC] SSD_Capacity_in_Gbytes = ( User_addressable_LBA_count - 21168 ) / 1953504
 *         User_addressable_LBA_count = ( SSD_Capacity_in_Gbytes * 1953504 ) + 21168
 */
#define SSD_CAPACITY_30GB               (58626288)
#define SSD_CAPACITY_60GB               (117231408)
#define SSD_CAPACITY_120GB              (234441648)
#define SSD_CAPACITY_240GB              (468862128)
#define SSD_CAPACITY_480GB              (937703088)
#define SSD_CAPACITY_960GB              (1875385008U)
#define SSD_CAPACITY_1920GB             (3750748848U)

#define SSD_CAPACITY_4GB                (7835184)
#define SSD_CAPACITY_8GB                (15649200)
#define SSD_CAPACITY_16GB               (31298400)
#define SSD_CAPACITY_32GB               (62533296)
#define SSD_CAPACITY_64GB               (125045424)
#define SSD_CAPACITY_128GB              (250069680)
#define SSD_CAPACITY_256GB              (500118192)
#define SSD_CAPACITY_512GB              (1000215216)
#define SSD_CAPACITY_1TB                (2000409264U)
#define SSD_CAPACITY_2TB                (4000797360U)



/*
 *
 */
#if (CONFIG_PROG_DATA_TYPE == PROG_DATA_TYPE_2D_TLC)
#define MAX_D3_PROG_BUF_WL_NUM              (6) /* 4 ~  */
#elif (CONFIG_PROG_DATA_TYPE == PROG_DATA_TYPE_3D_TLC)
#define MAX_D3_PROG_BUF_WL_NUM              (12) /* 4 ~  */
#elif (CONFIG_PROG_DATA_TYPE == PROG_DATA_TYPE_3D_QLC)	//Reip Porting 3D-V7 QLC Add
#define MAX_D3_PROG_BUF_WL_NUM              (16) /* 4 ~  */
#endif



/*
 * CONFIG RW HCMD BATCH NUMBER
 */
#define MAX_HOST_RW_HCMD_BATCH_NUM          (8) /* 2 ~ 32, TBD */




#endif /* _DEF_H_ */

