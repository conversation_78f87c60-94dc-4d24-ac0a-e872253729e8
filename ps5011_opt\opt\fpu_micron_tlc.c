#include "fpu.h"

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)


#if NCS_EN
U16 guwFPUContentForNCS[0x0D] = {
	FPU_CMD(0xC9), FPU_CMD(0x80), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
};
#endif  /* NCS_EN */


U16 guwFPUEntryResetFAh[] = {
		FPU_OFFSET(gFpuEntryList.fpu_entry_reset_fa_lun0),
		FPU_OFFSET(gFpuEntryList.fpu_entry_reset_fa_lun1),
		FPU_OFFSET(gFpuEntryList.fpu_entry_reset_fa_lun2),
		FPU_OFFSET(gFpuEntryList.fpu_entry_reset_fa_lun3),
};

#if (VIRTUAL_ADDRESS_EN)
const U8 gubBinValue[BIN_PROFILE_NUM] =
{	0x00, 0x10, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00	};
#endif /* (VIRTUAL_ADDRESS_EN) */

/*
 * Unused List:
 * fpu_reserved0
 * fpu_reserved1
 * fpu_reserved2
 */
FPU_ENTRY_LIST gFpuEntryList = {
	/*  fpu_version */
	{
		FPU_VER_L, FPU_VER_H,
	},
	/*  fpu_entry_addr_gen_die */
	{
		FPU_ADR_GEN, FPU_NOP,
	},
	/*  fpu_entry_nop */
	{
		FPU_NOP, FPU_END,
	},
	/*  fpu_entry_addr_gen_only */
	{
		FPU_ADR_GEN, FPU_END,
	},

	/*
	 *
	 * auto poll fpu section
	 *
	 */
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	/*  fpu_entry_read_status_busy_40, Read Status for busy  */
	{
		FPU_NOP, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE0), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x40),  FPU_END,
	},
	/*  fpu_entry_read_status_busy_40_DIE1, Read Status for busy  */
	{
		FPU_NOP, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE1), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x40),  FPU_END,
	},
	/*  fpu_entry_read_status_busy_20, Read Status for busy  */
	{
		FPU_NOP, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE0), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20),  FPU_END,
	},
	/*  fpu_entry_read_status_busy_20_DIE1, Read Status for busy  */
	{
		FPU_NOP, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE1), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20),  FPU_END,
	},
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	/*  fpu_entry_read_status_busy_40, Read Status for busy  */
	{
		FPU_NOP, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x40),  FPU_NOP, FPU_END, // FPU_NOP, FPU_END,
		/*  fpu_entry_read_status_busy_20, Read Status for busy  */
		{
			FPU_NOP, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20),  FPU_NOP, FPU_END, //FPU_NOP, FPU_END,
		},
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	/*  fpu_entry_read_status_78_70_CURR_MODE_01, Read Status for fail  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x01),  FPU_END,
	},
	/*  fpu_entry_read_status_78_70_PREV_MODE_02, Read Status for fail  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x02),  FPU_END,
	},
	/*  fpu_entry_read_status_70_04_CURR_SLC_MODE, Read Status for fail  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x01),  FPU_END,
	},
	/*  fpu_entry_read_status_70_08_PREV_SLC_MODE, Read Status for fail  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x02),  FPU_END,
	},
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	/*  fpu_entry_read_status_78_70_busy_20_CURR_MODE_01, Read Status for PFA  */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE0), FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x01), FPU_END, FPU_NOP, FPU_NOP, FPU_NOP,
	},
	/*  fpu_entry_read_status_78_70_busy_20_CURR_MODE_01_DIE1, Read Status for PFA  */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE1), FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x01), FPU_END, FPU_NOP, FPU_NOP, FPU_NOP,
	},
	/*  fpu_entry_read_status_78_70_busy_40_PREV_MODE_02, Read Status for PFA  */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE0), FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x02), FPU_END, FPU_NOP, FPU_NOP, FPU_NOP,
	},
	/*  fpu_entry_read_status_78_70_busy_40_PREV_MODE_02_DIE1, Read Status for PFA  */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_VA_ROW_NUM), FPU_ADR_1B(ADDRESS_DIE1), FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x02), FPU_END, FPU_NOP, FPU_NOP, FPU_NOP,
	},
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	/*  fpu_entry_read_status_78_70_busy_20_CURR_MODE_01, Read Status for PFA  */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x01), FPU_END,
	},
	/*  fpu_entry_read_status_78_70_busy_40_PREV_MODE_02, Read Status for PFA  */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x02), FPU_END,
	},
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	/*  fpu_entry_read_status_70_busy_20_CURR_SLC_MODE_04, Read Status for PFA  */
	{
		FPU_NOP, FPU_NOP, FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x01), FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_40_PREV_SLC_MODE_08, Read Status for PFA  */
	{
		FPU_NOP, FPU_NOP, FPU_CMD_DQS(0x70),  FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x02), FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_40_INTERMEDIATE_MULTIPLANE_PROGRAM, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x00), FPU_END,
	},
	/* fpu_entry_read_status_70_busy_20_CURR_SLC_TLC_MODE_05, Read Status for PFA */
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x01), FPU_END
	},
	/*  fpu_entry_read_status_lun0_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(0), FPU_DLY(0x10), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_lun1_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(1), FPU_DLY(0x10), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20),  FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_lun2_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(2), FPU_DLY(0x10), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_lun3_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(3), FPU_DLY(0x10), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_78_busy_40, Read Status for busy  (Auto Poll Only)*/
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x40), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_78_busy_20, Read Status for busy  (Auto Poll Only)*/
	{
		FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20), FPU_NOP, FPU_END,
	},
	/*  fpu_read_and_compare_feature_data, Reserve for Check Get Feature Data, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/*  fpu_entry_read_status_err_81, Read Status for Check Error, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x80), FPU_DAT_R_MASK(0x81), FPU_END, FPU_NOP, FPU_NOP, FPU_END,
	},
	/* fpu_entry_read_status_70_busy_80_00, Dummy Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x80), FPU_DAT_R_MASK(0x00), FPU_END
	},
	/* fpu_entry_read_status_70_busy_20_00, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x00), FPU_END
	},

	/*	fpu_entry_getfeature_compare_data00_00_00_00  */
	{
		FPU_CMD(0x00), FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF),
		FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF), FPU_NOP, FPU_END,
	},
	/*	fpu_entry_getfeature_compare_data01_00_00_00  */
	{
		FPU_CMD(0x00), FPU_NOP, FPU_DAT_R_CMP(0x01), FPU_DAT_R_MASK(0xFF), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF),
		FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0xFF), FPU_NOP, FPU_END,
	},

	/* fpu_set_feature */
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_DLY(0x10), FPU_CMD_DQS(0x00), FPU_END,
	},
	/* fpu_get_feature */
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/**********************************************************
	** The Above Section is Used to Place Alignment FPU Sequence
	***********************************************************/

	/*
	 *
	 * error handle fpu section
	 *
	 */
	/*  fpu_entry_read, Page Read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_END,
	},

	/*  fpu_entry_dma_r_05_e0, Rand data out.  */
	{
		// Micron use 06h-E0h
		FPU_ADR_GEN, FPU_CMD(0x06), FPU_ADR(ADDRESS_DMA_NUM), FPU_CMD_DQS(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},

	/*  fpu_entry_dma_r_06_e0_poll_ready */
	{
		FPU_ADR_GEN, FPU_CMD(0x06), FPU_ADR(ADDRESS_DMA_NUM), FPU_CMD_DQS(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_DLY(0x15), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_END,
	},

	/*  fpu_reserved0, Rand data out.  */
	{
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP
	},
	/*  fpu_entry_dump_ibuf  */
	{
		FPU_BR(0x00), FPU_END,
	},
	/*  fpu_entry_dma_r_raw  */
	{
		// Micron use 06h-E0h
		FPU_ADR_GEN, FPU_CMD(0x06), FPU_ADR(ADDRESS_DMA_NUM), FPU_CMD(0xE0),
		FPU_NOP, FPU_NOP, FPU_DLY(0x10), FPU_DMA_R_RAW(0x00),  // !!!  DON'T CHANGE THE POSITION OF "FPU_DMA_R_RAW(0x00)"  !!!
		FPU_END,
	},
	/*
	 *
	 * normal fpu section
	 *
	 */

	/*  fpu_entry_reset_ff, Toggle/Lagacy reset  */
	{
		FPU_CMD(0xFF), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_reset_fc, Onfi Reset.  */
	{
		FPU_CMD(0xFC), FPU_DLY(0x10), FPU_END,
	},
	/* fpu_entry_reset_fa_lun0, reset lun */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(0), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10), FPU_ADR_GEN, FPU_CMD(0xFA), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_END, FPU_NOP, FPU_NOP,
	},
	/* fpu_entry_reset_fa_lun1, reset lun */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(1), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10), FPU_ADR_GEN, FPU_CMD(0xFA), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_END, FPU_NOP, FPU_NOP,
	},
	/* fpu_entry_reset_fa_lun2, reset lun */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(2), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10), FPU_ADR_GEN, FPU_CMD(0xFA), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_END, FPU_NOP, FPU_NOP,
	},
	/* fpu_entry_reset_fa_lun3, reset lun */
	{
		FPU_CMD_DQS(0x71), FPU_ADR_1B(3), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10), FPU_ADR_GEN, FPU_CMD(0xFA), FPU_ADR(ADDRESS_ROW_NUM), FPU_DLY(0x10), FPU_END, FPU_NOP, FPU_NOP,
	},

	/*
	 * Read command: slc / mlc mode + single plane mode.
	 */



	/*  fpu_entry_slc_read, A2 Page Read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*
	 * Erase command: slc / mlc mode + plane mode.
	 */


	/*  fpu_entry_erase, Block Erase w/ fsa  */
	{
		ER_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_CMD(0xD0), FPU_END,
	},
	/*  fpu_entry_slc_erase, A2 Block Erase w/ fsa  */
	{
		ER_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_CMD(0xD0), FPU_END,
	},

	/*  fpu_entry_slc_60h_row_erase  */
	{
		ER_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_END,
	},

	/*  fpu_entry_tlc_60h_row_erase  */
	{
		ER_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_END,
	},

	/*  fpu_entry_2p_erase, 2-plane Block Erase w/ fsa  */
	{
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_CMD(0xD0), FPU_DLY(0x10), FPU_END,
	},


	/*
	 * misc
	 */

	/*  fpu_entry_read_dma  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_NOP, FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_dly_only  */
	{
		FPU_DLY(0x01), FPU_END,
	},

	/*  fpu_entry_erase_all_done  */
	{
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(ADDRESS_ERASE_NUM), FPU_CMD(0xD0), FPU_END,
	},
	/*  fpu_entry_70 */
	{
		FPU_CMD_DQS(0x70), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_prog_to_flash_cache, Page Program w/ fsa  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_END,
	},

	/*  fpu_entry_read_from_flash_cache, Rand data out.  */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(ADDRESS_DMA_NUM), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},

	/*  fpu_entry_C85_A5_NoDMAW  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_DMA_W_RAW  */
	{
		FPU_DMA_W_RAW(FPU_RAW_PROG_IBF), FPU_END,
	},
	/*  fpu_entry_DMA_R_RAW  */
	{
		FPU_DMA_R_RAW(FPU_RAW_DST_IBUF(0) | FPU_RAW_SRC_IBUF(1) | FPU_RAW_SET_MODE(2) | FPU_RAW_LOGIC_NOP), FPU_END,
	},
	/*  fpu_entry_C11  */
	{
		FPU_CMD_DQS(0x11), FPU_END,
	},
	/*  fpu_entry_C00_A5_C05_A2_CE0  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x05), FPU_ADR(2), FPU_CMD(0xE0), FPU_END,
	},


	/*
	 * read/cache read
	 */

	/*
	 * Read command: slc / mlc mode + plane cache mode.
	 */
	/*  fpu_entry_slc_3F_read. */
	{
		//No FPU_END
		RD_SLC_PREFIX,
	},
	/*  fpu_entry_3F_read, */
	{
		FPU_ADR_GEN, FPU_CMD(0x3F), FPU_END, FPU_NOP, FPU_NOP, FPU_NOP,
	},

	/*  fpu_entry_slc_1p_20_read  */
	{
		RD_SLC_PREFIX
	},
	/*  fpu_entry_tlc_1p_20_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x20), FPU_DLY(0x15), FPU_END,
	},
	/*  fpu_entry_slc_1p_30_read  */
	{
		RD_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_1p_30_read  */
	{
		RD_TLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_1p_31_read  */
	{
		RD_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_1p_31_read  */
	{
		RD_TLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},

	/*  fpu_entry_slc_2p_32_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_30_read  */
	{
		RD_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_2p_32_31_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_31_read  */
	{
		RD_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_slc_2p_32_F1_30_read  */
	{
		RD_SLC_PREFIX
	},
	/*  fpu_entry_tlc_2p_32_F1_30_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_2p_32_F2_30_read  */
	{
		RD_SLC_PREFIX,
	},
	/*  fpu_entry_tlc_2p_32_F2_30_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_4p_32_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_30_read  */
	{
		RD_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},

	/*  fpu_entry_slc_4p_32_31_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_31_read  */
	{
		RD_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},

	/*	fpu_entry_slc_3p_32_30_read */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_3p_32_30_read  */
	{
		RD_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_END,
	},

	/* 	fpu_entry_slc_3p_32_31_read */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_3p_32_31_read  */
	{
		RD_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD_DQS(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x31), FPU_END,
	},

	/*  fpu_common_region_delimiter, used for checking common_region size <= 2KB  */
	{
		FPU_NOP, FPU_END,
	},


	/*
	 * Program command: slc / mlc mode.
	 */


	/*  fpu_entry_prog_mlc_80_11, Page Program w/ fsa  */
	{
		WR_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_prog_mlc_80_15, Page Program w/ fsa  */
	{
		WR_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
	/*  fpu_entry_prog_slc_80_11, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_prog_slc_80_11_gc, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	/*  fpu_entry_prog_slc_80_15, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},

#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_prog_slc_80_15_gc, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	/*  fpu_entry_prog_slc_80_11_F1, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_prog_slc_80_11_F2, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_mlc_prog, Page Program w/ fsa  */
	{
		WR_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},

	/*  fpu_entry_slc_prog, A2 Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_slc_prog_gc, A2 Page Program w/ fsa  */
	{
		WR_SLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	// william need cache program
	{
		FPU_CMD(0xA2), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_NOP, FPU_NOP, FPU_END,	// FPU_PTR_CA2_C85_A5_DW
	},

	/*
	 * program command: tlc mode.
	 */

	/*  fpu_entry_m3d_tlc_prog_10  */
	{
		WR_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_m3d_tlc_prog_10_gc  */
	{
		WR_TLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/

	/*  fpu_entry_m3d_tlc_prog_11  */
	{
		WR_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_m3d_tlc_prog_11_gc  */
	{
		WR_TLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	/*  fpu_entry_m3d_tlc_prog_15  */
	{
		WR_TLC_PREFIX,
		FPU_ADR_GEN,  FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_m3d_tlc_prog_15_gc  */
	{
		WR_TLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN,  FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	/*  fpu_entry_m3d_tlc_prog_11_die0  */
	{
		WR_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD_DQS(0x78), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_m3d_tlc_prog_11_die1  */
	{
		WR_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(PROG_CMD), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD_DQS(0x78), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_getfeature_temperature_die0  */
	{
		FPU_CMD(0xD4), FPU_ADR_1B(0x00), FPU_ADR_1B(0xE7), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_getfeature_temperature_die1  */
	{
		FPU_CMD(0xD4), FPU_ADR_1B(0x01), FPU_ADR_1B(0xE7), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_getfeature_dma  */
	{
		FPU_CMD(0x00), FPU_DLY(0x10), FPU_DMA_R, FPU_END,
	},
	/*	fpu_entry_check_plane_status_mk20  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x78), FPU_ADR(ADDRESS_ERASE_NUM), FPU_DLY(0x18), FPU_POL_MASK(0x20), FPU_NOP, FPU_NOP, FPU_END,		//FPU_PTR_C78_ADR3_POL_MK20_C00
	},
	/*
	 * Error Handle - Softbit
	 */
	//For E11 verification
	/*  fpu_entry_adg_test  */
	{
		FPU_ADR_GEN, FPU_ADR(0x05), FPU_END,
	},
	/*  fpu_entry_check_status_to_ready  */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(BIT6), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_CA2_C00_A5_C30  */
	{
		/*0x0000*/ RD_SLC_PREFIX, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x30), FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_C05_A5_CE0_DR  */
	{
		// Micron use 06h-E0h
		/*0x0010*/	FPU_CMD(0x06), FPU_ADR(ADDRESS_DMA_NUM), FPU_CMD(0xE0), FPU_DLY(0x10), FPU_DMA_R, FPU_NOP, FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_C05_A2_CE0_DR  */
	{
		/*0x0020*/	FPU_CMD(0x05), FPU_ADR(0x02), FPU_CMD(0xE0), FPU_DLY(0x10), FPU_DMA_R, FPU_NOP, FPU_NOP, FPU_END,
	},

	/*  fpu_reserved1  */
	{
		/*0x0030*/
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP
	},

	/*  fpu_entry_test_C70_POL_MK40_C00  */
	{
		/*0x0040*/	FPU_CMD_DQS(0x70), FPU_DLY(0x18), FPU_POL_MASK(0x40), FPU_CMD(0x00), FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	//below is testing usage
	/*  fpu_entry_test_CA2_C80_A5_DW_C10  */
	{
		/*0x0050*/	FPU_CMD(0xA2), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_C85_A5_DW  */
	{
#if NES_GEN2_EN
		/*0x0060*/	FPU_CMD_DQS(0x85), FPU_DLY(0x10), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_NOP, FPU_NOP, FPU_END,
#else /*NES_GEN2_EN*/
		/*0x0060*/	FPU_CMD_DQS(0x85), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_NOP, FPU_END,
#endif /*NES_GEN2_EN*/
	},

	/*  fpu_entry_test_CA2_C60_A3_CD0  */
	{
		/*0x0070*/	ER_SLC_PREFIX, FPU_CMD(0x60), FPU_ADR(0x03), FPU_CMD(0xD0), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_END
	},

	/* fpu_entry_70_e0 */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_END
	},

	/* fpu_raw_dma_read */
	{
		// Micron use 06h-E0h
		FPU_ADR_GEN, FPU_CMD(0x06), FPU_ADR(ADDRESS_DMA_NUM), FPU_CMD(0xE0),
		FPU_NOP, FPU_NOP, FPU_DLY(0x60), FPU_DMA_R_RAW(0x00),  // !!!  DON'T CHANGE THE POSITION OF "FPU_DMA_R_RAW(0x00)"  !!!
		FPU_END
	},

	/* fpu_tlc_read_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tlc_read_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tlc_read_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},

	/* fpu_tlc_rr_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END
	},
	/* fpu_tlc_rr_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_DLY(0x10), FPU_END
	},
	/* fpu_tlc_rr_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_DLY(0x10), FPU_END
	},
	/* fpu_slc_micron_sb_read */
	{
		RD_SLC_PREFIX,
	},
	/* fpu_tlc_micron_sb_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0x34), FPU_DLY(0x10), FPU_END, FPU_END, FPU_END
	},
	/* fpu_sbc */
	{
		FPU_SBC(0x00), FPU_END
	},
	/* fpu_backup_restore_ibuf */
	{
		FPU_BR(0x00), FPU_BR(0x00), FPU_BR(0x00), FPU_BR(0x00), FPU_END
	},

	/* fpu_00_30_read */
	{
		FPU_ADR_GEN,  FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_ram_cmd */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DMA_R_RAW(FPU_RAW_1ST_FRM | FPU_RAW_LOGIC_NOP), FPU_DMA_R_RAW(FPU_RAW_2ND_FRM | FPU_RAW_LOGIC_NOP), FPU_END
	},
	/* fpu_correct */
	{
		FPU_ADR_GEN, FPU_DMA_R_COR(0x00), FPU_END
	},
	/* fpu_reserved2 */
	{
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP
	},
	/* fpu_slc_prefix_read_retry */
	{
		RD_SLC_PREFIX,
	},
	/* fpu_tlc_prefix_read_retry */
	{
		FPU_CMD(0x2E), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_ADR_1B(0x00), FPU_CMD(0x30), FPU_END,
	},
	/* fpu_micron_set_mlbi_cmd */
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_END

	},
	/* fpu_micron_get_mlbi_cmd */
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_micron_set_mlbi_cmd_by_MT */
	{
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, 0, 0, FPU_END
		},
	},
	/* fpu_micron_get_mlbi_cmd_by_MT */
	{
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
		{
			0, 0, 0, 0, FPU_END
		},
	},

	/* fpu_entry_06_E0_no_dma_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x06), FPU_ADR(ADDRESS_READ_NUM), FPU_CMD(0xE0), FPU_NOP, FPU_NOP, FPU_NOP, FPU_END
	},

	/*fpu_micron_nand_mode_use*/
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_entry_slc_ndep_30_read_bin0 */
	{
		RD_SLC_PREFIX,
	},
	/* fpu_entry_tlc_ndep_30_read_bin0 */
	{
		FPU_CMD(0x2E), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_GEN,  FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_ADR_1B(0), FPU_CMD(0x30), FPU_END
	},

	/* fpu_entry_slc_ndep_30_read_bin1 */
	{
		RD_SLC_PREFIX,
	},
	/* fpu_entry_tlc_ndep_30_read_bin1 */
	{
		FPU_CMD(0x2E), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_GEN,  FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), FPU_ADR_1B(0), FPU_CMD(0x30), FPU_END
	},
	/* fpu_entry_ctrl_re_low */
	{
		FPU_GPO(0x80), FPU_NOP, FPU_NOP, FPU_END,
	},
	/*  fpu_entry_slc_1p_20_read_bin1  */
	{
		RD_SLC_PREFIX,
	},
	/*  fpu_entry_tlc_1p_20_read_bin1  */ //Get read offset: 07h-E0h //delay time: 05-E0 (400 ns), 07-E0(600 ns)
	{
		FPU_ADR_GEN, FPU_CMD(0x07), FPU_ADR(4), FPU_CMD(0xE0), 0, FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_slc_1p_30_read_bin1 */ // for N48R prefix part1
	{
		//No FPU_END
		RD_SLC_PREFIX,
	},
	/*  fpu_entry_tlc_1p_30_read_bin1 */
	{
		FPU_CMD(0x2E), 0, 0, 0, 0, FPU_NOP,
	},
	/*  fpu_entry_slc_1p_30_read_bin2*/ // for N48R prefix part2
	{
		//No FPU_END
		FPU_NOP,
	},
	/*  fpu_entry_tlc_1p_30_read_bin2*/
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_READ_NUM), 0, FPU_CMD(0x30), FPU_END,
	},
};
#endif /* (MICRON_S17_E21_140S_EN && PS5017_EN) */

