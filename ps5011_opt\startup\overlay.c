/**** PHISON HIGHLY CONFIDENTIAL INFORMATION ****/
//#include <stdlib.h> //abort
//#include <sys/types.h>  //open
//#include <sys/stat.h>   //open
//#include <fcntl.h>  //open
//#include <unistd.h> //lseek
//#include <stdio.h>  //printf
//#include <errno.h>  //errno
//#include "nds32_intrinsic.h"
//
//#include "def_fun.h"
//#include "vars.h"
//
//#include "uart.h"
//#include "flash1.h"
//#include "misc.h"
#include "ovly_excpt.h"
#include "nds32_intrinsic.h"


/*
    THIS OVERLAY IMPLEMENTATION ASSUMES THE WHOLE PROGRAM IS DIVIDED INTO ONE
    ROOT SEGMENT AND MULTIPLE OVERLAY SEGMENTS.  THE SIZE OF ROOT SEGMENT MUST
    BE INTEGER MULTIPLE OF PAGE SIZE AND THE SIZE OF EACH OVERLAY SEGMENT IS
    EXACTLY THE SIZE OF ONE PAGE.
*/

/*
    TYPES OF GENERAL EXCEPTION
*/
#define GE_ALIGN_CHECK          0 // cpu will hang up if this exception
#define GE_RESERVED_INST        1   // Overlay Exceptions
#define GE_TRAP                 2
#define GE_ARITHMETIC           3
#define GE_PRECISE_BUS_ERR      4
#define GE_INPRECISE_BUS_ERR    5
#define GE_COPROCESSOR          6
#define GE_PRIVILEGE_INST       7
#define GE_RESERVED_VALUE       8
#define GE_NON_EXIST_LOCAL_MEM  9
#define GE_MPZIU_CTRL           10


///*
//    overlay busy lock
//*/
//static volatile BOOL _gbOverlayBusy = 0;
//
//
//#if OVLY_DEBUG
///* DO NOT MODIFY THE FOLLOWING DEBUGGING HOOKS; KEEP IT AS IS */
///*!
//  @brief DO NOT MODIFY THE FOLLOWING DEBUGGING HOOKS; KEEP IT AS IS
//  @return NONE
//  @note
//  - NOTE
//*/
//static void _ovly_debug_event (void)
//{
//}
//#endif
//
//
///*
//    load a target overlay segment into memory page
//    return value: 0 means illegal instruction
//            -1 means fail
//            other positive value means old segment number in memory page
//*/
////extern char __ovly_lmastart;
///*!
//  @brief load a target overlay segment into memory page
//  @param[in] ipc    the interrupt program counter value
//  @param[in] povl   the over lay register which need to use
//  @return NONE
//  @note
//  - NOTE
//*/
//static void overlay_load (unsigned int ipc, OVLY_REGS * povl)
//{
//#if EN_OVERLAY
//    U16  i;
//    U32  dwStartAddr;
//    U8   byTmpFDev;
//    U16  wPBlock;
//    U8   byPPlane;
//    U16  wPPage;
//    U16  wStartPage;
//    U8   byBankPerPage = BANK_SECTOR >> gbySectorBitNo;
//    int  old_seg = (povl->base_addr >= povl->root_size) ?  (povl->base_addr - povl->root_size) / OVLY_PAGE_SIZE : -1;
//    int  new_seg = ((ipc - povl->root_size) / OVLY_PAGE_SIZE);
//    //int rc;
//    //int section_size;
//
//    //printf ("In overlay_load ipc is %#x\n", ipc);
//#if OVLY_DEBUG
//    {
//        /* Mark old overlay segment unmapped */
//        if ((old_seg >= 0) && (old_seg < NUM_OVLY))
//            _ovly_table[old_seg].mapped = 0;
//    }
//#endif
//
//    uart_show_msg("\r\n Banking");
//    //uart_show_var_hex("base", povl->base_addr);
//    //uart_show_var_hex("ipc", ipc);
//
//    /* init overlay registers first */
//    dwStartAddr =
//        (((ipc - povl->root_size) >> OVLY_PAGE_BITS) << OVLY_PAGE_BITS) +
//        povl->root_size;
//    povl->base_addr = dwStartAddr;
//    povl->end_addr = dwStartAddr + OVLY_PAGE_SIZE;
//
//    /* then, read overlay segment to memory page */
//    /* You can replace this part with your DMA initialization */
//    /* In this case, this is asynchronous I/O */
//
//    // 1. backup some global variables
//    gbCodeBlockFail = 0;
//    byTmpFDev = gbyFDevice;
//    wPBlock = gwPBlock;
//    byPPlane = gbyPPlane;
//    wPPage = gwPPage;
//    switch_to_device0_plane0();
//
//    ENABLE_TSB_PSLC();
//
//LOAD_CODE:
//    // 2. calculate the new_seg's address
//    if (gbUseTmpCode) {
//        if (gbUse2ndCode) {
//            gwPBlock = MC_VAR.tmp_code2_BS.wPBlk[0];
//        } else {
//            gwPBlock = MC_VAR.tmp_code1_BS.wPBlk[0];
//        }
//        wStartPage = (new_seg * byBankPerPage);
//    } else {
//        gwPBlock = gwCodeBlk;
//        wStartPage = (new_seg * byBankPerPage) + gbyBank0Page;
//    }
//    gbyPPlane = 0;
//
//    for (i = 0; i < byBankPerPage; i++) {
//        gwPPage = get_lower_page_mapping((wStartPage + i), 1);
//        // 3. read page into overlay sram
//        set_addr_buf_single_plane();
//        flash_read_page((OVL_BASE_PTR + (i << gbySectorBitNo)), TMP_SPARE_PTR, 0, gbySecPerPage);
//
//        // 4. ecc error handling
//        if (gbEccErr || (TMP_SPARE_BUF[6] != CODE_MARK1)) {
//            gbCodeBlockFail = 1;
//            break;
//        }
//    }
//
//    if (gbCodeBlockFail) {
//        if (gbAnotherCodeErr) {
//            // 2 code block fail
//            _assert(0x3A);
//            //gbDisWrite = 1;
//            //gbyTableErr = 0x99;
//            //gbCardWp = 1;
//            //set_card_lock();
//            //enable_write_protect();
//        } else {
//            // only 1 code block fail
//            gbAnotherCodeErr = 1;
//            gbCodeBlockFail = 0;
//            gbUse2ndCode = (~gbUse2ndCode);
//            goto LOAD_CODE;
//        }
//    }
//
//    // 5. restore global variables
//    gbyFDevice = byTmpFDev;
//    gwPBlock = wPBlock;
//    gbyPPlane = byPPlane;
//    gwPPage = wPPage;
//    sel_flash_by_fdevice();
//    RESTORE_TSB_PSLC();
//
//#if OVLY_DEBUG
//    {
//        /* DO NOT MODIFY THE FOLLOWING DEBUGGING CODE; KEEP THEM AS THEY ARE */
//        _ovly_table[new_seg].mapped = 1;
//        _ovly_debug_event ();
//    }
//#endif
//#endif
//}


/*!
  @brief general exception dispatcher of CPU
  @param[in] pctx    the interrupt type value
  @return NONE
  @note
  - NOTE
*/
void general_exception_dispatcher (INTR_CTX *pctx)
{
	/* Interrupt is still disabled at this point */
	/* get all needed system registers' values before re-enable interrupt */
	unsigned int itype = __nds32__mfsr (NDS32_SR_ITYPE);

	//    PRINT_LED(0x44);
	//    PRINT_LED(itype & 0xf);

	switch (itype & 0xf) {
	//        case GE_RESERVED_INST: {
	//            /* overlay page fault will trigger a reserved instruction exception */
	//            /* we need to distinguish page fault from real reserved instruction */
	//            OVLY_REGS *povl = OVLY_CTRL_BASE;
	//            //unsigned int old_seg;
	//
	//            /* illegal instruction or page fault? */
	//            if (povl->root_size > pctx->ipc
	//                    || (povl->base_addr <= pctx->ipc && povl->end_addr > pctx->ipc)) {
	//                /* ILLEGAL INSTRUCTION;
	//                   Please Handle it.
	//                 */
	//                //puts ("ILLEGAL INSTRUCTION!");
	//                //printf ("ipc is %#x\n", pctx->ipc);
	//                abort();
	//            } else if (pctx->ipc >= povl->root_size
	//                       && pctx->ipc < (povl->root_size + (OVLY_PAGE_SIZE * NUM_OVLY))) {
	//                /* page fault and ipc is inbound */
	//                if (!_gbOverlayBusy) {
	//                    /* lock it */
	//                    _gbOverlayBusy = 1;
	//
	//                    /* Enable INTERRUPT; allow other ISRs or threads to run */
	//                    __nds32__setgie_en ();
	//                    __nds32__dsb ();
	//                    overlay_load (pctx->ipc, povl);
	//
	//                    /* allow the next page fault handling */
	//                    _gbOverlayBusy = 0;
	//
	//                    /* no need to update IPC, execute the same instruction again */
	//                } else {
	//                    /*
	//                    overlay manager is busy;
	//                    it means other ISR or thread is calling some overlay
	//                    this case should not happen for a single page overlay system
	//                    */
	//                    //puts ("overlay manager is busy");
	//                    abort();
	//                }
	//            } else {
	//                /* out of bound */
	//                //puts ("out of bound");
	//                //printf ("ipc is %#x\n", pctx->ipc);
	//                abort();
	//            }
	//
	//            break;
	//        }
	case GE_ALIGN_CHECK:
	case GE_TRAP:
	case GE_ARITHMETIC:
	case GE_PRECISE_BUS_ERR:
	case GE_INPRECISE_BUS_ERR:
	case GE_COPROCESSOR:
	case GE_PRIVILEGE_INST:
	case GE_RESERVED_VALUE:
	case GE_NON_EXIST_LOCAL_MEM:
	case GE_MPZIU_CTRL:
	// enable hard reset to avoid cpu hangup
	//            if (gbUhs2Mode) {
	//                rdw_UHS2_REG[UHS2_RESET_EN_REG] |= SET_EN_HARD_RESET;
	//            } else {
	//                rdw_MS_TOP[BUFMODE_CTRL2] &= CLR_CMD0_HW_RESET;   //Hard reset
	//            }
	default:
		break;
	}
}
