/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_prog.c                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_PROG_C_

#include "opt_main.h"
#include "opt_arch.h"
#include "opt_const.h"
#include "opt_hal.h"
#include "opt_mt_ext.h"
#include "opt_prog.h"
#include "fpu.h"
#include "opt_debug.h"
#include "nds32_intrinsic.h"
#include "opt_api.h"


static BOOL opt_mt_group_d2_prog_data_in(OPT_JOB_STRUCT_PTR job, OPT_JOB_STRUCT_PTR prev_job, BOOL prev_job_valid, BOOL cache_cmd, BOOL split);
static BOOL opt_mt_group_d2_prog_chk_status(OPT_JOB_STRUCT_PTR job, BOOL wait_true_ready, BOOL check_previous);

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
static U32 opt_samsung_confirm_fsa_transform(U32 ulFSA);
#endif

BOOL opt_macro_cmd_d2_normal_prog(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1;
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_1 == job->macro_state) {
		if (opt_mt_group_d2_prog_data_in(job, NULL, FALSE, FALSE, FALSE)) {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_NON_CACHE_2 == job->macro_state) {
		if (opt_mt_group_d2_prog_chk_status(job,
				((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (FALSE == job->slc_mode) && (FALSE == (GET_PREREAD_DISABLE_STATE_QUEUE(gOptStruct.cur_que)))),
				FALSE)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
		opt_dccm_log_debug(0xF1F1F1F1);//normal_prog chk status no fail
	}
	return FALSE;
}

BOOL opt_macro_cmd_d2_cache_prog_start(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_PRE_CACHE;
	}

	if (OPT_MACRO_CMD_STATE_RUN_PRE_CACHE == job->macro_state) {
			opt_dccm_log_debug(0x1A1A1A1A);//cache prog start
		if (opt_mt_group_d2_prog_data_in(job, NULL, FALSE, TRUE, FALSE)) {
			job->macro_state = OPT_MACRO_CMD_STATE_WAIT_CLOSE;

			return TRUE;
		}
	}
	return FALSE;
}

BOOL opt_macro_cmd_d2_cache_prog(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	if (OPT_MACRO_CMD_STATE_INIT == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_CACHE_NEXT;
	}

	if (OPT_MACRO_CMD_STATE_RUN_CACHE_NEXT == job->macro_state) {
			opt_dccm_log_debug(0x1B1B1B1B);//cache prog
		if (opt_mt_group_d2_prog_data_in(job, &que_mgr->job_handle[que_mgr->head_job], TRUE, TRUE, FALSE)) {
			job->macro_state = OPT_MACRO_CMD_STATE_RUN_POST_CACHE;
		}
	}

	if (OPT_MACRO_CMD_STATE_RUN_POST_CACHE == job->macro_state) {
		if (opt_mt_group_d2_prog_chk_status(&que_mgr->job_handle[que_mgr->head_job], FALSE, TRUE)) {
			que_mgr->job_handle[que_mgr->head_job].macro_state = OPT_MACRO_CMD_STATE_DONE;
			job->macro_state = OPT_MACRO_CMD_STATE_WAIT_CLOSE;

			return TRUE;
		}
	}
	return FALSE;
}

BOOL opt_macro_cmd_d2_cache_prog_end(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job)
{
	OPT_CRITICAL_ASSERT(ASSERT_FLOW_ERROR | 0x5, OPT_MACRO_CMD_STATE_INIT == job->macro_state);
	if (OPT_MACRO_CMD_STATE_WAIT_CLOSE == job->macro_state) {
		job->macro_state = OPT_MACRO_CMD_STATE_RUN_POST_CACHE;
	}

	if (OPT_MACRO_CMD_STATE_RUN_POST_CACHE == job->macro_state) {
			opt_dccm_log_debug(0x1C1C1C1C);//cache prog end
		if (opt_mt_group_d2_prog_chk_status(job, TRUE, FALSE)) {
			job->macro_state = OPT_MACRO_CMD_STATE_DONE;

			return TRUE;
		}
	}
	return FALSE;
}

static BOOL opt_mt_group_d2_prog_data_in(OPT_JOB_STRUCT_PTR job, OPT_JOB_STRUCT_PTR prev_job, BOOL prev_job_valid, BOOL cache_cmd, BOOL split)
{
	U8 ubCmdIndex;
	U8 ubRSTag;
	U8 ubRSTagCmdCnt[RS_TAG_NUM];
	U8 ubRSTagIndex;
	U8 ubRSTag_PEC;
	U8 ubRSDataPageNum[RS_TAG_NUM] = {0};
	U8 ubNeedCheckPECMap;
	U8 ubPageDepth; // Max Page Number in Logical Plane Bank
	U8 ubMTPResource = job->cmd_cnt;
	U8 ubPollingSequenceSelect;
	U32 ulOPTInfo;
	U32 plane_cnt_org; // Logical Plane Bank Number
	U32 ulPlaneIdx;
	U32 bk_lookup_ptr;
	U16 uwFPU;
	U16 slc;
	BOOL shift_pre_fix;
	U8 ubProgramPlaneBMP = 0;
	U8 ubMap = cpu_comm->ubPreviousProgramPlaneBMP[(gOptStruct.cur_que >> 1)];
	U32 ulRAIDECCPBStatus;
	U8 ubRAIDECCTagID;
	U8 ubResourceToTable = FALSE;
	U8 ubVPBIdx;
	U8 ubProgramCmdRemainNum;
	//Currently, QLC Cell is 12+4, divided into two programs
	U8 ubQLCExtraPageEnd = FALSE;
	U8 ubFPUQLCCellFirstPass = FALSE;	//Coarse : with prefix 0x0D ;  Fine : without prefix
	U16 uwExpectProgramPage = job->uwExpectProgramPage; //Local for re-enter case
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
	U8 ubDMAPageBitmap = 0;
	U8 ubDMAPageIdx = 0;
	U8 ubTheLastPlaneLatchGroupID;
#endif

	opt_dccm_log_debug(0xCAC00000 | (cache_cmd <<8) | (job->cache_prev << 1) | job->cache_next);
	if (job->ubMixPlaneBMP) {
		OPT_CRITICAL_ASSERT(ASSERT_IOR_ERROR | 0x8, cache_cmd);
		plane_cnt_org = job->ubPlaneCnt;
	}
	else {
		plane_cnt_org = bit_vld_2_bit_cnt[job->plane_vld];
		opt_dccm_log_debug(0x33330000 | (job->cmd_cnt << 8) | plane_cnt_org);
	}

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
	ubTheLastPlaneLatchGroupID = select_group_id[job->plane_vld];
#endif

	ubPageDepth = 1;
	if (job->fsp) {
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
		ubPageDepth = FSP_PAGE_CNT + 1; /* Program Confirm */
		ubDMAPageBitmap = ((1 << (job->cmd_cnt / plane_cnt_org)) - 1) << (FSP_PAGE_CNT - (job->cmd_cnt / plane_cnt_org));
#else
		ubPageDepth = job->cmd_cnt / plane_cnt_org;
#endif

#if (MICRON_140S)
		_WRITE_OPT_DCCM_PCA_DEBUG(job->uwExpectProgramPage);
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) // fix
		if (BIT0 & ubPageDepth) {	// TODO: Mix Plane Need Fix ,  Currently stuck QLC Cell settings
			if ((4 == plane_cnt_org ) && (3 == ubPageDepth)) { //Mix Plane uses job ->ubPlaneCnt, so the calculation of plan_cnt_org will not be incorrect
				ubQLCExtraPageEnd = TRUE;
				// Due to E13 opt element not enough workaround will lock current queue (N48 FSP need 16)
				// Prog need to check with total FSP MTP RSC usage at the beginning
				ubMTPResource = (FSP_PAGE_CNT * NAND_MAX_PLANE);
			}
			uwExpectProgramPage -= (FSP_PAGE_CNT - ubPageDepth);
		}
#endif //(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) // fix
		_WRITE_OPT_DCCM_PCA_DEBUG((job->cmd_cnt << 16 ) | ubQLCExtraPageEnd);
		_WRITE_OPT_DCCM_PCA_DEBUG((plane_cnt_org) << 16 | job->plane_vld);
		_WRITE_OPT_DCCM_PCA_DEBUG( FSP_PAGE_CNT << 16 | ubPageDepth);
		_WRITE_OPT_DCCM_PCA_DEBUG(job->rmp_pca);
		_WRITE_OPT_DCCM_PCA_DEBUG(job->page);
		_WRITE_OPT_DCCM_PCA_DEBUG(uwExpectProgramPage);
		OPT_CRITICAL_ASSERT(ASSERT_PCA_ERROR | 0x9, (uwExpectProgramPage != job->page));
#endif
	}
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
	else {
		ubDMAPageBitmap = ((1 << ubPageDepth) - 1);
	}
#endif

	if(job->cmd_cnt % plane_cnt_org){
		opt_dccm_log_debug(0x33330000 | (job->cmd_cnt << 8) | plane_cnt_org);
	}
	OPT_CRITICAL_ASSERT(ASSERT_ARM_FW_FLOW_ERROR | 0x0, (job->cmd_cnt % plane_cnt_org));

	bk_lookup_ptr = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];

	opt_dccm_log_debug(0x22220000 | job->RS_parity);
	if (job->RS_parity) {   // RS parity encode cnt check
		if ((_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubMTPResource)) ||
			(((IOR_EN) ? ((MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) & !GET_PROG_LUXT_QUEUE(gOptStruct.cur_que)) : 0))) {
			return FALSE;
		}
		// E13 Handshake with FW befor use RaidECC Reg
		M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(TRUE);
		M_OPT_WAIT_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(TRUE);
		__ASM_VOLATILE_MEMORY__();
		if (M_OPT_CHECK_COMM_FW_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(TRUE)) {
			M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(FALSE);
			__ASM_VOLATILE_MEMORY__();
			return FALSE;
		}

		for (ubRSTagIndex = 0; ubRSTagIndex < RS_TAG_NUM; ubRSTagIndex++) {
			gOPTRSMgr.ubRSTagList[ubRSTagIndex] = 0xFF;
			ubRSTagCmdCnt[ubRSTagIndex] = 0;
		}

		ubNeedCheckPECMap = 0;
		for (ubCmdIndex = 0; ubCmdIndex < job->cmd_cnt; ubCmdIndex++) {
			ubRSTagIndex = 0;
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = (prev_job_valid ? (prev_job->cmd_cnt + ubCmdIndex) : ubCmdIndex);
			hal_optq_wait_lookup_q_convpage_valid();

			ubRSTag = LOOKUP_RS_TAG_W;

			while (1) {
				if (0xFF == gOPTRSMgr.ubRSTagList[ubRSTagIndex]) {
					gOPTRSMgr.ubRSTagList[ubRSTagIndex] = ubRSTag;
					if (LOOKUP_RS_OTFENC_W) {
						ubRSTagCmdCnt[ubRSTagIndex]++;
					}
				}
				else if (ubRSTag == gOPTRSMgr.ubRSTagList[ubRSTagIndex]) {
					if (LOOKUP_RS_OTFENC_W) {
						ubRSTagCmdCnt[ubRSTagIndex]++;
					}
				}
				else {
					ubRSTagIndex++;
					OPT_CRITICAL_ASSERT(ASSERT_RAID_ERROR | 0x0, RS_TAG_NUM == ubRSTagIndex);
					continue;
				}
				if (LOOKUP_RS_LAST_PAGE_W) {
					ubRSDataPageNum[ubRSTagIndex] = LOOKUP_RS_PAGE_NUMBER_W + 1;  // encode cnt : page number + 1 ( start from RS page 0)
				}
				break;
			}

			if (M_OPT_CHK_LOOKUP_RAIDECC_PROGRAM_PARITY) { // Find real program parity, because force program parity doesn't have encode/ready cnt.
				for (ubRSTagIndex = 0; ubRSTagIndex < RS_TAG_NUM; ubRSTagIndex++) {
					if (ubRSTag == gOPTRSMgr.ubRSTagList[ubRSTagIndex]) {
						ubNeedCheckPECMap |= (BIT0 << ubRSTagIndex);
						break;
					}
				}
			}
		}

		for (ubRSTagIndex = 0; ubRSTagIndex < RS_TAG_NUM; ubRSTagIndex++) {
			if ((BIT0 << ubRSTagIndex) & ubNeedCheckPECMap) {
				U32 ulRAIDECC_TAG_CONFIG;

				M_RAIDECC_SET_TAG_SEL(gOPTRSMgr.ubRSTagList[ubRSTagIndex]);
				__ASM_VOLATILE_MEMORY__();

				M_RAIDECC_SET_TAG_VLD_CTL();
				__ASM_VOLATILE_MEMORY__();

				M_RAIDECC_WAIT_TAG_VLD();
				__ASM_VOLATILE_MEMORY__();

				ulRAIDECC_TAG_CONFIG = M_RAIDECC_GET_TAG_CONFIG();
				__ASM_VOLATILE_MEMORY__();

				ubRSTag_PEC = (ulRAIDECC_TAG_CONFIG & (RAIDECC_TAG_PEC_MASK << RAIDECC_TAG_PEC_SHIFT)) >> RAIDECC_TAG_PEC_SHIFT;
				__ASM_VOLATILE_MEMORY__();
				// if (0 == ubRSDataPageNum[ubRSTagIndex]) Indicates that the program parity may not be the same FSP as the corresponding last rs page, so set RSDataPageNum=0，use PRC Check instead
				if (0 == ubRSDataPageNum[ubRSTagIndex]) {
					U8 ubTempRAIDECCPageNum;
					ubTempRAIDECCPageNum = ((ulRAIDECC_TAG_CONFIG & (RAIDECC_TAG_PRC_MASK << RAIDECC_TAG_PRC_SHIFT)) >> RAIDECC_TAG_PRC_SHIFT);
					__ASM_VOLATILE_MEMORY__();

					if ((INVALID_PRC_0x00 == ubTempRAIDECCPageNum) || (INVALID_PRC_0xFF == ubTempRAIDECCPageNum)) {	// invalid PRC
						ubRSDataPageNum[ubRSTagIndex] = 0xFF;
					}
					else {
						ubRSDataPageNum[ubRSTagIndex] = ubTempRAIDECCPageNum;
					}
				}
				if ((ubRSTag_PEC + ubRSTagCmdCnt[ubRSTagIndex]) != ubRSDataPageNum[ubRSTagIndex]) {
					OPT_CRITICAL_ASSERT(ASSERT_RAID_ERROR | 0x1, (ubRSTag_PEC + ubRSTagCmdCnt[ubRSTagIndex]) > ubRSDataPageNum[ubRSTagIndex]);

					if (OPTQB[R8_OPT_AVL_MTP_SRC] < (ubMTPResource + RESERVED_MT_POOL_CNT)) {
						gOPTRSMgr.ubReleaseReservedMTPoolResource = TRUE;
					}

					OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;
					M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(FALSE);
					__ASM_VOLATILE_MEMORY__();
					return FALSE;
				}
			}
		}
		M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(FALSE);
		__ASM_VOLATILE_MEMORY__();
	}
	else {

		//check tag == parity_tag or not
		if (gOPTRSMgr.ubReleaseReservedMTPoolResource) {
			for (ubCmdIndex = 0; ubCmdIndex < job->cmd_cnt; ubCmdIndex++) {
				ubRSTagIndex = 0;
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = (prev_job_valid ? (prev_job->cmd_cnt + ubCmdIndex) : ubCmdIndex);
				hal_optq_wait_lookup_q_convpage_valid();

				ubRSTag = LOOKUP_RS_TAG_W;

				if (LOOKUP_RS_OTFENC_W) {   // Only encode page should be considered (skip parity)
					while (1) {
						if (ubRSTag == gOPTRSMgr.ubRSTagList[ubRSTagIndex]) {
							gOPTRSMgr.ubMTPoolResourceToProgramData = TRUE;
							gOPTRSMgr.ubReleaseReservedMTPoolResource = FALSE;
						}
						else {
							ubRSTagIndex++;
							if (RS_TAG_NUM == ubRSTagIndex) {
								break;
							}
							continue;
						}
						break;
					}
				}
			}
		}
		// Due to E13 opt element not enough workaround will lock current queue (N48 FSP need 16)
		// The different MTP RSC check rule btw LUX and T pages may cause deadlock when MTP RSC are not enough
		// Because we check total usage at LUX , last 4 T pages blindly use reserve resource
		if (TRUE == GET_PROG_LUXT_QUEUE(gOptStruct.cur_que)) {
		}
		else if (gOPTRSMgr.ubMTPoolResourceToProgramData) {
			gOPTRSMgr.ubMTPoolResourceToProgramData = FALSE;
			if ((_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubMTPResource)) ||
				(IOR_EN ? (MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) : 0)) { // Valley Check Set Feature
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;
				gOPTRSMgr.ubReleaseReservedMTPoolResource = TRUE;
				return FALSE;
			}
		}
		else if (_chk_write_mtq_free_cnt(gOptStruct.cur_que, ubMTPResource)) {

			// set lookup ptr before OPT_CHECK_LOOKUP_PROGRAM_RANDOM_USERDEFINE()
			if (prev_job_valid) {
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = prev_job->cmd_cnt;
			}
			else {
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 0;
			}
			hal_optq_wait_lookup_q_valid();

			if (OPT_CHECK_LOOKUP_PROGRAM_TABLE_USERDEFINE) {
				//E13 and S17 both need to handshake with FW before use FIP TOP to Check VPB.
				M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_FIP(TRUE);
				M_OPT_WAIT_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_FIP(TRUE);
				__ASM_VOLATILE_MEMORY__();

				if (M_OPT_CHECK_COMM_FW_CHECK_RAIDECC_REG_REQUEST_BY_FIP(FALSE)) {
					// Check TAG
					hal_optq_wait_lookup_q_convpage_valid();
					ubRAIDECCTagID = LOOKUP_RS_TAG_W;
					for (ubVPBIdx = 0; ubVPBIdx < FCON_RAIDECC_PB_SEL_NUM; ubVPBIdx++) {
						R8_OPT_FCON[R8_FCON_RAIDECC_PB_SEL] = ubVPBIdx;
						__ASM_VOLATILE_MEMORY__();
						__ASM_DSB();
						ulRAIDECCPBStatus = R32_OPT_FCON[R32_FCON_RAIDECC_PB_STS];
						if (M_OPT_CHECK_FCON_RAIDECC_PB_STS_VALID(ulRAIDECCPBStatus) && (ubRAIDECCTagID == M_OPT_GET_FCON_RAIDECC_PB_TAG_NUM(ulRAIDECCPBStatus))) {
							ubResourceToTable = TRUE;
							break;
						}
					}
				}
				M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_FIP(FALSE);
				__ASM_VOLATILE_MEMORY__();
			}
			/*   OPT_CHECK_LOOKUP_PROGRAM_TABLE_USERDEFINE:  FW in GR, GC program , we will ensure that these RAIDECC programs are not stuck by the RAIDECC IP (the SQ for the next tag will only be sent after the previous parity CQ is received)
			      OPT_CHECK_LOOKUP_PROGRAM_RANDOM_USERDEFINE: open block RAIDECC program (RAIDECC program OTF = FALSE) will not be stuck by RAIDECC IP */
			if ((FALSE == OPT_CHECK_LOOKUP_PROGRAM_TABLE_USERDEFINE) ||
				(ubResourceToTable) || OPT_CHECK_LOOKUP_PROGRAM_RANDOM_USERDEFINE) {
				if ((MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) || (_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, ubMTPResource))) {
					OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;
					return FALSE;
				}
			}
			else {
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;
				return FALSE;
			}
		}
	}

	__ASM_VOLATILE_MEMORY__();
	if (prev_job_valid) {
		OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = prev_job->cmd_cnt;
		slc = prev_job->slc_mode;
	}
	else {
		OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = 0;
		slc = job->slc_mode;
	}
	opt_dccm_log_debug(0xD5000000 | slc);
	__ASM_VOLATILE_MEMORY__();

	//There is latency in form MT
	U8 ubPlaneBankOffset, ubPlaneBankIdx, ubUnitTypeIdx, ubProgTargetTLCUnit = FALSE, ubProgTargetTLCUnitType = OPEN_UNIT_TYPE_NUM;
	U16 uwUnitByVCA, uwPageByVCA;
#if (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
	if (cpu_comm->micron_info.ubLUNForMoreUnitEn)
#else /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
	if (cpu_comm->ubLUNForMoreUnitEn)
#endif
	 {
		ubPlaneBankOffset = gOptStruct.cur_que * NAND_MAX_PLANE;
	}
	else {
		ubPlaneBankOffset = gOptStruct.cur_que * NAND_MAX_PLANE * gubMaxDie + NAND_MAX_PLANE * job->ubDie;
	}
	if (!job->slc_mode) {
		if ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (TRUE == cache_cmd)) {
			OPT_CRITICAL_ASSERT(ASSERT_ARM_FW_FLOW_ERROR | 0x1, TRUE); // N18 QLC Prog doesn't have Cache
		}
		uwUnitByVCA = (gOptStruct.ulPCAMaskFWUnit & job->pca) >> gOptStruct.ubPCAMaskFWUnitShift;
		//If all page 0f each planebank already programmed
#if	(OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
		for (ubUnitTypeIdx = OPEN_GR_UNIT; ubUnitTypeIdx < OPEN_SPOR_UNIT; ubUnitTypeIdx++) {
			if (cpu_comm->micron_info.Flag.ubAll & BIT(ubUnitTypeIdx)) {
				if (uwUnitByVCA == cpu_comm->micron_info.uwNextOpenUnit[ubUnitTypeIdx]) {
					OPT_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0x6, (OPEN_GR_UNIT == ubUnitTypeIdx) && (0 != gubUnreachLastPagePlaneNum[ubUnitTypeIdx])); //If haven't finished writing, request to switch UNIT
					cpu_comm->micron_info.uwOpenUnit[ubUnitTypeIdx] = cpu_comm->micron_info.uwNextOpenUnit[ubUnitTypeIdx];
					cpu_comm->micron_info.uwNextOpenUnit[ubUnitTypeIdx] = INVALID_UNIT;
					cpu_comm->micron_info.Flag.ubAll &= ~ BIT(ubUnitTypeIdx);
					gubUnreachLastPagePlaneNum[ubUnitTypeIdx] = gubTotalPlaneBank;
					//Reset LastProgramPlaneIndex
					for (ubPlaneBankIdx = 0; ubPlaneBankIdx < gubTotalPlaneBank; ubPlaneBankIdx++) {
						guwLastProgramPageInPlaneBank[ubUnitTypeIdx][ubPlaneBankIdx] = 0;
					}
				}
			}
			if (cpu_comm->micron_info.uwOpenUnit[ubUnitTypeIdx] == uwUnitByVCA) {
				ubProgTargetTLCUnit = TRUE;
				ubProgTargetTLCUnitType = ubUnitTypeIdx;
				break;
			}
		}
#endif /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
	}
	hal_optq_wait_lookup_q_valid();
	ubFPUQLCCellFirstPass = OPT_GET_LOOKUP_IS_QLC_CELL_1ST_PASS;

	U8 ubProgPageIdx;
	U8 ubMixPlaneShift = 0; // Which planes have been issued (80-10), the starting point of the CMD sequence for this Round Program is the Physical Plane Bank
	U8 ubTotalPlaneCnt = job->cmd_cnt;
	U8 ubMixPlaneBMP = (job->ubMixPlaneBMP | BIT(plane_cnt_org - 1)); //The last bit needs to be raised by oneself
	U8 ubOrigOPTQueuePtr = OPTQB[OPTQB_LOOKUP_PTR_UPDATE];
	U8 ubFirstOP = TRUE;

	//Lam Debug
	opt_dccm_log_debug(0xD1000000 | (ubTotalPlaneCnt << 16) | (ubPageDepth << 8) | (plane_cnt_org));//0xD10C0304
	opt_dccm_log_debug(0xD2000000 | ubMixPlaneBMP);
	do { //How many planes do we need to do all of them
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
		ubDMAPageIdx = 0;
#endif
		for (ubProgPageIdx = 0; ubProgPageIdx < ubPageDepth; ubProgPageIdx++) { // UbPageDepth mains how many pages each physical plane bank has
		#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOrigOPTQueuePtr + (ubDMAPageIdx * plane_cnt_org) + ubMixPlaneShift; //Move opt ptr
#else
			OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = ubOrigOPTQueuePtr + (ubProgPageIdx * plane_cnt_org) + ubMixPlaneShift; //Move opt ptr
#endif
			job->ubPlaneP2LMap = 0;
			ubProgramPlaneBMP = 0;
			shift_pre_fix = ((((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (MicronFlashID4 == IM_140S_ID4)) || (OPT_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC) || (OPT_CATEGORY_FLASH == FLASH_HYNIX_V7_QLC) || (OPT_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC)) &&
					(FALSE == ubFPUQLCCellFirstPass) && (FALSE == slc)) ? TRUE : FALSE;		//Only Valid when 140s use auto exist mode, TLC/QLC no need prefix 0x3C
			for (ulPlaneIdx = ubMixPlaneShift; ulPlaneIdx < plane_cnt_org; ulPlaneIdx++) { // Plan_cnt_org indicates how many physical plane banks on the same Flash page
				hal_optq_wait_lookup_q_valid();
				//Lam Debug
				opt_dccm_log_debug(0xD3D3D3D3);
				opt_dccm_log_debug((ubProgPageIdx << 24) | (ulPlaneIdx << 16) | (OPT_LOOKUP_PLANE_ADDR << 8) | OPTQB[OPTQB_LOOKUP_PTR_UPDATE]);
				if (((MicronFlashID4 == IM_N28A_ID4)) && OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD) {
					opt_mt_group_valley_check_cmd(job); // Call for Disable Pre-read eXtra or Upper/Lower
				}
				OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_LOOKUP |
					OPTMTW_FORMMT_ATTR_SEL_TMP |
					OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
					OPTMTW_FORMMT_FSA0_FROM_LOOKUP |
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
					((ubDMAPageBitmap & BIT(ubProgPageIdx)) ? OPTMTW_FORMMT_DMA_MT_TRIG : OPTMTW_FORMMT_CMD_MT_TRIG);
#else
					OPTMTW_FORMMT_DMA_MT_TRIG;
#endif

				//There is latency in form MT
				if (ubProgTargetTLCUnit) {
					//If all page 0f each planebank already programmed
					uwPageByVCA = ((OPTQL[OPTQ_PROC_Q_LOOKUP_PCA]) >> COP0_PAGE_START_POINT(0)) & BITMSK(COP0_PAGE_LENS(0), 0);
					ubPlaneBankIdx = ubPlaneBankOffset + (NAND_MAX_PLANE - (plane_cnt_org - ulPlaneIdx));
#if	(OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON)
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) // N18 is two pass prog, pca maybe small than last
#if (N18_PROGRAM_ORDER_CHECK_EN)
					U32 ulProgramDebugPCA;
					ulProgramDebugPCA = guwLastProgramPageInPlaneBank[ubProgTargetTLCUnitType][ubPlaneBankIdx];
					if (((8 == job->cmd_cnt) && ((ubProgPageIdx + 2) == ubPageDepth)) || (12 == job->cmd_cnt) || (4 == job->cmd_cnt)) {
						// First Plane in Second Pass or First Pass or TLC or SLC
						OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0xA, (((ulProgramDebugPCA + 1) != uwPageByVCA) && (0 != uwPageByVCA)));
					}
					else {
						if (8 == job->cmd_cnt) { // second pass
							if ((ubProgPageIdx + 1) == ubPageDepth) {
								if (ulProgramDebugPCA < 212) { // Gap 70 to 93
									OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0xB, ((uwPageByVCA + ((ulProgramDebugPCA - 120) >> 2) + 70) != ulProgramDebugPCA));
								}
								else if (ulProgramDebugPCA > 3048) { // Gap 93 to 71
									OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0xC, ((uwPageByVCA + (93 - (ulProgramDebugPCA - 3048))) != ulProgramDebugPCA));
								}
								else if ((uwPageByVCA + 93) != ulProgramDebugPCA) { // Gap 93
									OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0xD, (0 != uwPageByVCA));
								}
							}
							else {
								OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0xE, TRUE);
							}
						}
						else {
							OPT_CRITICAL_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0xF, TRUE);
						}
					}
#endif /* N18_PROGRAM_ORDER_CHECK_EN */
					if (uwPageByVCA > (guwLastProgramPageInPlaneBank[ubProgTargetTLCUnitType][ubPlaneBankIdx])) {
						guwLastProgramPageInPlaneBank[ubProgTargetTLCUnitType][ubPlaneBankIdx] = uwPageByVCA;
						if ((cpu_comm->micron_info.uwD3PageSize - 1) == uwPageByVCA) {
							gubUnreachLastPagePlaneNum[ubProgTargetTLCUnitType]--;	 //Every PlaneBank only hit once!!
							if (0 == gubUnreachLastPagePlaneNum[ubProgTargetTLCUnitType]) {
								cpu_comm->micron_info.uwOpenUnit[ubProgTargetTLCUnitType] = INVALID_UNIT;
							}
						}
					}
#else /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */
					if (uwPageByVCA == (guwLastProgramPageInPlaneBank[ubProgTargetTLCUnitType][ubPlaneBankIdx] + 1)) {
						guwLastProgramPageInPlaneBank[ubProgTargetTLCUnitType][ubPlaneBankIdx] = uwPageByVCA;
						if ((cpu_comm->micron_info.uwD3PageSize - 1) == uwPageByVCA) {
							gubUnreachLastPagePlaneNum[ubProgTargetTLCUnitType]--;   //Every PlaneBank only hit once!!
							if (0 == gubUnreachLastPagePlaneNum[ubProgTargetTLCUnitType]) {
								cpu_comm->micron_info.uwOpenUnit[ubProgTargetTLCUnitType] = INVALID_UNIT;
							}
						}
					}
					else {
						OPT_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0x7, (0 != uwPageByVCA));
					}
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */
#endif /* (OPT_CATEGORY_CUSTOMER == CUSTOMER_MICRON) */
				}

				job->ubPlaneP2LMap |= (((OPTQL[OPTQ_PROC_Q_LOOKUP_PCA] >> COP0_PLANE_START_POINT(COP0_PCA_RULE_0)) & gubPlaneMask) << (OPT_LOOKUP_PLANE_ADDR << 1));

				hal_optmt_wait_form_mt();

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
				if(ubDMAPageBitmap & BIT(ubProgPageIdx))
#endif
				{
					OPTQW[R16_OPT_MARK_CMD_PTR] = OPTQW[OPTQW_LOOKUP_CMD_PTR];     // Manually mark, provide patch cmd judgment when cache program fails

					while (OPTQL[R32_OPT_MARK_CMD_PTR] & MARK_BSY);
				}

				if (mtq->dw0.bits.force_w_fail || OPT_CHK_LOOKUP_GEN_FAIL) {
					job->auto_gen_fail = TRUE;
				}

				if (ubFirstOP) {
					M_OPT_SET_MT_TEMPLATE_FIRST_OP();
					ubFirstOP = FALSE;
				}


				U8 ubIsLastProgPage = ((ubProgPageIdx + 1) == ubPageDepth);
				if (ubMixPlaneBMP & BIT(ulPlaneIdx)) {
					/*
					 * last plane -> 80-15 or 80-10 or 81-10
					 */
					//U8 ubIsLastProgPage = ((ubProgPageIdx + 1) == ubPageDepth);

					if (cache_cmd) {
						// 80-15 
						if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {							
							//uwFPU = M_OPT_GET_FPU_PROGRAM_80_15_GC(slc);
							if(slc){ 
								/*
								 *	 cache single plane: A2 80 15, A2 80 15, A2 80 15, A2 80 10
								 *		   multi  plane: A2 80 11, A2 81 11, A2 81 11, A2 81 15
							
								 */
								//uwFPU = (plane_cnt_org == 1) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_15)) : // 81 15
								//							(FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_10));	//81 10
								if(plane_cnt_org == 1){
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
								}else{
#if ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_15_gc); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_15_gc); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC )//zerio bics6 qlc add
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_15_gc); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_15_gc); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_slc_80_program_15_gc); //80 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#else
#error "no 81 15?"
#endif
								}									
							}else{
								uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc);
							}

						}
						else {							
								//uwFPU = M_OPT_GET_FPU_PROGRAM_80_15(slc); //Gary change
								if(slc){ 
									/*
									 *   cache single plane: A2 80 15, A2 80 15, A2 80 15, A2 80 10
									 *   	   multi  plane: A2 80 11, A2 81 11, A2 81 11, A2 81 15

									 */
									//uwFPU = (plane_cnt_org == 1) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_15)) : // 81 15
									//							(FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_10));	//81 10
									if(plane_cnt_org == 1){
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15); //80 15
										opt_dccm_log_debug(0xD4008015);
									}else{
#if ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_15); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_15); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC )//zerio bics6 qlc add
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_15); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_15); //81 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_slc_80_program_15); //80 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
										uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc); //80 15
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15); //80 15
#else
#error "no 81 15?"
#endif
										opt_dccm_log_debug(0xD4008115);
									}									
								}else{
									uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10);
									opt_dccm_log_debug(0xD4008010);
								}							
						}
					}
					else {
						// 80-10
						if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {							
							//uwFPU = M_OPT_GET_FPU_PROGRAM_80_10_GC(slc);
							if (slc) {
								/*
								 *	 normal single plane: A2 80 10 
								 *			multi	plane: A2 80 11, 81 11, 81 11, 81..10 (last plane in Multi plane case)
								 */
								 if ( plane_cnt_org == 1){
									 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog_gc); //80 10
								 }
								 else {
									 if((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) {//1st plane afer MixPlane
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog_gc);
										 opt_dccm_log_debug(0xD4008010);
									 }
									 else{
#if ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_10_gc); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
								   		 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_10_gc); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC )//zerio bics6 qlc add
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_10_gc); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_10_gc); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog_gc); //80 10
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_slc_80_program_10_gc); //80 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog_gc); //80 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Samsung_slc_81_program_10_gc); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog_gc); //80 10
#else
#error "no 81 10?"
#endif
										 opt_dccm_log_debug(0xD4008110);
									 }
								 }
							}
							else {
								/*
								 *	 normal single plane: 01 80 22 / 02 80 22 / 03 80 23
								 *			multi  plane: 01 80 11,  01 81 11, 01 81 11, 01 81 22
								 *						  02 80 11,  02 81 11, 02 81 11, 02 81 22
								 *						  03 80 11,  03 81 11, 03 81 11, 03 81 22
								 *						  04 80 11,  04 81 11, 04 81 11, 04 81 23  (last plane in Multi plane case)
								 */
								if ( plane_cnt_org == 1) {	//singe plane
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
									if (ubIsLastProgPage) {
										/* Program Confirm */
										mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
										uwFPU = gwFPU_xlc_1P_prog_gc[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
									}
									else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
										/* LSB/CSB Program dummy */
										mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
										uwFPU = gwFPU_xlc_1P_prog_gc[4][OPT_LOOKUP_PLANE_ADDR];
									}
									else {
										/* Program DMA */
										uwFPU = gwFPU_xlc_1P_prog_gc[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
									}
#else
									uwFPU = gwFPU_xlc_1P_prog_gc[ubProgPageIdx];
#endif
								}
								else {
								 	if((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))){//disconnecting the Mix plane, need to restart and issue 80
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
										if (ubIsLastProgPage) {
											/* Program Confirm */
											mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
											uwFPU = gwFPU_xlc_1P_prog_gc[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
										}
										else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
											/* LSB/CSB Program dummy */
											mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
											uwFPU = gwFPU_xlc_1P_prog_gc[4][OPT_LOOKUP_PLANE_ADDR];
										}
										else {
											/* Program DMA */
											uwFPU = gwFPU_xlc_1P_prog_gc[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
										}
#else
								 		uwFPU = gwFPU_xlc_1P_prog_gc[ubProgPageIdx];
#endif
									 	if(ubProgPageIdx < 2){
									 		opt_dccm_log_debug(0xD400801A);
									 	}
									 	else{
									 		opt_dccm_log_debug(0xD4008010);
									 	}
								 	}
								 	else{
#if ( (NAND_MAX_PLANE == 4) || (NAND_MAX_PLANE == 2) )
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)//Samsung v7 mst add--Reip
											if (ubIsLastProgPage) {
												/* Program Confirm */
												mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
												uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][ubTheLastPlaneLatchGroupID];
											}
											else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
												/* LSB/CSB Program dummy */
												mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
												uwFPU = gwFPU_xlc_mP_prog_gc[4][ubTheLastPlaneLatchGroupID];
											}
											else {
												/* Program DMA */
												uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][ubTheLastPlaneLatchGroupID];
											}
#else
									 		uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_LAST_PLANE];
#endif
#else
#error "no 81 10?"
#endif								
										//Lam Debug
										if(ubProgPageIdx < 2){
											opt_dccm_log_debug(0xD400801A);
										}
										else{
											opt_dccm_log_debug(0xD4008010);
										}
								 	}
								}
							}
						}
						else {							
								//uwFPU = M_OPT_GET_FPU_PROGRAM_80_10(slc); //Gary change
								if (slc) {
									/*
									 *   normal single plane: A2 80 10 
									 *   		multi	plane: A2 80 11, 81..10 (last plane in Multi plane case)
									 */
									 if ( plane_cnt_org == 1){
										 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog); //80 10
									 }
									 else {
										 if((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) {
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog);
											 opt_dccm_log_debug(0xD4008010);
										 }
										 else{
#if ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_10); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC)
								   			 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_10); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC )//zerio bics6 qlc add
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_10); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC)//zerio BICS8 Add
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_SanDisk_slc_81_program_10); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog);
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_EMS_slc_80_program_10); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog);
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Samsung_slc_81_program_10); //81 10
#elif ( CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
											 uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog);
#else
#error "no 81 10?"
#endif	
											 opt_dccm_log_debug(0xD4008110);
										 }
									 }
								}
								else {
									/*
									 *	 normal single plane: 01 80 1A / 02 80 1A / 03 80 10
									 *			multi  plane: 01 80 11,  01 80 1A  
									 *						  02 80 11,  02 80 1A  
									 *						  03 80 11,  03 80 10 (last plane in Multi plane case)
									 */
									if ( plane_cnt_org == 1) {  //singe plane
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
										if (ubIsLastProgPage) {
											/* Program Confirm */
											mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
											uwFPU = gwFPU_xlc_1P_prog[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
										}
										else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
											/* LSB/CSB Program dummy */
											mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
											uwFPU = gwFPU_xlc_1P_prog[4][OPT_LOOKUP_PLANE_ADDR];
										}
										else {
											/* Program DMA */
											uwFPU = gwFPU_xlc_1P_prog[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
										}
#else
										uwFPU = gwFPU_xlc_1P_prog[ubProgPageIdx];
#endif
									 	//Lam Debug
									 	if(ubProgPageIdx < 2){
									 		opt_dccm_log_debug(0xD400801A);
									 	}
									 	else{
									 		opt_dccm_log_debug(0xD4008010);
									 	}
									}
									else {
									 	if((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))){//disconnecting the Mix plane, need to restart and issue 80
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
											if (ubIsLastProgPage) {
												/* Program Confirm */
												mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
												uwFPU = gwFPU_xlc_1P_prog[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
											}
											else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
												/* LSB/CSB Program dummy */
												mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
												uwFPU = gwFPU_xlc_1P_prog[4][OPT_LOOKUP_PLANE_ADDR];
											}
											else {
												/* Program DMA */
												uwFPU = gwFPU_xlc_1P_prog[ubProgPageIdx][OPT_LOOKUP_PLANE_ADDR];
											}
#else
									 		uwFPU = gwFPU_xlc_1P_prog[ubProgPageIdx];
#endif
										 	if(ubProgPageIdx < 2){
										 		opt_dccm_log_debug(0xD400801A);
										 	}
										 	else{
										 		opt_dccm_log_debug(0xD4008010);
										 	}
									 	}
									 	else{
#if ( (NAND_MAX_PLANE == 4) || (NAND_MAX_PLANE == 2) )
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)//Samsung v7 mst add--Reip
											if (ubIsLastProgPage) {
												/* Program Confirm */
												mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
												uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][ubTheLastPlaneLatchGroupID];
											}
											else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
												/* LSB/CSB Program dummy */
												mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
												uwFPU = gwFPU_xlc_mP_prog[4][ubTheLastPlaneLatchGroupID];
											}
											else {
												/* Program DMA */
												uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][ubTheLastPlaneLatchGroupID];
											}
#else
									 		uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_LAST_PLANE];
#endif
#else
#error "NAND_MAX_PLANE"
#endif
									 	//Lam Debug
									 	if(ubProgPageIdx < 2){
									 		opt_dccm_log_debug(0xD400801A);
									 	}
									 	else{
									 		opt_dccm_log_debug(0xD4008010);
									 	}
									 	}
									}
								}					
						}
					}
					if (ubIsLastProgPage) {
						ubFirstOP = TRUE;
						if (PFA_EN) {
							// d2_normal_prog: the program DMA directly return CQ, release parameter ram, pol fail, d2_prog_chk_status only pop opt, without formMT
							// d2_cache_prog_start: the program DMA return CQ, but not release parameter ram, not pol fail, hese actions will only be performed in the next d2_cache_prog or d2_cache_prog_end
							// d2 cache_prog: the program DMA return the own CQ, and assist the previous to release parameter ram, pol fail
							// Normal Program N28 QLC When enabling Preread, it is necessary to send another Enable Preread after the Program page form is completed, so do not release the parameter
							// N28 Support Normal Program on QLC , In order to make CQ return to FW after DMA done, we use the pol dummy polling sequence, which is completed as soon as one polling is reached, just like when DMA done returns to CQ, check the status function to POLL FAIL
							if ((FALSE == ubQLCExtraPageEnd) && ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) &&
									((OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD && (!OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD)) ||
										((FALSE == job->slc_mode) && (FALSE == (GET_PREREAD_DISABLE_STATE_QUEUE(gOptStruct.cur_que))))))) {
								mtd->dw0_dat.bits.par_rls = FALSE;
							}
							else {
								if ( (FALSE == prev_job_valid) && (TRUE == cache_cmd) ) {   // d2_cache_prog_start
									mtd->dw0_dat.bits.par_rls = 0;
								}
								else {
									mtd->dw0_dat.bits.par_rls = 1;	// d2_normal_prog or d2_cache_prog
									if (TRUE == cache_cmd) {        // d2 cache_prog
										mtd->dw3_dat.bits.fw_rls_sel = 1;   // release mtd->dw3_dat.bits.fw_rls_idx
										mtd->dw3_dat.bits.fw_rls_idx = prev_job->uwParam_RAM_Index;
									}
									//else  // d2_normal_program
								}
								//mtd->dw1_dat.bits.nor_cq_rsp = X; //Determine whether to return CQ based on the content of cmd in
							}
						}
						else {
							mtd->dw1_dat.bits.nor_cq_rsp = 0;
							mtd->dw0_dat.bits.par_rls = 0;	 // this cmd iin will be used for gen chk status mt
						}
						mtd->dw2_mt_cfg1.bits.mtp_gro_pri_def = 1;  // After cmd is downloaded, CE busy's MT will be given high priority
					}
					else {
						if (OPT_CHK_LOOKUP_PROGRAM_PARITY_USERDEFINE) {
							// avoid deadlock, mt_prio_lck can't be set
							opt_dccm_log_debug(0xA0A0A0A0);
						}
						else {
							mtd->dw2_mt_cfg1.bits.mt_hard_lock = 1;
							opt_dccm_log_debug(0xB0B0B0B0);
						}

						mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_NOT_RD; //Wr format.
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
						if ((0 == (ubDMAPageBitmap & BIT(ubProgPageIdx ))) || (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx + 1)))) {
							//dummy page & last DMA page, can not release, keep for confirm pages
							mtd->dw0_dat.bits.par_rls = 0;
							mtd->dw1_dat.bits.nor_cq_rsp = 0;
						}
						else
#endif
						{
							mtd->dw0_dat.bits.par_rls = 1;
						}
					}

					job->uwParam_RAM_Index = mtd->dw1_dat.bits.pram_idx;

					// TODO: only 10 need auto poll

					mtq->dw0.bits.pfa_int_en = 0;

					if ((ubIsLastProgPage) && (FALSE == ubQLCExtraPageEnd)) {    //Program Last Page in this job , to confirm that it is already a T page, QLC Cell represents the second group's end prograom

						CLEAR_PROG_LUXT_QUEUE(gOptStruct.cur_que);		// Clear Prog LUXT State when last page (Page T)
						if (cache_cmd) {
							if (TRUE == prev_job_valid) {   // d2_cache_prog
								ubPollingSequenceSelect = (PFA_EN) ? M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_40_PREV_02(M_OPT_GET_MTD_DIE_NUM()) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM()); // 0xC0;
								mtq->dw0.bits.pfa_int_en = PFA_EN;
								if (prev_job->auto_gen_fail) { // If FSP has a page that requires gen fail then gen fail
									mtq->dw0.bits.force_w_fail = TRUE;
								}
							}
							else {  // d2_cache_prog_start
								ubPollingSequenceSelect = M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM()); //0xC0;
							}
							M_OPT_SET_MT_TEMPLATE_ALLOW_SWITCH();
						}
						else {	// d2_normal_prog
#if (( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)\
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)\
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC))	//Reip Porting 3D-V7 QLC Add
							ubPollingSequenceSelect = ((PFA_EN) ? (M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM())) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));//0xE0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)//zerio bics change
							ubPollingSequenceSelect = ((PFA_EN) ? ((FALSE == job->slc_mode) ? M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM()) : M_FPU_GET_POLL_SELECT_SLC_READ_STATUS_70_BUSY_20_CURR_04(M_OPT_GET_MTD_DIE_NUM())) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
							if(cpu_comm->btPolling_waitrdy){//zerio BICS8 Add
								ubPollingSequenceSelect = POL_SEQ_FPU_ENTRY_READ_STATUS_78_BUSY_40;
							}
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
							ubPollingSequenceSelect = ((PFA_EN) ? M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM()) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));
#else
							ubPollingSequenceSelect = ((PFA_EN) ? (M_FPU_GET_POLL_SELECT_READ_STATUS_70_BUSY_20_CURR_01(M_OPT_GET_MTD_DIE_NUM())) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()));//0xE0;POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_80_00
#endif
							mtq->dw0.bits.pfa_int_en = PFA_EN;

							if (PFA_EN) {
								mtq->dw3.bits.allow_switch = 1;
							}

							if (job->auto_gen_fail) { // If FSP has a page that requires gen fail then gen fail
								mtq->dw0.bits.force_w_fail = TRUE;
							}
						}
						ubMixPlaneShift = ulPlaneIdx + 1;
					}
					else {  // Lower/eXtra page (after 80h - 15h)
						if (ubIsLastProgPage) {	//represents the first group's end program , at this time pull the flag
							SET_PROG_LUXT_QUEUE(gOptStruct.cur_que);
							mtd->dw2_mt_cfg1.bits.mt_hard_lock = 1;
						}
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)//zerio n48r add||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC
						ubPollingSequenceSelect = (PFA_EN) ? POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_INTERMEDIATE_MULTIPLANE_PROGRAM : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM());
#else
						ubPollingSequenceSelect = (PFA_EN) ? POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_C0_INTERMEDIATE_MULTIPLANE_PROGRAM : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM());//0xC0;
#endif

						mtq->dw0.bits.pfa_int_en = PFA_EN;
					}
				}
				else {
					// 80-11
					mtq->dw0.bits.pfa_int_en = PFA_EN;
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)//zerio n48r add||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC
					ubPollingSequenceSelect = (PFA_EN) ? POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_INTERMEDIATE_MULTIPLANE_PROGRAM : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM());
#else
					ubPollingSequenceSelect = (PFA_EN) ? POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_C0_INTERMEDIATE_MULTIPLANE_PROGRAM : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM());
#endif
					if (M_OPT_CHK_NEED_COP0_BACKUP_P4K_WORKAROUND_BY_MT_TEMPLATE()) {						
							//uwFPU = M_OPT_GET_FPU_PROGRAM_80_11_GC(slc);						
							if (slc) {
#if ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
								// 81..11
								//uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_11);
								uwFPU = ((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11_gc)): //1st plane or 1st plane after Mixplane
										FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_11_gc); //other plane
#elif ( OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v7/v8 mst add--Reip
								// 81..11
								//uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_11);
								uwFPU = ((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11_gc)): //1st plane or 1st plane after Mixplane
										FPU_OFFSET(gFpuEntryList.fpu_entry_Samsung_slc_81_program_11_gc); //other plane
#elif (NAND_MAX_PLANE == 2)
								uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11_gc); //BICS5 Only 2 Plane, does not exist other case
#elif (NAND_MAX_PLANE == 4)
								uwFPU = ((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11_gc)): //1st plane or 1st plane after Mixplane
									FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11_gc); //other plane
#else
#error "NAND_MAX_PLANE"
#endif
								if((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) {//1st plane after MixPlane
									opt_dccm_log_debug(0xD4A28011);
								}
								else{
									opt_dccm_log_debug(0xD4008111);
								}
							}
							else {
#if ( NAND_MAX_PLANE == 4)
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)//Samsung v7 mst add--Reip
								if (0 == ulPlaneIdx){
									if (ubIsLastProgPage) {
										/* Program Confirm */
										mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
										uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
									}
									else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
										/* LSB/CSB Program dummy */
										mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
										uwFPU = gwFPU_xlc_mP_prog_gc[4][TLC_PROG_FIRST_PLANE];
									}
									else {
										/* Program DMA */
										uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
									}
								}
								else {//ulPlaneIdx = 1/2/3
									if((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx)){//disconnecting the Mix plane, need to restart and issue 80
										if (ubIsLastProgPage) {
											/* Program Confirm */
											mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
											uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
										}
										else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
											/* LSB/CSB Program dummy */
											mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
											uwFPU = gwFPU_xlc_mP_prog_gc[4][TLC_PROG_FIRST_PLANE];
										}
										else {
											/* Program DMA */
											uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
										}
									}
									else {
										if (ubIsLastProgPage) {
											/* Program Confirm */
											mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
											uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_OTHER_PLANE];
										}
										else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
											/* LSB/CSB Program dummy */
											mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
											uwFPU = gwFPU_xlc_mP_prog_gc[4][TLC_PROG_OTHER_PLANE];
										}
										else {
											/* Program DMA */
											uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_OTHER_PLANE];
										}
									}
								}
#else
								if ( 0 == ulPlaneIdx){
									uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
								}
								else {//ulPlaneIdx = 1/2/3
									if((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx)){//disconnecting the Mix plane, need to restart and issue 80
										uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
										opt_dccm_log_debug(0xD4008011);
									}
									else{
										uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_OTHER_PLANE];
										opt_dccm_log_debug(0xD4008111);
									}
								}
#endif
#elif ( NAND_MAX_PLANE == 2)
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
								if (ubIsLastProgPage) {
									/* Program Confirm */
									mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
									uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
								}
								else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
									/* MLC Program dummy */
									mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
									uwFPU = gwFPU_xlc_mP_prog_gc[4][TLC_PROG_FIRST_PLANE];
								}
								else {
									/* Program DMA */
									uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
								}
#else
								uwFPU = gwFPU_xlc_mP_prog_gc[ubProgPageIdx][TLC_PROG_FIRST_PLANE]; //BICS5 Only 2 Plane, does not exist other case
#endif
								opt_dccm_log_debug(0xD4008011);
#else
#error "NAND_MAX_PLANE"
#endif
							}	

					}
					else {						
							/*								 
							 *	 normal multi plane: A2 80 11, 81..10 (last plane in Multi plane case)
							 *	 cache multi plane:  A2 80 11, 81 15
							 */

							//uwFPU = M_OPT_GET_FPU_PROGRAM_80_11(slc); // gary change
							if (slc) {
#if ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || ( CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
								// 81..11
								//uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_11);
								uwFPU = ((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11)): //1st plane or 1st plane after Mixplane
										FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_11); //other plane
#elif ( OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || OPT_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v7/v8 mst add--Reip
								// 81..11
								//uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_Hynix_slc_81_program_11);
								uwFPU = ((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11)): //1st plane or 1st plane after Mixplane
										FPU_OFFSET(gFpuEntryList.fpu_entry_Samsung_slc_81_program_11); //other plane
#elif ( NAND_MAX_PLANE == 2)
								uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11); //BICS5 Only 2 Plane, does not exist other case
#elif ( NAND_MAX_PLANE == 4)
								uwFPU = ((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11)): //1st plane or 1st plane after Mixplane
									FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11); //other plane
#else
#error "NAND_MAX_PLANE"
#endif
								if((ulPlaneIdx == 0) || ((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx))) {//1st plane after MixPlane
									opt_dccm_log_debug(0xD4A28011);
								}
								else{
									opt_dccm_log_debug(0xD4008111);
								}
							}
							else {
#if ( NAND_MAX_PLANE == 4)
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)//Samsung v7 mst add--Reip
								if (0 == ulPlaneIdx){
									if (ubIsLastProgPage) {
										/* Program Confirm */
										mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
										uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
									}
									else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
										/* LSB/CSB Program dummy */
										mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
										uwFPU = gwFPU_xlc_mP_prog[4][TLC_PROG_FIRST_PLANE];
									}
									else {
										/* Program DMA */
										uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
									}
								}
								else {//ulPlaneIdx = 1/2/3
									if((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx)){//disconnecting the Mix plane, need to restart and issue 80
										if (ubIsLastProgPage) {
											/* Program Confirm */
											mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
											uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
										}
										else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
											/* LSB/CSB Program dummy */
											mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
											uwFPU = gwFPU_xlc_mP_prog[4][TLC_PROG_FIRST_PLANE];
										}
										else {
											/* Program DMA */
											uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
										}
									}
									else {
										if (ubIsLastProgPage) {
											/* Program Confirm */
											mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
											uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_OTHER_PLANE];
										}
										else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
											/* LSB/CSB Program dummy */
											mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
											uwFPU = gwFPU_xlc_mP_prog[4][TLC_PROG_OTHER_PLANE];
										}
										else {
											/* Program DMA */
											uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_OTHER_PLANE];
										}
									}
								}
#else
								if ( 0 == ulPlaneIdx){
									uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
									opt_dccm_log_debug(0xD4008011);
								}
								else {//ulPlaneIdx = 1/2/3
									if((ubMixPlaneBMP << 1) & BIT(ulPlaneIdx)){//disconnecting the Mix plane, need to restart and issue 80
										uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
										opt_dccm_log_debug(0xD4008011);
									}
									else{
										uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_OTHER_PLANE];
										opt_dccm_log_debug(0xD4008111);
									}
								}
#endif
#elif ( NAND_MAX_PLANE == 2)
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
								if (ubIsLastProgPage) {
									/* Program Confirm */
									mtq->dw10_iFSA0 = opt_samsung_confirm_fsa_transform(mtq->dw10_iFSA0);
									uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
								}
								else if (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) {
									/* MLC Program dummy */
									mtq->dw10_iFSA0 = OPT_LOOKUP_PLANE_ADDR << COP0_PLANE_START_POINT(0);
									uwFPU = gwFPU_xlc_mP_prog[4][TLC_PROG_FIRST_PLANE];
								}
								else {
									/* Program DMA */
									uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE];
								}
#else
								uwFPU = gwFPU_xlc_mP_prog[ubProgPageIdx][TLC_PROG_FIRST_PLANE]; //BICS5 Only 2 Plane, does not exist other case
#endif
								opt_dccm_log_debug(0xD4008011);
#else
#error "NAND_MAX_PLANE"
#endif
							}						
					}

					if (OPT_CHK_LOOKUP_PROGRAM_PARITY_USERDEFINE) {
						// avoid deadlock, mt_prio_lck can't be set
					}
					else {
						mtd->dw2_mt_cfg1.bits.mt_hard_lock = 1;
					}

					mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_NOT_RD; //Wr format.
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
					if ((!ubIsLastProgPage) && ((0 == (ubDMAPageBitmap & BIT(ubProgPageIdx))) || (0 == (ubDMAPageBitmap & BIT(ubProgPageIdx + 1))))) {
                        //last DMA page, can not release, keep for confirm pages
                        mtd->dw0_dat.bits.par_rls = 0;
                        mtd->dw1_dat.bits.nor_cq_rsp = 0;
					}
                    else
#endif
                    {
                    	mtd->dw0_dat.bits.par_rls = 1;
                    }
				}

				if (split || shift_pre_fix) {
					uwFPU += (2 + FPU_NTODT_ITEM_SHIFT_5_BYTE);
				}

				if (FIP_BYPASS_WDMA_EN && OPT_CHECK_BYPASS_WDMA_USERDEFINE) {
					uwFPU = M_FIP_FPU_BYPASS_WDMA;
					M_OPT_SET_OPTCM_WDMA_MSK_FLH_IO(TRUE);
				}

				M_SET_OPTCM_FPU_POL_SEQ(uwFPU, ubPollingSequenceSelect);
				M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);

				//mtq->dw0.bits.busy = 0;

				if (cache_cmd) {
					ulOPTInfo = (prev_job->ubRMP_bypass) ? OPT_INFO_RMP_BYPASS : OPT_INFO_RMP_USE;
					if (prev_job->auto_gen_fail) {
						ulOPTInfo |= OPT_INFO_GEN_FAIL;
					}
				}
				else {
					ulOPTInfo = (job->ubRMP_bypass) ? OPT_INFO_RMP_BYPASS : OPT_INFO_RMP_USE;
					if (job->auto_gen_fail) {
						ulOPTInfo |= OPT_INFO_GEN_FAIL;
					}
				}

				if (COP0_MT_RESOURCE_NOT_ENOUGH_WORKAROUND) {
					if (ubFirstOP && (FALSE == prev_job_valid)) {
						M_OPT_SET_MT_TEMPLATE_FIRST_OP();
						ubFirstOP = FALSE;
					}
				}

				ulOPTInfo |= (PFA_EN) ? OPT_INFO_PFA : OPT_INFO_POL_FAIL;

				mtq->dw8_userdefine = (ulOPTInfo | (job->ubPlaneP2LMap << OPT_INFO_PLANE_SHIFT) | OPTQ_PROC_Q_LOOKUP_USRDEF_INFO);

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
				if (ubDMAPageBitmap & BIT(ubProgPageIdx)) {
					mtq->dw0.bits.w_dma = 1;
				}
				else {
					mtq->dw3.bits.erase = 1;
					mtq->dw0.bits.ultra_w_en = 0;
					mtq->dw1.bits.dis_udma = 1;
				}
#else
				mtq->dw0.bits.w_dma = 1;
#endif
				if (job->ubMixPlaneBMP) {
					ubProgramPlaneBMP |= OPT_LOOKUP_PLANE_BIT;
				}
				else {
					ubProgramPlaneBMP = ((prev_job_valid) ?  prev_job->plane_vld : job->plane_vld);
				}
				_SET_PROG_OPT_STATUS_(((prev_job_valid) ? cache_cmd : 0), ubProgramPlaneBMP);  // for patch cmd force gen fail, the first cache cmd do not pull cache bit

				ubMap = (gOptStruct.cur_que & BIT0) ? (ubMap & 0x0F) : (ubMap & 0xF0);	//clear queue bits (high or low 4 bits)
				ubMap |= (ubProgramPlaneBMP << ((gOptStruct.cur_que & BIT0) << 2));
				cpu_comm->ubPreviousProgramPlaneBMP[gOptStruct.cur_que >> 1] = ubMap;	//each queue use 4 bits;

				__ASM_VOLATILE_MEMORY__();

				//N48R QLC Coarse
				//first Pass => first plane with prefix
				//second Pass => first plane with no prefix
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS5_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS8_3D_TLC))	//Reip Porting 3D-V7 QLC Add//zerio BICS8 Add//zerio bics6 qlc add// || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICS6_3D_QLC)
				if (slc) {
#if (OPT_CATEGORY_FLASH == FLASH_HYNIX_V8_TLC)
					shift_pre_fix = FALSE;
#else
					shift_pre_fix = TRUE;
#endif
				}
				else{
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems add--karl
					shift_pre_fix = TRUE;
#endif
				}
#else
				shift_pre_fix = TRUE;
#endif

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
				if (ubDMAPageBitmap & BIT(ubProgPageIdx))
#endif
				{
					ubTotalPlaneCnt--;
				}

				if (OPT_CHECK_LOOKUP_PROGRAM_RANDOM_USERDEFINE) {
					// for program P4K backup flow
					mtq->dw9.bits.buf_mode = TRUE;
					mtd->dw1_dat.bits.seq_wr = FALSE;
				}
				__ASM_VOLATILE_MEMORY__();
				_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
				hal_optmt_trigger();
				//Lam Debug
				opt_dccm_log_debug(0xDDDDDDDD);
				cpu_comm->uoTotalNandWrite++;

				__ASM_VOLATILE_MEMORY__();
				if (((MicronFlashID4 == IM_N28A_ID4)) && OPT_CHECK_LOOKUP_ENABLE_PREREAD_CMD && (!OPT_CHECK_LOOKUP_DISABLE_PREREAD_CMD)) {
					opt_mt_group_valley_check_cmd(job); // Call for Enable Pre-read
				}
				OPTQB[OPTQB_LOOKUP_PTR_UPDATE]++;
				if (ubMixPlaneBMP & BIT(ulPlaneIdx)) {
					if(slc){
						shift_pre_fix = FALSE;//disconnecting MixPlane needs to resend A2
					}
					break;
				}
			}
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
			if ((ubDMAPageBitmap & BIT(ubProgPageIdx)) && (ubDMAPageBitmap & BIT(ubProgPageIdx + 1))) {
				/*
				* only DMA L and M page need update to next lookup ptr,
				* dummy page and 1st DMA page use same lookup ptrand
				* last DMA page and confirm page use
				*/
				ubDMAPageIdx ++;
			}
#endif
		}
	} while (ubTotalPlaneCnt);

	OPTQB[OPTQB_LOOKUP_PTR_UPDATE] = bk_lookup_ptr;

	if (PFA_EN) {
		if (!((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (FALSE == job->slc_mode))) {
			if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
				hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
				hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
			}
		}
	}

	// Due to E13 opt element not enough workaround will lock current queue (N48 FSP need 16)
	// The different MTP RSC check rule btw here and check status may cause deadlock when MTP RSC are not enough
	// Pop to 0 to avoid go into check status
	ubProgramCmdRemainNum = ((TRUE == GET_PROG_LUXT_QUEUE(gOptStruct.cur_que)) ? 0 : 1);
	while (job->cmd_cnt > ubProgramCmdRemainNum) { // fix OPTC size only 12, can't cache QLC 2 plane program
		// pop all cmd except last one
		--job->cmd_cnt;
		hal_optq_q_pop();
	}
	return TRUE;
}


static BOOL opt_mt_group_d2_prog_chk_status(OPT_JOB_STRUCT_PTR job, BOOL wait_true_ready, BOOL check_previous)
{
	U8 cmd_cnt;
	U8 i;
	U8 free_cnt;
	U32 ulOPTInfo;
	U8 ubPollingSequenceSelect;
	U16 uwFPU;
	U8 ubMap = cpu_comm->ubPreviousProgramPlaneBMP[(gOptStruct.cur_que >> 1)];

	cmd_cnt = job->cmd_cnt;

	if (cmd_cnt) {
		if (wait_true_ready) {
			i = 0;
			free_cnt = MT_2_MT_TRIG;   // 2 MT: 1 atuo poll + 1 check status
		}
		else {
			if (PFA_EN) {
				i = 2;
				free_cnt  = 0;
			}
			else {
				i = 1;
				free_cnt = MT_1_MT_TRIG;   // 1 MT: check status
			}
		}

		if (_chk_other_cmd_mtq_free_cnt(gOptStruct.cur_que, free_cnt)) {
			if ((MT_TRIGGER_CNT_EMPTY != OPTQB[OPTQB_PROC_Q_MTQ_TRIG_CNT]) || (_chk_reserved_mtq_free_cnt(gOptStruct.cur_que, free_cnt))) {
				return FALSE;
			}
		}

		for (; i < 2; i++) {
			hal_optq_wait_curq_valid();
			OPTQW[OPTMTW_FORMMT] = OPTMTW_FORMMT_FROM_HEAD  |
				OPTMTW_FORMMT_ATTR_SEL_TMP  |
				OPTMTW_FORMMT_COPY_ONE_PLANE_FSA |
				OPTMTW_FORMMT_FSA0_FROM_HEAD |
				OPTMTW_FORMMT_CMD_MT_TRIG;

			hal_optmt_wait_form_mt();

			//---------- MT TABLE ----------//
			ubPollingSequenceSelect = ((0 == i) ? M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM()) : M_FPU_GET_POLL_SELECT_READ_STATUS_BUSY_40(M_OPT_GET_MTD_DIE_NUM()));
			if (0 == i) {
				// MT0 : NOP fpu + auto_poll
				uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_addr_gen_die);
				mtq->dw0.bits.pfa_int_en = 0;
				mtd->dw1_dat.bits.nor_cq_rsp = 0;
			}
			else {
				// MT1 : check status fpu
				mtq->dw0.bits.busy = 0;

				if (job->auto_gen_fail) { // If FSP has a page that requires gen fail then gen fail, so it must intentionally pol read bit to create STA error
					uwFPU = M_FPU_GET_READ_STATUS_BUSY_20(M_OPT_GET_MTD_DIE_NUM());
				}
				else {
					if (check_previous) {
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
						uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_78_70_PREV_MODE_02);
#else
						uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_70_02_PREV_TLC_MODE);
#endif
					}
					else {
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
						uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_78_70_CURR_MODE_01);
#else
						uwFPU = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_70_01_CURR_TLC_MODE);
#endif
					}
				}

				ulOPTInfo = (job->ubRMP_bypass) ? OPT_INFO_RMP_BYPASS : OPT_INFO_RMP_USE;

				ulOPTInfo |= OPT_INFO_POL_FAIL;

				if (job->auto_gen_fail) {
					ulOPTInfo |= OPT_INFO_GEN_FAIL;
				}

				mtq->dw13_iFSA2 = ulOPTInfo | (job->ubPlaneP2LMap << OPT_INFO_PLANE_SHIFT) | OPT_INFO_POL_FAIL_MARK;

				mtq->dw3.bits.erase = 1;

				mtq->dw3.bits.allow_switch = 1;
				if (hal_optq_check_ARM_stall_MTpool_req(gOptStruct.cur_que)) {
					hal_optq_Andes_stop_formMT(gOptStruct.cur_que);	// Due to HW bug, MTP_stall is used to replace MT trigger data
					gubClearARMStallReq = TRUE;
				}


				mtd->dw0_dat.bits.par_rls = 1;
				if (PFA_EN) {
					mtd->dw1_dat.bits.nor_cq_rsp = 0;	// all Normal CQ return after the 80h DMA 1xh MT
				}
				// else mtd->dw1_dat.bits.nor_cq_rsp = X;    // Use the default value from cmd in to determine

			}

			// To avoid when ultra W/R is turned on,cause this CQ is completed before the previous ultraDMA
			mtq->dw0.bits.ultra_w_en = 0;
			mtq->dw1.bits.dis_udma = 1;

			//---------- TRIGGER DATA ----------//

			mtd->dw0_dat.bits.cq_atr_b1_cq_format = CQ_ATR_B1_CQ_FORMAT_NOT_RD; //Wr format.
			M_SET_OPTCMB_TRIG_DATA_PCA_SEL(PCA_SEL_iFSA0_ENA);

			__ASM_VOLATILE_MEMORY__();

			_SET_PROG_OPT_STATUS_(((check_previous) ? (1) : (0)), (job->plane_vld));

			ubMap = (gOptStruct.cur_que & BIT0) ? (ubMap & 0x0F) : (ubMap & 0xF0);	//clear queue bits (high or low 4 bits)
			ubMap |= (((U8)(job->plane_vld)) << ((gOptStruct.cur_que & BIT0) << 2));
			cpu_comm->ubPreviousProgramPlaneBMP[gOptStruct.cur_que >> 1] = ubMap;	//each queue use 4 bits;

			__ASM_VOLATILE_MEMORY__();

			mtd->dw2_mt_cfg1.bits.frc_emp = FALSE;	// RS force save the program parity will pull force empty, this cmd may form two MTs (program + pol true ready/fail), the secondpol true ready/fail don't neet to pull force empty
			M_SET_OPTCM_FPU_POL_SEQ(uwFPU, ubPollingSequenceSelect);
			__ASM_VOLATILE_MEMORY__();

			_WRITE_OPT_DCCM_PCA_DEBUG(mtq->dw10_iFSA0);
			hal_optmt_trigger();

			if (gubClearARMStallReq) {
				hal_optq_clear_ARM_stall_MTpool_req(gOptStruct.cur_que);
				gubClearARMStallReq = FALSE;
			}

		}
		hal_optq_q_pop();

	}

	return TRUE;
}

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
U32 opt_samsung_confirm_fsa_transform(U32 ulFSA)
{
	U32 ulPage = (gOptStruct.ulPCAMaskPage[0] & ulFSA) >> COP0_PAGE_START_POINT(0);
	U32 ulConfirmPage;
#if(OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V6_TLC)
	if (ulPage < 0x10) {
		ulConfirmPage = ulPage >> 1;
	}
	else if (ulPage < 0xBF8) {
		ulConfirmPage = (ulPage - 0x10) / 3 + 8;
	}
	else {
		ulConfirmPage = ((ulPage - 0xBF8) >> 1) + 0x400;
	}
#elif(OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V6P_TLC)
	if (ulPage < 0x20) {
		ulConfirmPage = ulPage >> 1;
	}
	else if (ulPage < 0xC68) {
		ulConfirmPage = (ulPage - 0x20) / 3 + 16;
	}
	else {
		ulConfirmPage = ((ulPage - 0xC68) >> 1) + 0x428;
	}
#elif (OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V7_TLC)//Samsung v7 mst add--Reip
	if (ulPage < 0xC) {
		ulConfirmPage = ulPage >> 1;
	}
	else if (ulPage < 0x59A) {
		ulConfirmPage = (ulPage - 0xC) / 3 + 6;
	}
	else if (ulPage < 0x5A6) {
		ulConfirmPage = ((ulPage - 0x59A) >> 1) + 480;
	}
	else if (ulPage < 0x5B2) {
		ulConfirmPage = (ulPage - 0x5A6) + 486;
	}
	else if (ulPage < 0x5BE) {
		ulConfirmPage = ((ulPage - 0x5B2) >> 1) + 498;
	}
	else if (ulPage < 0xC48) {
		ulConfirmPage = (ulPage - 0x5BE) / 3 + 504;
	}
	else if (ulPage < 0xC54) {
		ulConfirmPage = ((ulPage - 0xC48) >> 1) + 1062;
	}
	else {
		ulConfirmPage = (ulPage - 0xC54) + 1068;
	}
#elif (OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V8_TLC)//Samsung v8 mst add--Reip
	if (ulPage < 0xC) {
		ulConfirmPage = ulPage >> 1;
	}
	else if (ulPage < 0x1080) {
		ulConfirmPage = (ulPage - 0xC) / 3 + 6;
	}
	else if (ulPage < 0x108C) {
		ulConfirmPage = ((ulPage - 0x1080) >> 1) + 1410;
	}
	else {
		ulConfirmPage = (ulPage - 0x108C) + 1416;
	}
#elif (OPT_CATEGORY_FLASH==FLASH_SAMSUNG_V5_TLC)//Samsung v5 mst add--Jeffrey
	if (ulPage < 0x8) {
		ulConfirmPage = ulPage >> 1;
	}
	else if (ulPage < 0x440) {
		ulConfirmPage = (ulPage - 0x8) / 3 + 4;
	}
	else if (ulPage < 0x448) {
		ulConfirmPage = ((ulPage - 0x440) >> 1) + 0x16C;
	}
	else {
		ulConfirmPage = (ulPage - 0x448) + 0x170;
	}
#endif
	return ((ulFSA & (~gOptStruct.ulPCAMaskPage[0])) | (ulConfirmPage << COP0_PAGE_START_POINT(0)));
}
#endif

