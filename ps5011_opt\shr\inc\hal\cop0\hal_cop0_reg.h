/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  hal_cop0_reg.h                                                        */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _HAL_COP0_REG_H_
#define _HAL_COP0_REG_H_

#include "misc/types.h"

#ifdef WIN32
#define COP0_REG_BASE           ((U32)gSysResStruct.cop0_reg)
#define COP0_BBMS_SRAM0         ((U32)gSysResStruct.cop0_ram->cop0_bbms_sram0)
#define COP0_V2P_SRAM           ((U32)gSysResStruct.cop0_ram->cop0_v2p_sram)
#else
#define COP0_REG_BASE           (0x0B100000)
#define COP0_BBMS_SRAM0         (0x0B1A0000)
#define COP0_V2P_SRAM           (0x0B196000)
#endif

#define r8_COP0                 ((volatile U8  *)COP0_REG_BASE)
#define r16_COP0                ((volatile U16 *)COP0_REG_BASE)
#define r32_COP0                ((volatile U32 *)COP0_REG_BASE)
#define r64_COP0                ((volatile U64 *)COP0_REG_BASE)

// TODO: magic number
#define COP0_OPT_COMM_INFO_BASE          (0x0b191900 - 0x40)    // the dccm of optimizer, for banking usage.
#define CHK_COP0_OPT_BBRM_RDY            (0xF)

typedef union cregl_link_s {
	U32 all;
	struct {
		U32 wrr_ena        : 1; /* Enable weighted round-robin function of CLINK */
		U32 reserve        : 7;
		U32 high_wrr_weight: 4; /* Weight of high priority linked list for weighted round-robin. */
		U32 mid_wrr_weight : 4; /* Weight of middle priority linked list for weighted round-robin. */
		U32 reserve1       : 16;
	} fields;
} creg_link_t;

typedef union cregl_attr_s {
	U32 all;
	struct {
		U32 auto_gen_fail_R         : 1;   /* Bit[0]: auto gen fail for R */
		U32 auto_gen_fail_W         : 1;   /* Bit[1]: auto gen fail for W */
		U32 sta_no_stop             : 1;   /* Bit[2]: sta_no_stop */
		U32 conv_bps                : 1;   /* Bit[3]: conv_bps */
		U32 rs_one_parity_en  : 1;   /* Bit[4]: rs_one_parity_en */
		U32 rs_parity_tlc_prog: 1;   /* Bit[5]: rs_parity_tlc_prog */
		U32 mtq_dl_en               : 1;   /* Bit[6]: MTQ_DL_EN */
		U32 aes_fw_en               : 1;   /* Bit[7]: aes_fw_en */
		U32 cmp_en                  : 1;   /* Bit[8]: cmp_en */
		U32 buf_mode                : 1;   /* Bit[9]: buf_mode */
		U32 zip_e3d_chk_dis         : 1;   /* Bit[10]: ZIP_E3D_CHK_DIS */
		U32 rs_grp_num        : 2;   /* Bit[11~12]: RS_GRP_NUM */
		U32 pto_en                  : 1;   /* Bit[13]: PTO_EN */
		U32 pto_len_sel             : 2;   /* Bit[15:14]: PTO_LEN_SEL */
		U32 es_en             : 1;   /* Bit[16]: RS_OTFENC_EN */
		U32 udma_dis          : 1;   /* Bit[17]: udma_dis */
		U32 udma_w_en         : 1;   /* Bit[18]: udma_w_en */
		U32 reserve           : 13;
	} fields;
} creg_attr_t;

typedef union cregl_clksw_s {
	U32 all;
	struct {
		U32 io_typ     : 1;   /* Bit[0]: io_typ   0:single end(legacy, toggle 1.0, onfi), 1:differential */
		U32 fclk_div_en: 1;   /* Bit[1]: fclk_div_en */
		U32 fclk_div   : 6;   /* Bit[7:2]: fclk_div */
		U32 flh_typ    : 2;   /* Bit[9:8]: flh_typ 0:legacy, 2:toggle, 3:onfi */
		U32 tim_cfg_sel: 1;   /* Bit[10]: tim_cfg_sel */
		U32 reserve    : 21;
	} fields;
} creg_clksw_t;

typedef union creg_cq_msg0_s {
	U32 all;
	struct {
		U32 mt_idx   : 24;
		U32 hw_int   : 8;
	} fields;
} creg_cq_msg0_t;

typedef union creg_cq_msg1_s {
	U32 all;
	struct {
		U32 tar_cpu  : 3; //XXX
		U32 idx_mode : 1; //XXX
		U32 cpu_int  : 1; //XXX
		U32 err_frm  : 4;
		U32 reserved : 23;
	} fields;
} creg_cq_msg1_t;

typedef union cregb_channel_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_channel_rule_t;

typedef union cregb_plane_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_plane_rule_t;

typedef union cregb_lmu_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_lmu_rule_t;

typedef union cregb_bank_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_bank_rule_t;

typedef union cregw_page_rule_s {
	U16 all;
	struct {
		U16 start_point: 5;
		U16 length     : 4;
		U16 reserve    : 7;
	} fields;
} creg_page_rule_t;


typedef union cregw_block_rule_s {
	U16 all;
	struct {
		U16 start_point: 5;
		U16 length     : 4;
		U16 reserve    : 7;
	} fields;
} creg_block_rule_t;

typedef union cregb_die_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_die_rule_t;

typedef union cregb_exdie_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_exdie_rule_t;

typedef union cregb_trim_rule_s {
	U8 all;
	struct {
		U8 start_point: 5;
		U8 length     : 2;
		U8 reserve    : 1;
	} fields;
} creg_trim_rule_t;

/*
 * COP0 regs
 *
 */

//set register offset
///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_MT_RULE        (0x80>>2)    /* MT_RULE is for getting MT Queue information from PCA (address) -> channel & bank */
#define CREGL_VB_RULE        (0x84>>2)    /* VB_RULE is for getting VB(virtual Block) information from PCA (address) -> block & die & ext die*/
#define CREGL_SB_RULE        (0x88>>2)    /* SB_RULE is for getting SB (Super Block) information from PCA (address) -> channel & plane & Bank */
#define CREGL_STALL          (0x100>>2)   /* Stall of each queue in CLINK for error handling. If stall set, CLINK will stop push new element to optimizer command queue. */
#define CREGL_TIMEOUT        (0x108>>2)
#define CREGW_TIMEOUT_VALUE        (0x108>>1)    /* Timeout value of timeout counter, hardware set timeout if timeout counter == timeout */
#define COP0_TIMEOUT_VALUE_MAX    (0x3FF)    /* for the longest value to timeout */
#define CREGW_TIMEOUT_RESOLUTION   (0x10A>>1)    /* If timeout resolution value = n, timeout counter +1 every 2^(10-n) cycle. */
#define COP0_TIEOUT_RESOLUTION_MAX    (0)    /* for the longest value to timeout */

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_LINK        (0x180>>2)


#define CREGL_HIGH01_WEIGHT        (0x184>>2)
#define LINK_HIGH1_WEIGHT_SET(x)         (x<<4)
#define LINK_HIGH0_WEIGHT_SET(x)         (x<<0)

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_CR_LRSC_INIT        (0x188>>2)
#define CREG_CR_LRSC_INIT_BIT        (BIT0)
#define CREGL_ANDES_STANDBY        (0x204>>2)
#define CREG_STANDBY_REQ_BIT        (BIT0)
#define nCREG_WAKEUP_REQ_BIT        (~(CREG_STANDBY_REQ_BIT))
#define CREG_AUTO_STANDBY_EN_BIT        (BIT8)
#define CREG_ANDES_STANDBY_BIT        (BIT16)    /* RO, Andes standby status. 1: Andes CPU is in standby state. */
#define CREGL_Q_ARB_MASK        (0x208>>2)

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_ATTR0        (0x220>>2)

#define CREGL_ATTR1        (0x224>>2)

#define CREGL_ATTR2        (0x228>>2)

#define CREGL_ATTR3        (0x22C>>2)

#define CREGL_ATTR4        (0x230>>2)

#define CREGL_ATTR5        (0x234>>2)

#define CREGL_ATTR6        (0x238>>2)

#define CREGL_ATTR7        (0x23C>>2)

#define CREGL_ATTR8        (0X240>>2)

#define CREGL_ATTR9        (0x244>>2)

#define CREGL_ATTR10       (0x248>>2)

#define CREGL_ATTR11       (0x24C>>2)

#define CREGL_ATTR12       (0x250>>2)

#define CREGL_ATTR13       (0x254>>2)

#define CREGL_ATTR14       (0x258>>2)

#define CREGL_ATTR15       (0x25C>>2)


#define MT_ATTR0    (0)
#define MT_ATTR1    (1)
#define MT_ATTR2    (2)
#define MT_ATTR3    (3)
#define MT_ATTR4    (4)
#define MT_ATTR5    (5)
#define MT_ATTR6    (6)
#define MT_ATTR7    (7)
#define MT_ATTR8    (8)
#define MT_ATTR9    (9)
#define MT_ATTR10   (10)
#define MT_ATTR11   (11)
#define MT_ATTR12   (12)
#define MT_ATTR13   (13)
#define MT_ATTR14   (14)
#define MT_ATTR15   (15)

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_CLKSW_ATTR0        (0x260>>2)

#define CREGL_CLKSW_ATTR1        (0x264>>2)


#define CLKSW_ATTR0    (0)
#define CLKSW_ATTR1    (1)

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_MT_OPTION        (0x270>>2)

#define PFA_INT_EN_BIT    (BIT0)   /* Bit[0]: pfa_int_en */
#define CE_SEL_MODE_BIT   (BIT1)   /* Bit[1]: ce_select_mode */

#define CREGL_USERDEFINE        (0x280>>2)

#define CREG_CE_DECODER_MODE       (0x284>>2)

#define CREG_CE_DECODER_MODE_16CE_PER_CH    (0)
#define CREG_CE_DECODER_MODE_32CE_PER_CH    (BIT1)

#define CREG_CE_DECODER_MODE_EN             (BIT0)
#define CREG_CE_NORMAL_MODE                 (0)

#define CREGL_MTQ_TRIG_FULL_LIMIT   (0x288>>2)
#define CREGL_OPT_Q_EMPTY           (0x290>>2)

#define CREGL_MTQ_TRIG_EMPTY        (0x294>>2)

#define CREGL_MTQ_TRIG_CNT_SUB     (0x298>>2)

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_MTQ_TRIG_CNT         (0x2A0>>2)

#define CREGB_MTQ_TRIG_CNT0        (0x2A0)

#define CREGB_MTQ_TRIG_CNT1        (0x2A1)

#define CREGB_MTQ_TRIG_CNT2        (0x2A2)

#define CREGB_MTQ_TRIG_CNT3        (0x2A3)

#define CREGB_MTQ_TRIG_CNT4        (0x2A4)

#define CREGB_MTQ_TRIG_CNT5        (0x2A5)

#define CREGB_MTQ_TRIG_CNT6        (0x2A6)

#define CREGB_MTQ_TRIG_CNT7        (0x2A7)

#define CREGB_MTQ_TRIG_CNT8        (0x2A8)

#define CREGB_MTQ_TRIG_CNT9        (0x2A9)

#define CREGB_MTQ_TRIG_CNT10        (0x2AA)

#define CREGB_MTQ_TRIG_CNT11        (0x2AB)

#define CREGB_MTQ_TRIG_CNT12        (0x2AC)

#define CREGB_MTQ_TRIG_CNT13        (0x2AD)

#define CREGB_MTQ_TRIG_CNT14        (0x2AE)

#define CREGB_MTQ_TRIG_CNT15        (0x2AF)

#define CREGB_MTQ_TRIG_CNT16        (0x2B0)

#define CREGB_MTQ_TRIG_CNT17        (0x2B1)

#define CREGB_MTQ_TRIG_CNT18        (0x2B2)

#define CREGB_MTQ_TRIG_CNT19        (0x2B3)

#define CREGB_MTQ_TRIG_CNT20        (0x2B4)

#define CREGB_MTQ_TRIG_CNT21        (0x2B5)

#define CREGB_MTQ_TRIG_CNT22        (0x2B6)

#define CREGB_MTQ_TRIG_CNT23        (0x2B7)

#define CREGB_MTQ_TRIG_CNT24        (0x2B8)

#define CREGB_MTQ_TRIG_CNT25        (0x2B9)

#define CREGB_MTQ_TRIG_CNT26        (0x2BA)

#define CREGB_MTQ_TRIG_CNT27        (0x2BB)

#define CREGB_MTQ_TRIG_CNT28        (0x2BC)

#define CREGB_MTQ_TRIG_CNT29        (0x2BD)

#define CREGB_MTQ_TRIG_CNT30        (0x2BE)

#define CREGB_MTQ_TRIG_CNT31        (0x2BF)

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_OPT_STALL_RDY         (0x2C0>>2)

#define CREGL_CR_FCON_MT_CFG_ADDR        (0x300>>2)

#define CREGL_CR_IRAM_BASE        (0x304>>2)

#define CREGL_CMSG_0        (0x308>>2)

#define CREGW_CR_L4K_ZONE_OFS        (0x308>>1)

#define CREGL_CMSG_1        (0x30C>>2)

#define CREGW_CR_MT_ZONE_OFS        (0x30C>>1)

#define CREGW_CR_MT_ZONE_LEN        (0x30E>>1)

#define CREGL_CMSG_2        (0x310>>2)

#define CREGB_CR_CQ_BLOCK_INT_PTN_0        (0x310) //WR//
#define CREGB_CR_CQ_BLOCK_INT_PTN_1        (0x311) //WR//
#define CREGB_CR_CQ_BLOCK_INT_PTN_2        (0x312) //RD//
#define CREGB_CR_CQ_BLOCK_INT_PTN_3        (0x313) //RD//

#define CREGL_CMSG_3        (0x314>>2)

#define CREG_CR_IRAM_INIT_BIT        (BIT0)

#define CREG_CR_CQ_BLOCK_EN_0        (BIT8)  //WR//
#define CREG_CR_CQ_BLOCK_EN_1        (BIT9)  //WR//
#define CREG_CR_CQ_BLOCK_EN_2        (BIT10) //RD//
#define CREG_CR_CQ_BLOCK_EN_3        (BIT11) //RD//

#define CREG_CR_CQ_LINK_PROT_EN      (BIT12)

#define CREGL_CMSG_4        (0x318>>2)

#define CREGB_CR_ERR_MSG_CFG        (0x318)

#define CREGB_CR_TIEOUT_ENABLE        (0x319)

#define CREGB_CR_ERR_INT_FORMAT_CFG        (0x31A)

#define CREGB_CR_IRAM_NOT_RLS_CFG        (0x31B)

#define CREGL_CMSG_5        (0x31C>>2)

#define CREG_SR_IRAM_MT_FULL_BIT        (BIT10)        /* RO, The MT resource is full, no allocation is available. */

#define CREG_SR_IRAM_MT_EMPTY_BIT        (BIT11)        /* RO, The MT resource is empty, no current allocation for MT. */

#define CREG_SR_CQ_TAB_0_EMPTY           (BIT16)        /* RO, The empty condition of each order. */
#define CREG_SR_CQ_TAB_1_EMPTY           (BIT17)        /* RO, The empty condition of each order. */
#define CREG_SR_CQ_TAB_2_EMPTY           (BIT18)        /* RO, The empty condition of each order. */

#define CREG_SR_CQ_ALL_TAB_EMPTY         (BIT19)        /* RO, The empty condition of CQ_TAB. */

#define CREG_SR_CQ_TAB_0_FULL            (BIT24)        /* RO, The full condition of each order. */
#define CREG_SR_CQ_TAB_1_FULL            (BIT25)        /* RO, The full condition of each order. */
#define CREG_SR_CQ_TAB_2_FULL            (BIT26)        /* RO, The full condition of each order. */

#define CREG_SR_CQ_ALL_TAB_FULL          (BIT27)        /* RO, The full condition of CQ_TAB. */

#define CREGB_CR_ERR_CPU        (0x31C)        /* [2:0] CREG_CR_ERR_CPU - The ERR_CPU which receive the ERR_CPU_INT. */

#define ERR_CQ_MSG_TO_CPU0    (BIT1)
#define ERR_CQ_MSG_TO_CPU1    (BIT2)
#define ERR_CQ_MSG_TO_COP0    (BIT0)

#define CREGL_CMSG_6        (0x320>>2)

#define CREGB_SR_CQ_TAB_CPU0_LEVEL        (0x320)    /* RO */
#define CREGB_SR_CQ_TAB_CPU1_LEVEL        (0x321)    /* RO */
#define CREGB_SR_CQ_TAB_COP1_LEVEL        (0x322)    /* RO */
#define CREGB_SR_CQ_TAB_ALL_LEVEL         (0x323)    /* RO */

#define CREGL_CMSG_7        (0x324>>2)

#define CREGB_SR_IRAM_MT_RLS_PTR        (0x324)        /* The current release MT ptr in MT_ZONE */

#define CREGB_SR_IRAM_MT_ALCT_PTR        (0x326)        /* The next allocation MT ptr in MT_ZONE */

#define CREGL_CMSG_8        (0x328>>2)

#define CURRENT_CQ_MASK        (0x3FFFFF)  /* [21:18] ERR_FRM, [17] CPU_INT, [16:9] INT, [8:0] MT_IDX */

#define CURRENT_CQ_VLD_BIT      (BIT22)     /* [22]: 1'b1: current is processing, 1'b0: current is finished */

/* [23]1'h1: CQ_TAB_LEVEL shows level,
 *     1'h0: CQ_TAB_LEVEL shows ptr.
 *     (cq_tab_wptr=ptr, cq_tab rptr=(ptr>level) ? (ptr-level) : (level-ptr).
 */
#define CREG_CR_CQ_LEVEL_PTR_SEL_BIT         (BIT23)

#define CURRENT_CQ_SEL_fields        (BIT24|BIT25)    /* [25:24]: 2'h0: select CPU0, 2'h1: select CPU1, 2'h2: select COP1 */

#define CURR_CQ_SEL_CPU0        (0)
#define CURR_CQ_SEL_CPU1        (BIT24)
#define CURR_CQ_SEL_COP1        (BIT25)

#define CQ_FROM_CQ        (BIT26)    /* [26] 1'h0: select cq from cmsg_cq, 1'h1: select cq from cmsg_cq_link */
#define CQ_FROM_CQ_LINK        (0)

#define CURRENT_CQ_SRC_BIT27        (BIT27)    /* [27] if (CURRENT_CQ_SRC==0) send tie out else head overlap */
#define CURRENT_CQ_SRC_BIT28        (BIT28)    /* [28] if (CURRENT_CQ_SRC==0) mt rls else link overlap */
#define CURRENT_CQ_SRC_BIT29        (BIT29)    /* [29] if (CURRENT_CQ_SRC==0) vp ram upd else tail_overlap */
#define CURRENT_CQ_SRC_BIT30        (BIT30)    /* [30] if (CURRENT_CQ_SRC==0) mtq_trig_cnt else fifo_q_sel[0] */
#define CURRENT_CQ_SRC_BIT31        (BIT31)    /* [31] if (CURRENT_CQ_SRC==0) pseudo_cq else fifo_q_sel[1] */

#define CREGL_CMSG_9        (0x32C>>2)

#define CREGB_CR_CMSG_CQ_XDB_PSEL        (0x32C)

#define CMSG_CQ_XDB_PSEL_CPU0        (0)
#define CMSG_CQ_XDB_PSEL_CPU1        (0x01)
#define CMSG_CQ_XDB_PSEL_COP1        (0x02)

#define CREGL_CQ_MSG0        (0x330>>2)
#define CQ_MSG_SET_BY_CPU                   (BIT31)
#define CQ_MSG_INT_OFFSET                   (9)
#define CQ_MSG_INT(x)                       ((x & 0xFF) << CQ_MSG_INT_OFFSET)
#define CQ_MSG_PFA_BIT                      (BIT7)
#define CQ_MSG_MT_STOP_BIT                  (BIT6)
#define CQ_MSG_STA_BIT                      (BIT5)
#define CQ_MSG_EOT_BIT                      (BIT4) //EOT : Over error threshold occur.
#define CQ_MSG_ABORT_DONE_BIT               (BIT3)
#define CQ_MSG_DMA_DONE_BIT                 (BIT2)
#define CQ_MSG_PARTIAL_DONE_BIT             (BIT1)
#define CQ_MSG_ALL_DONE_BIT                 (BIT0)
#define CQ_MSG_MTIDX(x)                     (x & 0x1FF)
#define CQ_MSG_ERR_OFFSET                   (22)
#define CQ_MSG_ERR_FRM(x)                   ((x & 0xF) << CQ_MSG_ERR_OFFSET)

#define CREGL_CQ_MSG1        (0x334>>2)

#define CREGL_CR_BBMP_EN        (0x338>>2)
#define CREG_CR_BBMP_EN_BIT        (BIT0)        /* The BBMP function enable */


#define CREGB_CHANNEL_RULE      (0x380)
#define CREGB_PLANE_RULE        (0x381)
#define CREGB_LMU_RULE          (0x382)
#define CREGB_BANK_RULE         (0x383)
#define CREGW_PAGE_RULE         (0x384>>1)
#define CREGW_BLOCK_RULE        (0x386>>1)
#define CREGB_DIE_RULE          (0x388)
#define CREGB_EXDIE_RULE        (0x389)
#define CREGB_TRIM_RULE         (0x38A)
#define CREGB_ENTRY_RULE        (0x38B)

#define CREGB_ENTRY_4K_PAGE      (0)
#define CREGB_ENTRY_8K_PAGE      (1)
#define CREGB_ENTRY_16K_PAGE     (2)

#define CREG_HALF_8K_EN         (0x38C)
#define CREGL_BBMP_BASE_ADDR    (0x390>>2)
#define CREGL_BBMP_LENGS        (0x394>>2)
#define CREGW_BBMP_LENGS        (0x394>>1)
#define CREGL_VBRMP_BASE        (0x398>>2)
#define CREGL_VBRMP_EN          (0x39C>>2)
#define CREG_VBRMP_EN_BIT           (BIT0)
#define CREG_VBRMP_DIS              (~BIT0)
#define CREGL_BBMP_DIR_SEARCH    (0x3A0>>2)
#define CREG_BBMP_DIR_SEARCH_BIT    (BIT0)

#define CREGL_CSCH_DB_PTR        (0x3A8>>2)

#define CREGB_CSCH_DB_PTR    (0x3A8) //for Debug

#define CREGL_CSCH_COPT_IF_0_L        (0x400>>2) //read only.

#define CSCH_COPT_IF_0_EMPTY        (BIT20)
#define CSCH_COPT_IF_0_FULL         (BIT21)
#define CSCH_COPT_IF_0_FIFO_REST    (BIT22|BIT23|BIT24|BIT25|BIT26)
#define CSCH_COPT_IF_0_FIFO_LEVEL   (BIT31|BIT30|BIT29|BIT28|BIT27)

#define CREGL_CSCH_COPT_IF_0_H        (0x404) //read only.

#define CSCH_COPT_IF_0_CUR_PTR      (BIT3|BIT2|BIT1|BIT0)
#define CSCH_COPT_IF_0_CSCH_DB_PTR  (BIT7|BIT6|BIT5|BIT4)

#define CREGL_CSCH_COPT_IF_1_L        (0x408>>2) //read only.

#define CREGL_CSCH_COPT_IF_1_H        (0x40C>>2) //read only.

#define CREGL_CSCH_COPT_IF_2_L        (0x410>>2) //read only.

#define CREGL_CSCH_COPT_IF_2_H        (0x414>>2) //read only.

#define CREGL_CSCH_COPT_IF_3_L        (0x418>>2) //read only.

#define CREGL_CSCH_COPT_IF_3_H        (0x41C>>2) //read only.

#define CREGL_CSCH_COPT_IF_4_L        (0x420>>2) //read only.

#define CREGL_CSCH_COPT_IF_4_H        (0x424>>2) //read only.

#define CREGL_OPT_DB_PORT_L           (0x428>>2) //read only.

#define CREGL_OPT_DB_PORT_H           (0x42C>>2) //read only.

#define CREGL_CSCH_DB_PORT_L          (0x430>>2) //read only.

#define CREGL_CSCH_DB_PORT_H          (0x434>>2) //read only.

#define CREGL_MSG_DB_PORT             (0x438>>2) //read only.

#define CREGL_CMSG_DB_PORT            (0x43C>>2) //read only.

#define CREGL_AXIM_DB_PORT_L          (0x440>>2) //read only.

#define CREGL_AXIM_DB_PORT_H          (0x444>>2) //read only.

#define CREGL_LRSC_DB_PORT_L          (0x448>>2) //read only.

#define CREGL_LRSC_DB_PORT_H          (0x44C>>2) //read only.

///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_PAR_ERR_STAL        (0x500>>2)

#define CREG_PAR_ERR_STAL_BIT        (BIT0)    /* RW1C */

/*

CREG_PAR_ERR_ST[10]: LINK1 RAM
CREG_PAR_ERR_ST[09]: LINK0 RAM
CREG_PAR_ERR_ST[08]: V2P_RAM
CREG_PAR_ERR_ST[07]:OPTC_RAM
CREG_PAR_ERR_ST[06]:PAR_RAM
CREG_PAR_ERR_ST[05]:CQ_RAM
CREG_PAR_ERR_ST[04]:IRAM_TAB_RAM
CREG_PAR_ERR_ST[03]:VEC_RAM
CREG_PAR_ERR_ST[02]:BBMS_RAM
CREG_PAR_ERR_ST[01]:OPTD_RAM
CREG_PAR_ERR_ST[00]:OPTS_RAM

*/


///////////////////////////////////////////////////////////////////////////////////////////////////////////

#define CREGL_AXI_SLV_ERR         (0x504>>2)

#define CREG_AXI_SLV_ERR_BIT    (BIT0)

#define CREGL_CMEM_LS_DLY              (0x508>>2)  /* 3.2.100   CREG_CMEM_LS_DLY */

#define CREGB_LS_DLY_PD2_0             (0x508)
#define CREGB_LS_DLY_PD2_1             (0x509)


#define CREGL_COP0_STATUS              (0x580>>2)  /* 3.2.101   CREG_COP0_STATUS (Offset: 583h~580h) */

#define COP0_IDLE        (BIT0)
#define CQ_IDLE          (BIT8)
#define LINK_IDLE        (BIT9)
#define OPT_IDLE         (BIT10)
#define CSC_IDLE         (BIT11)
///////////////////////////////////////////////////////////////////////////////////////////////////////////
//BBS map
#define CREG_BBS_MAP0                ((REG64*)(COP0_BBMS_SRAM0))

/*******************************************************************************/

#define CQ_MSG_PFA_BIT           (BIT7)
#define CQ_MSG_MT_STOP_BIT       (BIT6)
#define CQ_MSG_STA_BIT           (BIT5)
#define CQ_MSG_EOT_BIT           (BIT4) //EOT : Over error threshold occur.
#define CQ_MSG_ABORT_DONE_BIT    (BIT3)
#define CQ_MSG_DMA_DONE_BIT      (BIT2)
#define CQ_MSG_PARTIAL_DONE_BIT  (BIT1)
#define CQ_MSG_ALL_DONE_BIT      (BIT0)

#define CQ_MSG_ALWAYS_RECEIVE_NORMAL_TIE_OUT_ENABLE    (CQ_MSG_PFA_BIT|CQ_MSG_MT_STOP_BIT|CQ_MSG_STA_BIT|CQ_MSG_EOT_BIT|CQ_MSG_ABORT_DONE_BIT|CQ_MSG_DMA_DONE_BIT|CQ_MSG_ALL_DONE_BIT) //except partial done.

#define CQ_MSG_DEFAULT_CREG_CR_IRAM_NOT_RLS_CFG        (CQ_MSG_PARTIAL_DONE_BIT|CQ_MSG_EOT_BIT|CQ_MSG_STA_BIT|CQ_MSG_MT_STOP_BIT|CQ_MSG_PFA_BIT)  //0xF2

#define CQ_MSG_DEFAULT_CREG_CR_ERR_INT_FORMAT_CFG      (CQ_MSG_EOT_BIT|CQ_MSG_STA_BIT|CQ_MSG_MT_STOP_BIT|CQ_MSG_PFA_BIT) //0xF0

#define CQ_MSG_DEFAULT_CREG_CR_TIEOUT_ENABLE           (CQ_MSG_ALL_DONE_BIT|CQ_MSG_DMA_DONE_BIT|CQ_MSG_ABORT_DONE_BIT|CQ_MSG_EOT_BIT|CQ_MSG_STA_BIT|CQ_MSG_MT_STOP_BIT|CQ_MSG_PFA_BIT) //0xFD

#define CQ_MSG_DEFAULT_ERR_MSG_CFG                     (CQ_MSG_DEFAULT_CREG_CR_ERR_INT_FORMAT_CFG)  //0xF0


#endif /* _HAL_COP0_REG_H_ */


