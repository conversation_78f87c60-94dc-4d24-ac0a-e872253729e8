/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_test.h                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef OPT_TEST_H_
#define OPT_TEST_H_


#include "opt_global.h"
#include "opt_arch.h"
#include "opt_api.h"

#ifdef _OPT_TEST_C_
#define EXTERN
#else
#define EXTERN extern
#endif


#define DATA_BUFFER_OFS      (0x4A000000)
#define CPU0_DCCM0_Bank0_OFS (0x5C1A8000)

EXTERN void opt_axim_RW_test(void);
EXTERN void opt_bvci_RW_test(void);
EXTERN void opt_uart_burnin(void);
EXTERN void opt_uart_test(void);

#undef EXTERN
#endif /* OPT_TEST_H_ */
