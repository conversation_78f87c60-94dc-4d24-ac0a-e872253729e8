/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_read.h                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_READ_H_
#define _OPT_READ_H_

#include "misc/types.h"
#include "opt_main.h"
#include "opt_arch.h"



#ifdef _OPT_READ_C_
#define EXTERN
#else
#define EXTERN extern
#endif
#if (!NEW_IWL_EN)
U8 gubIWLCrossGroup;
#endif /* (!NEW_IWL_EN) */
//For Micron Read
#define IM_B17_SECTION_1	12
#define IM_B17_SECTION_2	36
#define IM_B17_SECTION_3	60
#define IM_B17_SECTION_4	2220
#define IM_B17_SECTION_5	2268
#define IM_B17_SECTION_6	2292

#define IM_B27_SECTION_1	18
#define IM_B27_SECTION_2	54
#define IM_B27_SECTION_3	90
#define IM_B27_SECTION_4	2412
#define IM_B27_SECTION_5	2466
#define IM_B27_SECTION_6	2502
#define IM_B27_SECTION_7	2538
#define IM_B27_SECTION_8	2574
#define IM_B27_SECTION_9	2592
#define IM_B27_SECTION_10	2628
#define IM_B27_SECTION_11	2646
#define IM_B27_SECTION_12	2682
#define IM_B27_SECTION_13	2736
#define IM_B27_SECTION_14	5058
#define IM_B27_SECTION_15	5112
#define IM_B27_SECTION_16	5148

#define IM_B27B_SECTION_1	12
#define IM_B27B_SECTION_2	36
#define IM_B27B_SECTION_3	60
#define IM_B27B_SECTION_4	1608
#define IM_B27B_SECTION_5	1656
#define IM_B27B_SECTION_6	1692
#define IM_B27B_SECTION_7	1740
#define IM_B27B_SECTION_8	1752
#define IM_B27B_SECTION_9	1776
#define IM_B27B_SECTION_10		1788
#define IM_B27B_SECTION_11		1824
#define IM_B27B_SECTION_12		3372
#define IM_B27B_SECTION_13		3420
#define IM_B27B_SECTION_14		3456


#define IM_N18_SECTION_1	12
#define IM_N18_SECTION_2	120
#define IM_N18_SECTION_3	3048

#define IM_N28_SECTION_1  12                            //     0-11 SLC
#define IM_N28_SECTION_2  24                            //     12-23       SLC Write L                             
#define IM_N28_SECTION_3  132                          //     24-131     TLC Write L U X
#define IM_N28_SECTION_4  2292                        //     132-2291 QLC Write L U X T[diff2]
#define IM_N28_SECTION_5  2340                        //     2292-2339       QLC Write L U X T[diff1]
#define IM_N28_SECTION_6  2412                        //     2340-2411       TLC Write L U X                      (same S_3)
#define IM_N28_SECTION_7  2460                        //     2412-2459       QLC Write L U X T[diff2]         (Same S_4)
#define IM_N28_SECTION_8  2496                        //     2460-2495       TLC Write L U X                               (same S_3)
#define IM_N28_SECTION_9  2544                        //     2496-2543       QLC Write L U X T[diff2]         (Same S_4)
#define IM_N28_SECTION_A 2580                        //     2544-2579       TLC Write L U X                               (same S_3)
#define IM_N28_SECTION_B 4548                        //     2580-4547       QLC Write L U X T[diff2]         (Same S_4)


#define IM_QLC_PAGE_NUM    4
#define IM_TLC_PAGE_NUM    3

#define M_FIP_GET_CURRENT_GROUP_ID()	(R32_OPT_FCON[R32_OPT_FCON_IOR_CFG] & IDX_VAL_MASK)
#if (FIP_SUPPORT_MT_VA)
#define M_OPT_GET_READ_FPU_BIN_OFFSET(SLC, CACHE, CMD_CNT, OFFSET) // HW diff, S17 use VA function to send virtual address
#if (VA_MAPPING_TABLE_EN)
EXTERN volatile U8 *gpubVirtualAddressValue;
#define M_OPT_GET_VIRTUAL_ADDRESS_VALUE(VALUE) (gpubVirtualAddressValue[(VALUE)])
#else /* VA_MAPPING_TABLE_EN */
#define M_OPT_GET_VIRTUAL_ADDRESS_VALUE(VALUE) (VALUE)
#endif /* VA_MAPPING_TABLE_EN */

#define M_OPT_SET_READ_CMD_BIN_VALUE(MTD, MTQ, VALUE)	do {\
															(MTD)->dw3_dat.bits.va_num = 1;\
				(MTQ)->dw4.bits.va0 = M_OPT_GET_VIRTUAL_ADDRESS_VALUE(VALUE);\
														} while(0)
#define M_OPT_GET_IWL_READ_FPU_BIN_OFFSET()		(0)
#define M_OPT_GET_SNAP_READ_FPU_BIN_OFFSET()	(0)
#else /* (FIP_SUPPORT_MT_VA) */
#define M_OPT_GET_READ_FPU_BIN_OFFSET(SLC, CACHE, CMD_CNT, OFFSET)	do {\
																OFFSET = ((SLC) ? 	(guwReadBinOffsetTable[(CACHE)][(((CMD_CNT) > 3) ? 0 : (CMD_CNT))] ) :\
																(guwReadBinOffsetTable[(CACHE)][(((CMD_CNT) > 3 ) ? 0 : (CMD_CNT))] ));\
															} while(0)

#define M_OPT_SET_READ_CMD_BIN_VALUE(MTD, MTQ, VALUE)
#define M_OPT_GET_IWL_READ_FPU_BIN_OFFSET() 	(guwIWLReadBinOffsetTable)
#define M_OPT_GET_SNAP_READ_FPU_BIN_OFFSET()    (guwSnapReadBinOffsetTable)

#endif /* (FIP_SUPPORT_MT_VA) */

enum enumSharePageOrder {
	FIRST_SHARE_PAGE,
	SECOND_SHARE_PAGE,
	THIRD_SHARE_PAGE,
	FOURTH_SHARE_PAGE,
};


EXTERN BOOL opt_macro_cmd_normal_read(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_cache_read_start(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_cache_read(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_cache_read_end(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);

#undef EXTERN
#endif /* _OPT_READ_H_ */
