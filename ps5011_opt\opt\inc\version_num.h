/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  version_num.h                                                         */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef VERSION_NUM_H_
#define VERSION_NUM_H_

#include "fpu.h"

//#define VERSION_MAJOR 0
//#define VERSION_MINOR 1


#define NAND_FACTORY (_SUPPORT_TOSHIBA_ | _SUPPORT_MICRON_<<1)
#define FLASH_TYPE   0//(_TLC_<<2 | _MLC_<<1 | _SLC_)

struct version_list {
	U8 nand_factory;
	U8 flash_type;
	U8 minor;
	U8 mjnor;
	U8 build_day;
	U8 build_month;
	U16 build_year;
	U8 build_min;
	U8 build_hour;
	U16 rsv;
};


#endif /* VERSION_NUM_H_ */
