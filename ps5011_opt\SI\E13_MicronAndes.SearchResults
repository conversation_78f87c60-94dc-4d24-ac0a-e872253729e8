---- MicronFlashID4 Matches (16 in 2 files) ----
Opt_global.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\inc):#define MicronFlashID4   IM_B27B_ID4
Opt_global.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\inc):#if (MicronFlashID4 ==  IM_B16A_ID4)
Opt_global.h (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\inc):#elif (MicronFlashID4 ==  IM_B27B_ID4)
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	if ((MicronFlashID4 == IM_B16A_ID4) || (MicronFlashID4 == IM_B17A_ID4)) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	else if (MicronFlashID4 == IM_B27A_ID4) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	else if (MicronFlashID4 == IM_B27B_ID4) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	else if (MicronFlashID4 == IM_N18A_ID4) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	if (MicronFlashID4 == IM_N18A_ID4) { //ubLXUNumer =>  0 (L), 1 (U), 2 (X), 3 (T)
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	if ((MicronFlashID4 == IM_B16A_ID4) || (MicronFlashID4 == IM_B17A_ID4)) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	else if (MicronFlashID4 == IM_B27A_ID4) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	else if (MicronFlashID4 == IM_B27B_ID4) {	//Current B27B uwY is WORDLINE
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):	else if (MicronFlashID4 == IM_N18A_ID4) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):		if (MicronFlashID4 == IM_N18A_ID4) {
Opt_main.c (d:\dreamyan_chen\documents\ds-5_workspace\e13_fw\andesfw\edam20e3-0a_b27b\ps5011_opt\opt\src):			OPT_ASSERT(ASSERT_WORDLINE_BYPASS_ERROR | 0x4, (MicronFlashID4 == IM_N18A_ID4) && (uwExtraPageIndex > guwLastProgramPageInPlaneBank[ubHitUnitType][gOptStruct.cur_que * gubMaxPlane]));
