/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_test.c                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_TEST_C_
#include"opt_test.h"
#include"opt_main.h"


void opt_bvci_RW_test(void)
{
#if 0
	U32 up_4B, lo_4B;
	U32 addr;

	addr  = 0x04003210;

	up_4B = 0x12345678;
	lo_4B = 0x87654321;

	// clear 4 bytes in 0x04003210
	bvci_write(addr, 0, BIT0 | BIT1 | BIT2 | BIT3);

	// test low 1st byte
	bvci_write(addr, lo_4B, BIT0);
	while (bvci_read(addr    ) != (0x00000021));

	// test low 2nd byte
	bvci_write(addr, 0, BIT0 | BIT1 | BIT2 | BIT3);
	bvci_write(addr, lo_4B, BIT1);
	while (bvci_read(addr    ) != (0x00004300));

	// test low 3rd byte
	bvci_write(addr, 0, BIT0 | BIT1 | BIT2 | BIT3);
	bvci_write(addr, lo_4B, BIT2);
	while (bvci_read(addr    ) != (0x00650000 ));

	// test low 4th byte
	bvci_write(addr, 0, BIT0 | BIT1 | BIT2 | BIT3);
	bvci_write(addr, lo_4B, BIT3);
	while (bvci_read(addr    ) != (0x87000000 ));

	// test 8 byte in 0x04003210 ~ 7
	bvci_write(addr,     0, BIT0 | BIT1 | BIT2 | BIT3);
	bvci_write(addr + 4,     0, BIT0 | BIT1 | BIT2 | BIT3);
	bvci_write(addr, lo_4B, BIT0 | BIT1 | BIT2 | BIT3);
	bvci_write(addr + 4, up_4B, BIT0 | BIT1 | BIT2 | BIT3);

	while (bvci_read(addr    ) != 0x87654321);
	while (bvci_read(addr + 4) != 0x12345678);
#endif
}


void opt_axim_RW_test(void)
{
#if 0
	U64 data, backup;
	U32 addr;

	addr  = DATA_BUFFER_OFS;
	data   = DATA_U64_PATTERN;

	backup = axim_read(addr);             /*backup*/
	axim_write(addr, data, 0xFF);
	if (axim_read(addr) != (DATA_U64_PATTERN)) {
		_DBM(_MSG_gather_, "A:OPT AXI RW Data_Buffer ERROR\n");
		while (1);
	}
	else {
		_DBM(_MSG_gather_, "A:OPT AXI RW Data_Buffer OK\n");
		axim_write(addr, backup, 0xFF);  /*Setting back*/
	}


	addr  = FLASH_IRAM_BASE;

	backup = axim_read(addr);            /*backup*/
	axim_write(addr, data, 0xFF);
	if (axim_read(addr) != (DATA_U64_PATTERN)) {
		_DBM(_MSG_gather_, "A:OPT AXI RW FLH_IRAM ERROR\n");
		while (1);
	}
	else {
		_DBM(_MSG_gather_, "A:OPT AXI RW FLH_IRAM OK\n");
		axim_write(addr, backup, 0xFF);  /*Setting back*/
	}

	/*
	    addr  = CPU0_DCCM0_Bank0_OFS;

	    axim_write(addr    , 0, 0xFF);
	    axim_write(addr    , data, 0xFF);
	    if (axim_read(addr    ) != (DATA_U64_PATTERN)){
	        _DBM(_MSG_gather_,"A:OPT AXI RW CPU0_DCCM0_Bank0 ERROR\n");
	    }
	    else{
	        _DBM(_MSG_gather_,"A:OPT AXI RW CPU0_DCCM0_Bank0 OK\n");
	    }
	*/

#endif
}




void opt_uart_burnin(void)
{
#if 0
	U32 i;
	while (1) {
		i++;
		_DBM2(_MSG_DEMO_, "OPT run uart burnin i:%d(d) i:%x(x)\n", i, i);
	}
#endif
}


void opt_uart_test(void)
{
	//    _DBM2(_MSG_DEMO_,"OPT run uart i:%d(d) i:%x(x)\n", 77, 77);
	;
}


#if SUPPORT_UART
void show_info(void)
{
#if 0
	_DBM2(_MSG_INFO_, "RULE  chn :%d :%d\n", OPT_CREG_CHANNEL_LENS, OPT_CREG_CHANNEL_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  pla :%d :%d\n", OPT_CREG_PLANE_LENS, OPT_CREG_PLANE_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  LMU :%d :%d\n", OPT_CREG_LMU_LENS, OPT_CREG_LMU_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  ban :%d :%d\n", OPT_CREG_BANK_LENS, OPT_CREG_BANK_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  pag :%d :%d\n", OPT_CREG_PAGE_LENS, OPT_CREG_PAGE_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  ede :%d :%d\n", OPT_CREG_EXID_LENS, OPT_CREG_EXID_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  die :%d :%d\n", OPT_CREG_DIE_LENS, OPT_CREG_DIE_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  blk :%d :%d\n", OPT_CREG_BLOCK_LENS, OPT_CREG_BLOCK_START_POINT);
	_DBM2(_MSG_INFO_, "RULE  trm :%d :%d\n", OPT_CREG_TRIM_LENS, OPT_CREG_TRIM_START_POINT);

	//_DBM1(_MSG_INFO_,"mti-pl :%d\n", gOptStruct.ulPCAMaskMultiPlane);
#endif
}
#endif
