/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_debug.h                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef OPT_DEBUG_H_
#define OPT_DEBUG_H_

#include "hal/cop0/hal_cop0_reg.h"
#include "hal/cop0/hal_cop0_types.h"
#include "opt_arch.h"
#include "opt_global.h"

#ifdef OPT_DEBUG_C_
#define EXTERN
#else
#define EXTERN extern
#endif

#define DCCM_DEBUG_RECORD_PCA_INSTEAD_IFSA  (1)

#define DCCM_DEBUG_SWITCH_Q_OP              (0xFF000000)
#define DCCM_DEBUG_BANKING_OP               (0xAA000000)
#define DCCM_DEBUG_STALL_OP                 (0xBB000000)
#define DCCM_DEBUG_TIMEOUT_OP               (0xCC000000)
#define DCCM_DEBUG_LOOKUP_PTR               (0xDD000000)
#define DCCM_DEBUG_1st_OP                   (0x0A000000)
#define DCCM_DEBUG_2nd_OP                   (0x0B000000)
#define DCCM_DEBUG_SET_CMD_OP               (0x0C000000)
#define DCCM_DEBUG_FORM_PLANE_INFO_OP       (0x0D000000)
#define DCCM_DEBUG_Q_POP                    (0x0E000000)
#define DCCM_DEBUG_PARSER_STATE             (0xEE000000)

#define _OR_OP_CODE(v, op)          ((v&0x00FF)|op)
//0xop_w_v
#define _OR_OP_CODE_2(v, w, op)     (((v&0x000F)|((w&0x000F)<<4))|op)

#define SWITCHQ_OP(v)                   _OR_OP_CODE(v, DCCM_DEBUG_SWITCH_Q_OP)
#define BANKING_OP(v)                   _OR_OP_CODE(v, DCCM_DEBUG_BANKING_OP)
#define STALL_OP(v)                     _OR_OP_CODE(v, DCCM_DEBUG_STALL_OP)
#define TIMEOUT_OP(v)                   _OR_OP_CODE(v, DCCM_DEBUG_TIMEOUT_OP)
#define LOOKUP_PTR_OP(v)                _OR_OP_CODE(v, DCCM_DEBUG_LOOKUP_PTR)
#define PARSER_1st_OP(v)                _OR_OP_CODE(v, DCCM_DEBUG_1st_OP)
#define PARSER_2nd_OP(v)                _OR_OP_CODE(v, DCCM_DEBUG_2nd_OP)
#define SET_CMD_OP(v)                   _OR_OP_CODE(v, DCCM_DEBUG_SET_CMD_OP)
#define FORM_PLANE_INFO(vld, cache)     _OR_OP_CODE_2(vld, cache, DCCM_DEBUG_FORM_PLANE_INFO_OP)
#define Q_POP(v)                        _OR_OP_CODE(v, DCCM_DEBUG_Q_POP)
#define PARSER_STATE(v)                 _OR_OP_CODE(v, DCCM_DEBUG_PARSER_STATE)
//#define OPT_D_DEBUG_BASE          (OPT_D_CPBK_BASE + OPT_D_CPBK_LENS)
/*per dubug info need bytes*/
#define OPT_D_DEBUG_PER_LENGTH      (4)
/*Rsv 8 bytes*/
#define OPT_D_DEBUG_RSV_BYTES       (4)
#define OPT_D_DEBUG_LENGTH          ((OPT_DCCM_DEBUG_LENS - OPT_D_DEBUG_RSV_BYTES) / OPT_D_DEBUG_PER_LENGTH)
#define _dccm_debug_get_base(idx)   (OPT_DCCM_DEBUG_BASE + (idx * OPT_D_DEBUG_PER_LENGTH))




#if OPT_SUPPORT_DCCM_DEBUG

#define _WRITE_OPT_DCCM_PATH_DEBUG(log)    opt_dccm_log_debug(log)
#define _WRITE_OPT_DCCM_PCA_DEBUG(log)     opt_dccm_log_debug(log)
#define _WRITE_OPT_DCCM_MT_DEBUG(log)     opt_dccm_log_debug(log)

#if OPT_SUPPORT_DCCM_BEHAVIOR
#define _WRITE_OPT_DCCM_BEHAVIOR(log)      opt_dccm_log_debug(log)
#endif

#if OPT_SUPPORT_DCCM_BANKING_DEBUG
#define _WRITE_OPT_DCCM_BANKING_DEBUG(log)     opt_dccm_log_debug(log)
#else
#define _WRITE_OPT_DCCM_BANKING_DEBUG(log)
#endif

EXTERN void opt_dccm_log_debug(U32 log);

EXTERN volatile U16 g_idx;
EXTERN volatile U32 *dccm_log_value;



#else /* #if OPT_SUPPORT_DCCM_DEBUG */
EXTERN void opt_dccm_log_debug(U32 log);
EXTERN volatile U16 g_idx;
EXTERN volatile U32 *dccm_log_value;
#define _WRITE_OPT_DCCM_DEBUG
#define _WRITE_OPT_DCCM_PATH_DEBUG(log)
#define _WRITE_OPT_DCCM_PCA_DEBUG(log)
#define _WRITE_OPT_DCCM_BANKING_DEBUG(log)
#define _WRITE_OPT_DCCM_MT_DEBUG(log)
#endif /* #if OPT_SUPPORT_DCCM_DEBUG */



#undef EXTERN
#endif /* OPT_DEBUG_H_ */
