/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  types.h                                                               */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _TYPES_H_
#define _TYPES_H_

#include <string.h> // memset(), memcpy(), NULL

#include "conf.h"
#include "misc/macro.h"
#include "misc/def.h"

#ifdef WIN32
// Win api
#include <windows.h>
#include <intrin.h>

#include <stdio.h>
//
#include "dev_ini.h"
#include "win32_vars.h"


#if 1
#include "hw/sys/hw_sys.h"
#include "hw/cpu/hw_cpu.h"
#include "hw/host/nvme/nvme.h"
#endif



#else

#ifdef TRUE
#undef TRUE
#endif

#ifdef FALSE
#undef FALSE
#endif

#define TRUE            (1)
#define FALSE           (0)

#define IDLE            (1)
#define BUSY            (0)

#define RS_TAG_NUM  (4)

typedef unsigned char       BOOL;   //Boolean:          TRUE/FALSE

#endif

typedef unsigned char       U8;     //Unsigned 8 Bits:  0 ~ 255 (0xFF)
typedef unsigned short      U16;    //Unsigned 16 Bits: 0 ~ 65,535 (0xFFFF)
typedef unsigned long       U32;    //Unsigned 32 Bits: 0 ~ 4,294,967,295 (0xFFFF_FFFF)
typedef unsigned long long  U64;    //Unsigned 64 Bits: 0 ~ 18,446,744,0763,709,551,615 (0xFFFF_FFFF_FFFF_FFFF)
typedef signed char         S8;     //Signed 8 Bits:    -127 ~ 128
typedef signed short        S16;    //Signed 16 Bits:   -32,767 ~ 32,768
typedef signed long         S32;    //Signed 32 Bits:   -2,147,483,647 ~ 2,147,483,648
typedef signed long long    S64;    //Signed 64 Bits:   -9,223,372,036,854,775,807 ~ 9,223,372,036,854,775,808

typedef union u64_union U64_UNION, *U64_UNION_PTR;
typedef union u32_union U32_UNION, *U32_UNION_PTR;
typedef union u16_union U16_UNION, *U16_UNION_PTR;

union u64_union {
	U64     u64;
	U32     u32x2[2];
	U16     u16x4[4];
	U8      u8x8[8];
};

union u32_union {
	U32     u32;
	U16     u16x2[2];
	U8      u8x4[4];
};

#pragma pack(2)
union u16_union {
	U16     u16;
	U8      u8x2[2];
};
#pragma pack()

typedef union {
	U32 ulong;
	struct {
		U16 W0: 16;
		U16 W1: 16;
	} uword;
	struct {
		U32 B0: 8;
		U32 B1: 8;
		U32 B2: 8;
		U32 B3: 8;
	} ubyte;
	struct {
		U32 BT0: 1;
		U32 BT1: 1;
		U32 BT2: 1;
		U32 BT3: 1;
		U32 BT4: 1;
		U32 BT5: 1;
		U32 BT6: 1;
		U32 BT7: 1;
		U32 BT8: 1;
		U32 BT9: 1;
		U32 BT10: 1;
		U32 BT11: 1;
		U32 BT12: 1;
		U32 BT13: 1;
		U32 BT14: 1;
		U32 BT15: 1;
		U32 BT16: 1;
		U32 BT17: 1;
		U32 BT18: 1;
		U32 BT19: 1;
		U32 BT20: 1;
		U32 BT21: 1;
		U32 BT22: 1;
		U32 BT23: 1;
		U32 BT24: 1;
		U32 BT25: 1;
		U32 BT26: 1;
		U32 BT27: 1;
		U32 BT28: 1;
		U32 BT29: 1;
		U32 BT30: 1;
		U32 BT31: 1;
	} bt;
} REG_t;


#endif /* _TYPES_H_ */

