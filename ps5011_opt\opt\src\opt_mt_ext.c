/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_mt_ext.c                                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define _OPT_MT_EXT_C_

#include "opt_mt_ext.h"
#include "opt_const.h"


inline void mt_ext_set_fpu_chk_true_ready(void)
{
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_78_70_CURR_MODE_01);
#else
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_70_01_CURR_TLC_MODE);
#endif
}

inline void mt_ext_set_fpu_cache_ready_chk_cur_status(void)
{
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_78_70_CURR_MODE_01);
#else
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_70_01_CURR_TLC_MODE);
#endif
}

inline void mt_ext_set_fpu_cache_ready_chk_pre_status(void)
{
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC||CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_78_70_PREV_MODE_02);
#else
	mtq->dw5.bits.fpu_ptr = FPU_OFFSET(gFpuEntryList.fpu_entry_read_status_70_02_PREV_TLC_MODE);
#endif
}

