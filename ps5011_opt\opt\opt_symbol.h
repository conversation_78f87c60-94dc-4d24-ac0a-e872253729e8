#ifndef OPT_SYMBOL_H_
#define OPT_SYMBOL_H_

/* CUSTOMER_CODE CATEGORY_CUSTOMER */
#define CUSTOMER_MAINSTREAM				(11)
#define CUSTOMER_MICRON					(21)
#define CUSTOMER_HYNIX					(31)
#define CUSTOMER_SANDISK                (32)
#define CUSTOMER_YMTC                   (33)
#define CUSTOMER_SAMSUNG                (34)

// Firmware Flash Category CATEGORY_FLASH
#define FLASH_B47R_TLC				    (19)
#define FLASH_N48R_QLC				    (20)   //zerio n48r add
#define FLASH_HYNIX_V6_TLC				(21)
#define FLASH_HYNIX_V7_TLC				(22)
#define FLASH_SANDISK_BICS5_TLC			(23)
#define FLASH_SANDISK_BICS6_TLC         (24)
#define FLASH_YMTC_TAS_TLC              (25)
#define FLASH_HYNIX_V7_QLC	 	        (26)	//Reip Porting 3D-V7 QLC Add
#define FLASH_YMTC_EMS_QLC				(27)    //ems add--karl
#define FLASH_HYNIX_V8_TLC				(28)    //Jeffrey Porting 3D-V8 TLC Add
#define FLASH_SANDISK_BICS8_TLC         (29)    //zerio BICS8 Add
#define FLASH_YMTC_WTS_TLC              (30)    //zerio wts add
#define FLASH_SAMSUNG_V6_TLC			(31)
#define FLASH_SAMSUNG_V6P_TLC			(32)
#define FLASH_SAMSUNG_V7_TLC			(33)	//Samsung v7 mst add--Reip
#define FLASH_SANDISK_BICS6_QLC         (34)    //zerio bics6 qlc add
#define FLASH_SAMSUNG_V8_TLC			(35)	//Samsung v8 mst add--Reip
#define FLASH_SAMSUNG_V5_TLC			(36)
#define FLASH_INTEL_N38A_QLC			(37)
#define FLASH_B37R_TLC				    (38)
#define FLASH_HYNIX_V9_TLC				(39)

// Firmware Controller Category CATEGORY_CONTROLLER
#define CONTROLLER_PS5013				(11)
#define CONTROLLER_PS5017				(13)
#define CONTROLLER_PS5021				(15)

#endif /* OPT_SYMBOL_H_ */

