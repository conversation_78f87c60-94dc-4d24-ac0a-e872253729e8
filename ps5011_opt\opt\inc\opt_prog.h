/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_prog.h                                                            */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _OPT_PROG_H_
#define _OPT_PROG_H_

#include "misc/types.h"
#include "opt_main.h"
#include "opt_arch.h"



#ifdef _OPT_PROG_C_
#define EXTERN
#else
#define EXTERN extern
#endif

EXTERN BOOL opt_macro_cmd_d2_normal_prog(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_d2_cache_prog_start(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_d2_cache_prog(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);
EXTERN BOOL opt_macro_cmd_d2_cache_prog_end(OPT_QUE_MGR_STRUCT_PTR que_mgr, OPT_JOB_STRUCT_PTR job);

#if (PS5021_EN || S17_EN)
#define M_RAIDECC_SET_TAG_SEL(TAG)		(R8_RS[R8_RAIDECC_TAG_SEL_1] = TAG)
#define M_RAIDECC_SET_TAG_VLD_CTL()		(R32_RS[R32_RAIDECC_TAG_VLD_CTL] |= RAIDECC_TAG_RD_1)
#define M_RAIDECC_WAIT_TAG_VLD()		{while((R32_RS[R32_RAIDECC_TAG_VLD_CTL] & (RAIDECC_TAG_RD_1_MASK  <<  RAIDECC_TAG_RD_1_SHIFT)));}
#define M_RAIDECC_GET_TAG_CONFIG()		(R32_RS[R32_RAIDECC_TAG_CONFIG_1])
#define M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(VALUE)
#define M_OPT_WAIT_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(VALUE)
#define M_OPT_CHECK_COMM_FW_CHECK_RAIDECC_REG_REQUEST_BY_RAIDECC(VALUE)			(FALSE)
#endif /*(PS5021_EN || S17_EN)*/

#define M_OPT_SET_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_FIP(VALUE)		(cpu_comm->ubAndesCheckRaidECCRegRequest = VALUE)
#define M_OPT_WAIT_COMM_ANDES_CHECK_RAIDECC_REG_REQUEST_BY_FIP(VALUE)		{while (VALUE != cpu_comm->ubAndesCheckRaidECCRegRequest);}
#define M_OPT_CHECK_COMM_FW_CHECK_RAIDECC_REG_REQUEST_BY_FIP(VALUE)			(VALUE == cpu_comm->ubFWCheckRaidECCRegRequest)

/*
 *
 *	Get Program FPU Offset
 *
 */

// for gulNTODTOngoing is FALSE

#define M_OPT_GET_FPU_PROGRAM_80_15(SLC) ((SLC) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15)) :\
													(FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_15)))
#define M_OPT_GET_FPU_PROGRAM_80_10(SLC) ((SLC) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog)) :\
													(FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10)))
#define M_OPT_GET_FPU_PROGRAM_80_11(SLC) ((SLC) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11)) :\
													(FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11)))

#if (COP0_BACKUP_P4K_WORKAROUND)
#define M_OPT_GET_FPU_PROGRAM_80_15_GC(SLC) ((SLC) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_15_gc)) :\
														(FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_15_gc)))
#define M_OPT_GET_FPU_PROGRAM_80_10_GC(SLC) ((SLC) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_slc_prog_gc)) :\
														(FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_10_gc)))
#define M_OPT_GET_FPU_PROGRAM_80_11_GC(SLC) ((SLC) ? (FPU_OFFSET(gFpuEntryList.fpu_entry_prog_slc_80_11_gc)) :\
														(FPU_OFFSET(gFpuEntryList.fpu_entry_m3d_tlc_prog_11_gc)))
#else /* (COP0_BACKUP_P4K_WORKAROUND) */
#define M_OPT_GET_FPU_PROGRAM_80_15_GC(SLC) (M_OPT_GET_FPU_PROGRAM_80_15(SLC))
#define M_OPT_GET_FPU_PROGRAM_80_10_GC(SLC) (M_OPT_GET_FPU_PROGRAM_80_10(SLC))
#define M_OPT_GET_FPU_PROGRAM_80_11_GC(SLC) (M_OPT_GET_FPU_PROGRAM_80_11(SLC))
#endif /* (COP0_BACKUP_P4K_WORKAROUND) */

#if (PS5021_EN || S17_EN)
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
#define M_FIP_FPU_BYPASS_WDMA	(FPU_OFFSET(gFpuEntryList.fpu_entry_test_C85_A5_DW))
#else
#define M_FIP_FPU_BYPASS_WDMA	(FPU_OFFSET(gFpuEntryList.fpu_entry_prog_bypass_WDMA))
#endif
#endif /* (PS5021_EN || S17_EN) */

#undef EXTERN
#endif /* _OPT_PROG_H_ */
