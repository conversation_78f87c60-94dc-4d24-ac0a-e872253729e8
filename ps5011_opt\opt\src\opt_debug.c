/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2016, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  opt_debug.c                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#define OPT_DEBUG_C_

#include "opt_debug.h"
#include "opt_api.h"

#if 1 //OPT_SUPPORT_DCCM_DEBUG
void opt_dccm_log_debug(U32 log)
{
	//opt_dccm_path_debug(0xDDDD);
	dccm_log_value = (U32 *)_dccm_debug_get_base(g_idx);
	*dccm_log_value = log;
	g_idx += 1;
	if (g_idx >= OPT_D_DEBUG_LENGTH) {
		g_idx = 0;
	}
	dccm_log_value = (U32 *)_dccm_debug_get_base(g_idx);
	*dccm_log_value = 0x99999999;
	//opt_dccm_path_debug(0xDDDD);
}
#endif
